<template>
  <div class="product-action-bar">
    <Skeleton class="product-action-bar--skeleton" :loading="loading">
      <template #skeleton>
        <SkeletonRow margin-top="0" height="0.5rem">
          <SkeletonCol width="60%" />
          <SkeletonCol width="40%" style="background: rgb(251, 229, 216)" />
        </SkeletonRow>
      </template>
      <template #content>
        <GoodsAction>
          <GoodsActionIcon text="客服" @click="onActionClick('service')">
            <van-icon name="chat-o" />
          </GoodsActionIcon>
          <GoodsActionButton text="立即购课" @click="onActionClick('buy')" />
        </GoodsAction>
      </template>
    </Skeleton>
  </div>
</template>

<script setup>
  import { Skeleton, SkeletonRow, SkeletonCol } from '@/components/skeleton'
  import { GoodsAction, GoodsActionButton, GoodsActionIcon } from '@/components/goods-action'

  defineProps({
    loading: Boolean,
  })

  const emit = defineEmits(['actionClick'])

  function onActionClick(name) {
    emit('actionClick', name)
  }
</script>

<style scoped lang="scss">
  .product-action-bar {
    position: fixed;
    bottom: 0;
    width: 3.75rem;
    height: 0.5rem;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    box-sizing: content-box;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: #fff;
    box-shadow: 0px -1px 0.04rem 0x rgba(0, 0, 0, 0.06);
  }

  .product-action-bar--skeleton {
    width: 100%;
    height: 100%;
  }
</style>
