import { ref, defineComponent } from 'vue'

const props = {
  modelValue: [String, Number],
  label: String,
  placeholder: String,
  disable: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  isLink: {
    type: Boolean,
    default: false,
  },
  title: String, // 弹窗标题
  options: {
    type: Array,
    default: () => [],
  },
  teleport: {
    type: [String, Element],
    default: '',
  },
  border: {
    type: Boolean,
    default: true,
  },
}

export default defineComponent({
  name: 'ijl-select',
  props: props,
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const inputVal = ref('')
    const showPicker = ref(false)
    let defaultIndex = 0
    const getDefaultIndex = () => {
      return props.options.findIndex((item) => {
        return item.id === props.modelValue
      })
    }

    const onConfirm = (value) => {
      inputVal.value = value.text
      emit('update:modelValue', value.id)
      emit('change', value.id, value)
      showPicker.value = false
    }

    const openPopup = () => {
      if (props.disable) return
      showPicker.value = true
    }

    return () => {
      if (props.modelValue !== '') {
        defaultIndex = getDefaultIndex()

        if (defaultIndex > -1) {
          inputVal.value = props.options[defaultIndex].text
        }
      }

      return (
        <div class="i-select">
          <van-field
            v-model={inputVal.value}
            disable={props.disable}
            required={props.required}
            is-link={props.isLink}
            border={props.border}
            readonly
            label={props.label}
            onClick={openPopup}
            placeholder={props.placeholder}
          />
          <van-popup
            v-model:show={showPicker.value}
            round
            position="bottom"
            teleport={props.teleport}
          >
            <van-picker
              title={props.title}
              columns={props.options}
              default-index={defaultIndex}
              onCancel={() => (showPicker.value = false)}
              onConfirm={onConfirm}
            />
          </van-popup>
        </div>
      )
    }
  },
})
