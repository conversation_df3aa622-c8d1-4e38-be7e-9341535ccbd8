
.action-bar {
  position: fixed;
  bottom: 0;
  width: 3.75rem;
  height: 0.6rem;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  box-sizing: content-box;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: #fff;
  box-shadow: 0px -1px 0.04rem 0x rgba(0,0,0,0.06);
}

.action-bar-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  font-size: .12rem;
  min-width: 0.8rem;
  background-color: #fff;
  user-select: none;

  &:active {
    background-color: #f2f3f5;
  }

  .icon {}
}

.action-bar-button {
  flex: 1;
  height: 0.45rem;
  margin: 0 0.1rem;
  background: #FF8C00;
  border-radius: 0.25rem;
  opacity: 0.8;
  color: #fff;
}