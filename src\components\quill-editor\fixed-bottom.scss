.editor-wrap {
  .fixed-toolbar {
    width: 3.75rem;
    position: fixed;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
  }

  #quill-toolbar {
    width: 100%;
    background-color: #fff;
    border-bottom: none;
  }

  .toolbar-left {
    button {
      float: left !important;
    }
  }

  :deep(.ql-editor) {
    padding: 12px 0;
    font-size: 0.15rem;
  }

  :deep(.ql-blank) {
    &::before {
      //color: rgba(0, 0, 0, 0.6);
      color: #b2b1b7;
      content: attr(data-placeholder);
      font-style: initial;
      left: 0;
      pointer-events: none;
      position: absolute;
      right: 15px;
    }
  }

  // 文字大小
  :deep(.ql-size .ql-picker-options) {
    top: -189px !important;
  }

  // 文字颜色
  :deep(.ql-color .ql-picker-options) {
    top: -112px !important;
  }

  // 文字背景颜色
  :deep(.ql-background .ql-picker-options) {
    top: -110px !important;
  }

  // 标题
  :deep(.ql-header .ql-picker-options) {
    top: -132px !important;
  }

  // 内容排版
  :deep(.ql-align .ql-picker-options) {
    top: -138px !important;

    .ql-picker-item {
      width: 30px;
      height: 28px;
    }
  }

  :deep(.ql-picker) {
    float: right;
  }

  :deep(.ql-toolbar button) {
    width: 32px;
    height: 28px;
    float: right;
  }

  :deep(.ql-color-picker) {
    width: 32px;
    height: 28px;
  }

  :deep(.ql-icon-picker) {
    width: 32px;
    height: 28px;
  }

  :deep(.editor) {
    border: none;
  }

  $fontSizeArr: 12, 14, 16, 20, 24, 36;
  @each $item in $fontSizeArr {
    :deep(.ql-picker-item[data-value="#{$item}px"]) {
      &::before {
        content: "#{$item}px";
      }
    }

    :deep(.ql-picker-label[data-value="#{$item}px"]) {
      &::before {
        content: "#{$item}px";
      }
    }
  }
}
