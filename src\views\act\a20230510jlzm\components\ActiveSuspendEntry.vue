<template>
  <div v-if="show" class="ActiveSuspendEntry">
    <van-icon name="clear" class="close" @click="close" />
    <img
      @click="$router.push({ name: 'a20230510jlzm' })"
      src="../images/active-suspend-entry.png"
      alt=""
    />
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { useRoute } from 'vue-router'

  const show = ref(true)
  const route = useRoute()

  const isManualLock = ref(false)

  const blacklist = [
    '/act/a20230510jlzm',
    '/act/a20230510jlzm/enter-for',
    '/act/a20230510jlzm/collect-gift-bag',
  ]

  const close = () => {
    isManualLock.value = true
    show.value = false
  }

  const updateShow = (to) => {
    if (blacklist.includes(to.path)) {
      show.value = false
    } else {
      show.value = !isManualLock.value
    }
  }

  watch(route, updateShow, { immediate: true })
</script>

<style lang="scss" scoped>
  .ActiveSuspendEntry {
    position: fixed;
    bottom: 1.21rem;
    right: 0.1rem;
    width: 0.8rem;
    height: 0.8rem;
    z-index: 199;

    img {
      width: 100%;
      height: 100%;
    }

    .close {
      font-size: 0.15rem;
      color: #b9b4b4;
      float: right;
    }
  }
</style>
