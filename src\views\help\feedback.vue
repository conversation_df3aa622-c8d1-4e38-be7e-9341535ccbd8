<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="page-content page-bg-white container">
        <div class="header">
          <div class="title">找不到想要的？</div>
          <div class="desc">请说出你的需求，我们将用心倾听并为你解决</div>
        </div>

        <div class="form-box">
          <ijl-select
            label="需求类型"
            :border="false"
            placeholder="请选择需求类型"
            is-link
            required
            v-model="formData.opinionType"
            :options="feedbackOption"
          />

          <van-field
            class="textarea"
            :border="false"
            v-model="formData.description"
            required
            rows="2"
            autosize
            label="需求描述"
            type="textarea"
            maxlength="300"
            placeholder="请详细描述你的需求"
            show-word-limit
          />

          <div class="upload-img">
            <uploader
              v-model="formData.pictures"
              class="uploader"
              not-login
              preview-size="0.72rem"
              file-type="jpg|png|gif|jpeg"
              :max-size="10240 * 1024"
              :max-count="9"
            />
            <div class="f10 uploader-tip">最多9张，&lt;10M/张，jpg/jpeg/png/gif格式</div>
          </div>

          <van-field
            v-model="formData.contactMobile"
            :border="false"
            required
            label="联系方式"
            placeholder="请输入手机号码"
          />
        </div>

        <div class="fixed-button">
          <button class="button" v-preventReClick @click="submit">提交</button>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import IjlSelect from '@/components/form/i-select'
  import Uploader from '@/components/upload-file'
  import Schema from 'async-validator'
  import { Toast, Dialog } from 'vant'
  import { validateMobile, validateStrIsEmpty } from '@/utils/validators'
  import { reqUserFeedback } from '@/api/generic-server'
  import { isLogin } from '@/common'
  import { localProxyStorage } from '@/utils/storage'

  const router = useRouter()

  const formData = reactive({
    opinionType: '1',
    description: '',
    contactMobile: '',
    pictures: [],
  })

  let formValidator = new Schema({
    opinionType: { required: true, message: '请选择需求类型' },
    description: { message: '请输入需求描述内容', validator: validateStrIsEmpty },
    contactMobile: { validator: validateMobile },
  })

  const feedbackOption = [
    { text: '找教练', id: '1' },
    { text: '找场馆', id: '2' },
    { text: '意见反馈', id: '3' },
    { text: '其他', id: '4' },
  ]

  const submit = () => {
    formValidator
      .validate(formData)
      .then(() => {
        let loading = Toast.loading({ forbidClick: true })
        let params = JSON.parse(JSON.stringify(formData))
        params.pictures = params.pictures.map((item) => item.path)

        if (isLogin()) {
          params.userId = localProxyStorage.user.userId
        }

        reqUserFeedback(params)
          .then(() => {
            loading?.clear()
            Dialog.alert({
              message: '发布需求成功，我们将尽快联系到您',
              confirmButtonText: '我知道了',
            }).then(() => {
              router.push('/')
            })
          })
          .catch(() => {
            loading?.clear()
          })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/fixed-button.scss';

  .container {
    padding-bottom: 1rem;
  }

  .header {
    padding: 0.15rem 0.15rem 0.1rem 0.15rem;

    .title {
      font-size: 0.2rem;
      color: #453938;
      font-weight: bold;
    }

    .desc {
      font-size: 0.12rem;
      margin-top: 0.06rem;
      color: #616568;
    }
  }

  .form-box {
    padding: 0 0.16rem;

    :deep(.van-field) {
      font-size: 0.14rem;
      padding: 0.16rem 0;
      border-bottom: 1px solid #eeeeee;
    }

    .textarea {
      flex-direction: column;
      padding-bottom: 0.08rem !important;
      border: none !important;

      :deep(.van-field__value) {
        padding: 0.12rem;
        border: 1px solid #e8e8e8;
        margin-top: 0.05rem;
      }

      :deep(.van-field__word-limit) {
        color: #b2b1b7;
      }
    }

    .upload-img {
      .uploader {
        :deep(.van-uploader__upload) {
          border-radius: 0.02rem;
          border: 1px solid #e8e8e8;
        }
        :deep(.van-uploader__preview-image) {
          border-radius: 0.02rem;
        }
      }

      .uploader-tip {
        transform-origin: left;
        color: rgb(167, 167, 167);
      }
    }
  }
</style>
