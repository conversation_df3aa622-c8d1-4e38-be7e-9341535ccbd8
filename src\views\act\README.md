## 专题目录命名
a + 日期 + 专题英文或拼音 优先使用有意义的英文名，如使用拼音，拼音字符超过10个，请使用首个拼音的全称+后面拼音的简写

## 专题文件目录
- 图片文件夹 images
- 组件文件夹 components
- 首页命名 index.vue
- 其他页面 xxx.vue

## 注意
- 为了后期维护可以定期删除过期的专题活动页，请按此规范进行开发
- 活动页面的资源文件，不要互相引用 例如：B 活动依赖 A 活动的组件，图片、字体资源，导致后面清理过期活动代码耦合程度太高，难于清理,
不能安全直接 delete 整个活动页面文件夹，如果需要依赖其他活动的，那直接 copy 过去即可

## 活动记录

| 活动名称     | 页面路径               | 活动是否结束 | 是否删除代码 |
|----------|--------------------|--------|--------|
| 一期明星教练招募 | /act/a20230321jlzm | 已结束    | 否      |
| 二期明星教练招募 | /act/a20230510jlzm | 已结束    | 否      |