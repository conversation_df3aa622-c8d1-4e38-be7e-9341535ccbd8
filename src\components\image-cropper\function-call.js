import <PERSON>ropper from './cropper'
import { mountComponent, usePopupState } from '../utils/mount-component'

let instance = null

function initInstance() {
  const Wrapper = {
    setup() {
      const { state, toggle } = usePopupState()
      return () => <Cropper {...state} onUpdate:show={toggle} />
    },
  }

  ;({ instance } = mountComponent(Wrapper))
}

export function ImageCropper(options) {
  return new Promise((resolve, reject) => {
    if (!instance) {
      initInstance()
    }

    let props = Object.assign({}, instance.defaultOptions, options, {
      callback: (action, params) => {
        ;(action === 'confirm' ? resolve : reject)(params)
      },
    })

    instance.open(props)
  })
}

ImageCropper.defaultOptions = {
  image: null,
}

ImageCropper.close = () => {
  if (instance) {
    instance.toggle(false)
  }
}
