<template>
  <van-popup @open="open" style="background: transparent">
    <div class="main">
      <div class="icon-close" @click="close" />

      <div class="poster" ref="posterWrapper" v-if="coach">
        <div class="user-info">
          <div class="user-avatar" :style="avatarStyle" />
          <div class="username">来自{{ userInfo.realName || userInfo.userName }}的推荐</div>
        </div>

        <div class="bubble"></div>

        <div class="coach-box">
          <div class="cover" :style="posterStyle"></div>
          <div class="coach-info">
            <div class="coach-name">{{ coach.realName }}</div>
            <div class="coach-title">「{{ ellipsis(coach.teachTitle) }}」</div>
            <div class="coach-desc">{{ ellipsis(coach.teachDescription) }}</div>
          </div>
        </div>
        <div class="footer">
          <div class="qr-code-tip">
            <p>想了解教练更多信息</p>
            <p>长按识别二维码>></p>
          </div>
          <div class="qrcode-wrapper">
            <div class="qr-code" ref="qrCodeWrapper"></div>
          </div>
        </div>
      </div>

      <div class="poster-img" v-if="posterURL || posterDataURL">
        <img v-if="isWeChatEnv" :src="posterDataURL" alt="海报" />
        <img v-else :src="posterURL" alt="海报" />
        <div class="text">- 长按上方图片保存并分享 -</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
  import { ref, computed, nextTick, onBeforeUnmount } from 'vue'
  import { Toast } from 'vant'
  import { baseURL, ossTempURL } from '@/config'
  import QRCode from 'qrcodejs2'
  import html2canvas from 'html2canvas'
  import { getCoachDetails } from '@/api/coach-server'
  import { getUserInfo } from '@/api/user-server'
  import { uploadFile } from '@/api/generic-server'
  import { localProxyStorage } from '@/utils/storage'
  import { getOssURL, dataURLtoFile } from '@/common'
  import { getBase64Image, sliceStr } from '@/utils'
  import { isWeChat } from '@/utils'

  const coach = ref(null)
  let loading, timer

  let props = defineProps({
    coachId: [String, Number],
  })

  const userInfo = ref({})

  const emit = defineEmits(['update:show'])

  onBeforeUnmount(() => {
    timer && clearInterval(timer)
    loading && loading.clear()
  })

  const ellipsis = (str) => {
    if (!str) return ''
    if (str.length > 18) {
      return sliceStr(str, 0, 18) + '...'
    } else {
      return str
    }
  }

  // 二维码是否生成完成
  const isQrCodeGen = ref(false)
  const qrCodeWrapper = ref(null)
  const posterWrapper = ref(null)
  const posterURL = ref(null)
  const coverDataURL = ref(null) // 海报封面 base64
  const posterDataURL = ref(null)
  const userAvatarDataURL = ref(null)
  const isWeChatEnv = ref(isWeChat())
  // const ossImgSize = "?x-oss-process=image/interlace,1/format,jpg/quality,q_30";
  const ossImgSize = '?x-oss-process=image/resize,m_fill,h_480,w_480'

  const posterStyle = computed(() => {
    return {
      background: coverDataURL.value ? `url("${coverDataURL.value}") no-repeat` : 'transparent',
      'background-size': 'cover',
      'background-position': 'center',
    }
  })

  const avatarStyle = computed(() => {
    return {
      background: userAvatarDataURL.value
        ? `url("${userAvatarDataURL.value}") no-repeat`
        : 'transparent',
      'background-size': 'cover',
      'background-position': 'center',
    }
  })

  // 等待图片加载完成
  const waitImgLoad = (cb) => {
    let count = 0
    const timeout = 20000 // 20秒

    timer = setInterval(() => {
      count += 50
      if (isQrCodeGen.value && coverDataURL.value && userAvatarDataURL.value) {
        clearInterval(timer)
        timer = null
        cb()
        return
      }

      if (count > timeout) {
        clearInterval(timer)
        timer = null
        loading && loading.clear()
        close()
        Toast('海报加载失败,请稍后再试')
      }
    }, 50)
  }

  // 生成二维码
  const genQrCode = () => {
    const link = `${baseURL}/coach/details/${props.coachId}?shareId=${localProxyStorage.user?.shareId}`

    nextTick(() => {
      new QRCode(qrCodeWrapper.value, {
        width: 128,
        height: 128,
        text: link,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.L,
      })
      isQrCodeGen.value = true
    })
  }

  const genPoster = () => {
    return new Promise((resolve, reject) => {
      waitImgLoad(() => {
        let elem = posterWrapper.value
        let devicePixelRatio = window.devicePixelRatio
        // 用getBoundingClientRect 是为了解决白边问题
        let width = elem.getBoundingClientRect().width
        let height = elem.getBoundingClientRect().height

        let canvas = document.createElement('canvas')
        canvas.width = width
        canvas.height = height

        html2canvas(elem, {
          canvas: canvas,
          width: width,
          height: height,
          scale: 1,
          dpi: devicePixelRatio,
          useCORS: true,
        })
          .then((canvas) => {
            resolve(canvas.toDataURL())
          })
          .catch((error) => {
            reject(error)
          })
      })
    })
  }

  const close = () => {
    emit('update:show', false)
  }

  // 加载用户头像
  const loadUserAvatar = (url, isReload = true) => {
    const size = '?x-oss-process=image/resize,m_fill,h_120,w_120'
    let imageURL = url
      ? url
      : getOssURL(userInfo.value.headImg) + size + '&v=' + new Date().getTime()

    getBase64Image(imageURL).then((imgDataURL) => {
      if (!imgDataURL && isReload) {
        loadUserAvatar(getOssURL('user/default_man_3x.png'), false)
      }
      userAvatarDataURL.value = imgDataURL
    })
  }

  const loadCoachCover = (url, isReload = true) => {
    const query = ossImgSize + '&v=' + new Date().getTime()
    const imageURL = url ? url : getOssURL(coach.value.coachImagesArray?.[0]) + query

    getBase64Image(imageURL).then((imgDataURL) => {
      if (!imgDataURL && isReload) {
        loadCoachCover(require('../../assets/images/default-img.png'), false)
      }
      coverDataURL.value = imgDataURL
    })
  }

  const initPoster = () => {
    getUserInfo().then((res) => {
      userInfo.value = res.data
      // 加载用户头像
      loadUserAvatar()
      // 加载教练封面
      loadCoachCover()
      // 生成分享二维码
      genQrCode()

      // 生成海报
      genPoster().then((dataURL) => {
        if (isWeChatEnv.value) {
          posterDataURL.value = dataURL
          loading && loading.clear()
        } else {
          // TODO: 主要是为了兼容领导手机 ！兼容百度app, 长按识别不了base64以及文件流的图片, 先上传到服务器临时文件区
          let formData = new FormData()
          formData.append('file', dataURLtoFile(dataURL, 'posters.png'))

          uploadFile(formData).then((res) => {
            posterURL.value = ossTempURL + '/' + res.data
            loading && loading.clear()
          })
        }
      })
    })
  }

  const open = () => {
    if (posterURL.value || posterDataURL.value) return
    loading = Toast.loading({
      message: '生成中...',
      duration: 0,
      forbidClick: true,
    })

    getCoachDetails({ coachId: props.coachId }).then((res) => {
      let { data } = res
      coach.value = data
      initPoster()
    })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('close', 0.6rem, 0.6rem) {
    position: absolute;
    top: 0.2rem;
    right: 0;
    z-index: 3000;
  }

  .main {
    width: 3.75rem;
    padding-top: 0.8rem;
    min-height: 100vh;
  }

  .coach-poster {
    position: relative;
    background: #666666;
    padding-top: 0.8rem;
    overflow: hidden;
  }

  .poster-img {
    width: 3.25rem;
    margin: 0 auto;
    text-align: center;

    img {
      width: 100%;
      height: 100%;
    }

    .text {
      margin-top: 0.16rem;
      font-size: 0.14rem;
      color: #ffffff;
    }
  }

  .poster {
    width: 750px;
    height: 1128px;
    position: absolute;
    left: -500%;
    top: -500%;
    background: url('@/assets/images/coach/share-bg.png') no-repeat;
    background-size: 100% 100%;
    z-index: 1;

    .user-info {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 16px 30px;

      .user-avatar {
        width: 72px;
        height: 72px;
        border-radius: 50%;
        border: 2px solid #ffffff;
      }

      .username {
        flex: 1;
        margin-left: 16px;
        font-weight: 600;
        font-size: 30px;
        color: #270f09;
      }
    }

    .bubble {
      width: 576px;
      height: 158px;
      position: absolute;
      z-index: 3;
      top: 126px;
      left: 26px;
      background: url('@/assets/images/coach/share-title.png') no-repeat;
      background-size: 100% 100%;
    }

    .coach-box {
      width: 100%;
      position: absolute;
      top: 254px;
      z-index: 2;

      .cover {
        width: 460px;
        height: 460px;
        margin: 0 auto;
      }
    }

    .coach-info {
      width: 460px;
      margin: 16px auto 0 auto;

      .coach-name {
        font-size: 32px;
        font-weight: 600;
        color: #82340e;
      }

      .coach-title {
        font-size: 28px;
        font-weight: 500;
        color: #82340e;
        margin-top: 8px;
      }

      .coach-desc {
        margin-top: 8px;
        font-size: 28px;
        color: #616568;
      }
    }

    .footer {
      position: absolute;
      width: 100%;
      padding: 0 102px;
      bottom: 60px;
      display: flex;
      justify-content: space-between;

      .qr-code-tip {
        font-size: 26px;
        color: #b2b1b7;
        padding-top: 26px;

        p + p {
          margin-top: 8px;
        }
      }

      .qrcode-wrapper {
        img {
          width: 100%;
          height: 100%;
        }

        .qr-code {
          background: #ffffff;
          border: 2px solid #d8d8d8;
          padding: 4px;
        }
      }
    }
  }
</style>
