<template>
  <div class="account-disabled">
    <img class="empty-icon" src="../../assets/images/empty.png" alt="你还未发布过内容哦～" />
    <p class="empty-desc">哦噢～您的教练账号已被停用，教练信息同步下架</p>
    <button class="i-button empty-button" @click="open">联系客服</button>
  </div>
</template>

<script setup>
  import gm from '@/components/gm-popup'

  const open = () => {
    gm.open({
      title: '联系客服',
      desc: '添加企业微信，在线联系客服',
    })
  }
</script>

<style lang="scss" scoped>
  .account-disabled {
    background-color: #fff;
    min-height: 100vh;
    text-align: center;

    .empty-icon {
      width: 0.98rem;
      height: 1rem;
      margin-top: 1.77rem;
    }

    .empty-desc {
      font-size: 0.12rem;
      color: #b2b1b7;
      margin-top: 0.1rem;
      user-select: none;
      padding: 0 0.88rem;
    }

    .empty-button {
      width: 1.8rem;
      height: 0.4rem;
      margin-top: 0.53rem;
      font-size: 0.16rem;
      color: #fff;
      font-weight: 600;
      background: #ff9b26;
      box-shadow: 0 2px 4px 1px rgba(245, 176, 76, 0.1);
      border-radius: 0.24rem;
    }
  }
</style>
