<template>
  <van-popup class="popup" round position="bottom">
    <van-picker
      class="picker"
      :columns="columns"
      @confirm="confirm"
      @cancel="$emit('update:show', false)"
    />
  </van-popup>
</template>

<script setup>
  import { ref, watch } from 'vue'

  let props = defineProps({
    modelValue: Array,
  })

  const emit = defineEmits(['confirm'])

  const startTimeIndex = ref(9)

  const endTimeIndex = ref(21)

  const confirm = (value) => {
    emit('confirm', [value[0], value[2]])
  }

  const genTiems = () => {
    let arr = []
    for (let i = 0; i <= 24; i++) {
      arr.push(`${i <= 9 ? '0' + i : i}:00`)
    }
    return arr
  }

  let times = genTiems()

  const columns = [
    // 第一列
    {
      values: times,
      defaultIndex: startTimeIndex,
    },
    // 第二列
    {
      values: ['-'],
      defaultIndex: 0,
    },
    {
      values: times,
      defaultIndex: endTimeIndex,
    },
  ]

  watch(
    () => props.modelValue,
    (newVal) => {
      if (Array.isArray(newVal) && newVal.length >= 1) {
        startTimeIndex.value = times.indexOf(newVal[0]) > -1 ? times.indexOf(newVal[0]) : 9
        endTimeIndex.value = times.indexOf(newVal[1]) > -1 ? times.indexOf(newVal[1]) : 21
      }
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped>
  .picker {
    :deep(.van-picker__confirm) {
      font-size: 0.15rem;
      color: #f06e6c;
    }

    :deep(.van-picker__cancel) {
      font-size: 0.15rem;
    }
  }
</style>
