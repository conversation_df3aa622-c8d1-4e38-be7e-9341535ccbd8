<template>
  <div class="ijl-image-cropper">
    <transition>
      <div v-if="show" class="ijl-image-cropper__content">
        <div class="image-container">
          <img ref="imageRef" :src="imageUrl" alt="" />
        </div>

        <div class="ijl-image-cropper__footer">
          <button class="cancel-cropper" @click="onCancel">取消</button>
          <button class="confirm-cropper" @click="onConfirm">确定</button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
  import { ref, watchEffect, nextTick, computed } from 'vue'
  import Cropper from 'cropperjs'
  import 'cropperjs/dist/cropper.css'

  const props = defineProps({
    show: { type: Boolean, default: false },
    image: null,
    callback: Function,
  })

  const emit = defineEmits(['update:show', 'confirm'])

  let cropper = null

  const imageRef = ref(null)

  const imageUrl = computed(() => {
    if (props.image instanceof File) return URL.createObjectURL(props.image)
    if (typeof props.image === 'string') return props.image
    return ''
  })

  const imageFileType = computed(() => {
    let fileType = 'jpeg'
    let blacklist = ['webp', 'gif']

    if (props.image instanceof File) {
      fileType = props.image.name.substring(props.image.name.lastIndexOf('.') + 1).toLowerCase()
    }

    if (typeof props.image === 'string') {
      let url = props.image.split('?')[0]
      fileType = url.substring(url.lastIndexOf('.') + 1).toLowerCase()
    }

    if (blacklist.includes(fileType)) {
      fileType = 'jpeg'
    }

    return fileType
  })

  function initCropper() {
    // 注意裁剪器的大小继承自图像的父元素（包装器）的大小，因此请确保使用可见的块元素包装图像。
    cropper = new Cropper(imageRef.value, {
      //裁剪框不能超出图片的范围,
      viewMode: 1,
      //拖拽图片模式 move 图片可移动
      dragMode: 'move',
      // 定义裁剪框的初始纵横比。默认情况下，它与画布(图像包装器)的长宽比相同。
      initialAspectRatio: NaN,
      //是否显示裁剪框的虚线
      guides: false,
      //是否显示裁剪框中间的 ‘+’ 指示器 默认true
      center: false,
      // 定义裁剪框的固定长宽比。默认情况下，裁剪框有一个自由比例。
      aspectRatio: 1,
      //是否在容器内显示网格状的背景 默认true
      background: false,
      //是否可以拖拽裁剪框 默认true
      cropBoxMovable: false,
      // 是否可以改变裁剪框的尺寸, 默认true
      cropBoxResizable: false,
      // 在初始化时自动裁剪图像
      autoCrop: true,
      // 0 到 1 之间的数字, 定义自动裁剪区域大小(百分比), 默认 0.8 表示 80%的区域
      autoCropArea: 0.95,
      //鼠标滑轮是否可以放大缩小图片
      zoomOnWheel: true,
      // 容器的最小高度，默认100
      minContainerHeight: window.innerHeight,
      // 容器的最小宽度，默认200
      minContainerWidth: 100,
      // 初始化完成
      ready() {},
    })
  }

  const updateShow = (value) => emit('update:show', value)

  const setLockScroll = (state) => {
    const bodyLockClass = 'ijl-overflow-hidden'
    let action = state ? 'add' : 'remove'
    document.body.classList[action](bodyLockClass)
  }

  const onConfirm = () => {
    let dataURL = cropper.getCroppedCanvas().toDataURL(`image/${imageFileType.value}`)
    props.callback && props.callback('confirm', dataURL)
    updateShow(false)
    setLockScroll(false)
  }

  const onCancel = () => {
    props.callback && props.callback('cancel')
    updateShow(false)
    setLockScroll(false)
    cropper?.destroy()
  }

  const showImageCropper = async () => {
    if (props.show) {
      setLockScroll(true)
      await nextTick()
      initCropper()
    }
  }

  watchEffect(showImageCropper)

  defineExpose({ open })
</script>

<style lang="scss" scoped>
  .ijl-image-cropper {
    position: fixed;
    left: var(--window-left);
    right: var(--window-right);
    top: 0;
    z-index: 2000;
  }

  .image-container {
    width: 100%;
    height: 100vh;
    background: black;

    img {
      display: block;
      /* 这条样式非常重要，请不要忽视这一点 */
      max-width: 100%;
      opacity: 0;
    }
  }

  .ijl-image-cropper__footer {
    position: fixed;
    left: var(--window-left);
    right: var(--window-right);
    bottom: 0.2rem;
    z-index: 101;
    padding: 0 0.5rem;
    display: flex;
    justify-content: space-between;

    .cancel-cropper {
      width: 1rem;
      height: 0.4rem;
      line-height: 0.4rem;
      border-radius: 0.02rem;
      color: rgb(59, 65, 82);
      background-color: rgb(255, 255, 255);
    }

    .confirm-cropper {
      width: 1rem;
      height: 0.4rem;
      line-height: 0.4rem;
      border-radius: 0.02rem;
      color: rgb(255, 255, 255);
      background-color: var(--i-primary);
    }
  }
</style>
