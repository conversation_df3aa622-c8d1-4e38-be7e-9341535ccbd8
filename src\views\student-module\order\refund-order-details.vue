<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div v-if="orderDetail" class="container min-height-100">
        <!-- 退款详情页 -->
        <div class="order-state-container">
          <p class="order-state">
            <span class="label">退款状态：</span>
            <span class="value">{{ orderStateFilter(orderDetail?.afterSaleStatus?.status) }}</span>
          </p>

          <steps :active="stepActive.step" :status="stepActive.status">
            <step>提交申请</step>
            <step>教练审核</step>
            <step>平台审核</step>
            <step>完成</step>
          </steps>

          <div v-if="stepActive.step" class="order-state-content">
            <!--            <div class="icon-bulb"></div>-->
            <div class="text">
              <template v-if="stepActive.step === 1">
                <p v-if="stepActive.status === 'progress'">
                  教练审核中，预计
                  <van-count-down
                    class="count-down"
                    :time="countDown(orderDetail.auditDeadTime)"
                    format="HH时mm分"
                  />
                  内为你处理。
                </p>
                <p v-else>教练已拒绝您的退款申请，拒绝原因：{{ orderDetail.refuseExplain }}</p>
              </template>
              <template v-if="stepActive.step === 2">
                <p v-if="stepActive.status === 'progress'">
                  平台审核中, 预计
                  <van-count-down
                    class="count-down"
                    :time="countDown(orderDetail.platformAuditDeadTime)"
                    format="DD天HH时mm分"
                  />
                  内为你处理。
                </p>
                <p v-else>平台已拒绝您的退款申请，拒绝原因：{{ orderDetail.refuseExplain }}</p>
              </template>
              <template v-if="stepActive.step === 3">
                <p>您的钱款爱教练已退回到原账号，到账周期可查看退款信息。</p>
              </template>
            </div>
          </div>
          <p class="warn">遇到问题，<span @click="service">联系客服</span></p>

          <p v-if="false" class="order-tip">
            <!-- 待处理 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'APPLIED'">
              <van-count-down class="purchaser" :time="countDown(orderDetail.auditDeadTime)">
                <template #default="timeData">
                  还有
                  <span class="purchaser-item">
                    <span v-if="timeData.hours > 0" class="block">{{ timeData.hours }}时</span>
                    <span class="block">{{ timeData.minutes }}分</span>
                  </span>
                  自动同意退款
                </template>
              </van-count-down>
            </template>
            <!-- 退款成功 -->
            <template
              v-if="
                orderDetail.afterSaleStatus.status === 'AGREED' &&
                orderDetail.refundStatus === 'PROCESSING'
              "
              >退款待到账
            </template>
            <template
              v-if="
                orderDetail.afterSaleStatus.status === 'AGREED' &&
                orderDetail.refundStatus === 'SUCCESS'
              "
              >已退回到原账户：¥{{ orderDetail.refundAmount }}元
            </template>
            <template
              v-if="
                orderDetail.afterSaleStatus.status === 'AGREED' &&
                orderDetail.refundStatus === 'FAILURE'
              "
              >如一个工作日仍未到账，请联系平台客服
            </template>
            <!-- 退款关闭 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'CANCELED'">
              你已取消退款申请
            </template>
            <!-- 拒绝退款 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'REFUSED'">
              教练拒绝了你的退款申请，拒绝理由：{{ orderDetail.refuseExplain }}
            </template>
          </p>
        </div>

        <div class="refund-details">
          <div class="title">退款信息</div>
          <div class="goods-details">
            <van-image
              class="goods-images"
              round
              fit="cover"
              width="0.74rem"
              height="0.74rem"
              :src="getOssURL(orderDetail.orderItem.imageUrl)"
            />
            <div class="goods-info">
              <div>
                <van-row justify="space-between" align="center">
                  <van-col class="goods-name omit">
                    {{ orderDetail.orderItem.spuName }}｜{{ orderDetail.orderItem.skuName }}
                  </van-col>
                  <van-col class="buy-price">¥{{ orderDetail.orderItem.totalAmount }}</van-col>
                </van-row>
              </div>
              <div class="goods-spec">
                授课方式：{{ orderDetail.orderItem.teachingWay?.typeName }}
              </div>
              <div class="buy-number">课时数：{{ orderDetail.orderItem.quantity }}个课时</div>
            </div>
          </div>
          <div class="order-detail">
            <div class="cell">
              <div class="label">退款原因</div>
              <div class="value">{{ orderDetail.refundReason?.typeName }}</div>
            </div>
            <div class="cell">
              <div class="label">申请课时</div>
              <div class="value">{{ orderDetail.refundQuantity }}个课时</div>
            </div>
            <div class="cell">
              <div class="label">退款金额</div>
              <div class="value money">¥{{ orderDetail.refundAmount }}</div>
            </div>
            <div class="cell">
              <div class="label">退款描述</div>
              <div class="value">
                <span v-if="isDesc" class="black" @click="descShow = !descShow">
                  {{ descShow ? '收起' : '查看' }}
                  <van-icon class="arrow" :name="descShow ? 'arrow-down' : 'arrow'" />
                </span>
                <span v-else>无</span>
              </div>
            </div>
            <div v-show="descShow" class="refund-desc">
              <p>{{ orderDetail.refundExplain }}</p>
              <image-preview-wrapper>
                <div class="images">
                  <img
                    v-for="imgUrl in orderDetail.mediaUrls"
                    :key="imgUrl"
                    :src="getOssURL(imgUrl)"
                    alt=""
                  />
                </div>
              </image-preview-wrapper>
            </div>
            <div class="cell">
              <div class="label">申请时间</div>
              <div class="value">{{ orderDetail.applyTime }}</div>
            </div>
            <div class="cell">
              <div class="label">退款编号</div>
              <div class="value">
                <span>{{ orderDetail.id }} I </span>
                <copy-text class="copy" :text="orderDetail.id" @success="copySuccess">
                  复制
                </copy-text>
              </div>
            </div>
          </div>
          <div class="title">退款说明</div>
          <div class="refund-rule">
            <p>1、用户提交订单后，未上课（核销课时）申请退款可全额退回；</p>
            <p>
              2、已联系私教服务提供者并至少完成一节课时（核销一节课时），此时申请退款，爱教练平台将退回剩余未上课的课时对应的款项，已核销的课时对应的款项不予退回；
            </p>
          </div>
        </div>
        <BottomBtns
          v-if="isShowCancelBtn"
          isleftPlain
          isOnlyOne="left"
          leftBtnText="取消退款"
          @leftClick="cancelRefuse"
        />
        <BottomBtns
          v-if="orderStatus === 'CANCELED'"
          isleftPlain
          isOnlyOne="left"
          leftBtnText="重新申请"
          @leftClick="againApply"
        />
        <!--        <BottomBtns-->
        <!--          v-if="orderStatus === 'REFUSED'"-->
        <!--          isleftPlain-->
        <!--          isOnlyOne="left"-->
        <!--          leftBtnText="客服介入"-->
        <!--          @leftClick="service"-->
        <!--        />-->
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { Toast, Dialog } from 'vant'
  import { useRoute } from 'vue-router'
  import CopyText from '@/components/copy-text'
  import BottomBtns from '@/components/basic/bottom-btns.vue'
  import { getAfterSaleDetail, getStuAfterSaleCancel, checkCanApply } from '@/api/trade-server'
  import router from '@/router'
  import { getOssURL } from '@/common'
  import gm from '@/components/gm-popup'
  import { getDateTime } from '@/utils/day'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import { Step, Steps } from '@/components/steps'

  const route = useRoute()
  const descShow = ref(false)
  const orderId = route.query.orderId || ''
  const orderStatus = ref(null)
  const orderDetail = ref(null)

  const orderStateFilter = computed(() => {
    return (state) => {
      const obj = {
        APPLIED: '待教练处理',
        AGREED: '退款成功',
        REFUSED: '拒绝退款',
        CANCELED: '取消退款',
        REVIEWED: '退款中',
      }
      return obj[state]
    }
  })

  const copySuccess = () => {
    Toast('复制成功')
  }

  const isShowCancelBtn = computed(() => {
    if (!orderStatus.value) return false
    return (
      orderStatus.value === 'APPLIED' ||
      orderStatus.value === 'REFUSED' ||
      orderStatus.value === 'REVIEWED'
    )
  })

  const stepActive = computed(() => {
    const progress = {
      step: 0,
      status: 'finish',
    }

    if (!orderDetail.value) return progress

    let afterSaleStatus = orderDetail.value.afterSaleStatus

    // 取消退款
    if (afterSaleStatus.status === 'CANCELED') {
      progress.step = null
      progress.status = ''
    }

    // 被拒绝退款
    if (afterSaleStatus.status === 'REFUSED') {
      // 教练拒绝
      if (orderDetail.value.coachAuditStatus === 'REFUSED') {
        progress.step = 1
        progress.status = 'error'
      }
      // 平台拒绝
      if (orderDetail.value.platformAuditStatus === 'REFUSED') {
        progress.step = 2
        progress.status = 'error'
      }
    }

    // 教练审核中
    if (afterSaleStatus.status === 'APPLIED') {
      progress.step = 1
      progress.status = orderDetail.value.coachAuditStatus === 'REFUSED' ? 'error' : 'progress'
    }

    // 平台审核中
    if (afterSaleStatus.status === 'REVIEWED') {
      progress.step = 2
      progress.status = orderDetail.value.platformAuditStatus === 'REFUSED' ? 'error' : 'progress'
    }

    // 退款完成
    if (afterSaleStatus.status === 'AGREED') {
      progress.step = 3
      progress.status = 'finish'
    }

    return progress
  })

  // 是否有退款描述
  const isDesc = computed(() => {
    if (!orderDetail.value) return false
    return orderDetail.value.refundExplain || orderDetail.value.mediaUrls.length > 0
  })

  const init = async () => {
    await getAfterSaleDetail({ id: orderId }).then((res) => {
      const { data } = res
      orderDetail.value = data
      orderStatus.value = orderDetail.value.afterSaleStatus.status
    })
  }

  // 倒计时(到期时间 - 现在时间)
  const countDown = computed(() => {
    return (state) => {
      let downTime
      if (getDateTime(state) - getDateTime()) {
        downTime = getDateTime(state) - getDateTime()
      } else {
        downTime = 0
      }
      return downTime
    }
  })

  init()

  // 取消退款
  const cancelRefuse = () => {
    Dialog.confirm({
      message: '是否确认取消退款？',
    })
      .then(() => {
        getStuAfterSaleCancel({ id: orderDetail.value.id }).then(() => {
          Toast('取消退款成功')
          orderDetail.value = {}
          init()
        })
      })
      .catch(() => {})
  }

  // 重新申请
  const againApply = () => {
    const params = {
      orderId: orderDetail.value.orderId,
      orderItemId: orderDetail.value.orderItemId,
    }
    checkCanApply(params).then((res) => {
      if (res.data) {
        router.push({
          name: 'studentOrderApplyRefund',
          query: {
            orderId: orderDetail.value.orderId,
            orderItemId: orderDetail.value.orderItemId,
          },
        })
      } else {
        Toast(res.msg)
        router.push({
          name: 'studentOrder',
        })
      }
    })
  }
  // 客服介入
  const service = () => {
    gm.open({
      title: '联系客服',
      desc: '添加企业微信，在线联系客服',
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    background: #fff;
    padding-bottom: 0.8rem;
  }

  .order-state-container {
    padding: 0 0.15rem;
    border-bottom: 0.08rem solid #eeeeee;

    .order-state {
      padding: 0.12rem 0;
      line-height: 0.2rem;
    }

    .order-state-content {
      display: flex;
      align-items: center;
      color: #616568;
      background: #f7f7f7;
      border-radius: 0.04rem;
      padding: 0.06rem 0.15rem;

      .icon-bulb {
        width: 0.14rem;
        height: 0.14rem;
        background: url('../../../assets/images/icon/icon-bulb.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 0.12rem;
      }

      .text {
        flex: 1;
        p {
          word-break: break-all;
        }
      }
    }

    .warn {
      font-size: 0.12rem;
      padding: 0.12rem 0;
      color: #b2b1b7;

      span {
        color: #0382fc;
        cursor: pointer;
      }
    }

    .order-tip {
      display: flex;
      margin-top: 0.06rem;
      color: #616568;
    }

    .label {
      color: #616568;
    }

    .value {
      font-size: 0.18rem;
      color: #1a1b1d;
      font-weight: 600;
    }

    .time {
      font-size: 0.16rem;
      color: #ff6445;
    }
  }

  .refund-details {
    padding: 0 0.15rem;

    .title {
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
      padding: 0.11rem 0 0.08rem 0;
    }

    .goods-details {
      display: flex;
      background: #fff;

      .goods-images {
        width: 0.74rem;
        height: 0.74rem;
        border-radius: 0.06rem;
      }

      .goods-info {
        margin-left: 0.1rem;
        flex: 1;
      }

      .goods-name {
        width: 1.8rem;
        font-size: 0.14rem;
        color: #1a1b1d;
      }

      .buy-price {
        font-size: 0.14rem;
        color: #1a1b1d;
      }

      .goods-spec,
      .buy-number {
        font-size: 0.12rem;
        color: #616568;
        margin-top: 0.03rem;
      }
    }

    .order-detail {
      margin-top: 0.13rem;
      border-bottom: 0.01rem solid #eeeeee;
      .refund-desc {
        background: #f3f3f3;
        border-radius: 0.06rem;
        padding: 0.1rem;
        color: #616568;
        margin-bottom: 0.13rem;

        .images {
          margin-top: 0.12rem;

          img {
            object-fit: cover;
            width: 0.72rem;
            height: 0.72rem;
            margin-bottom: 0.1rem;

            &:not(:nth-child(4n + 0)) {
              margin-right: 0.1rem;
            }
          }
        }
      }

      .arrow {
        margin-left: 0.06rem;
      }

      .black {
        color: #1a1b1d;
      }

      .cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 0.14rem;
        margin-bottom: 0.13rem;

        .label {
          color: #1a1b1d;
        }

        .value {
          color: #b2b1b7;
        }

        .money {
          font-size: 0.16rem;
          color: #ff6445;
          font-weight: 600;
        }

        .copy {
          color: #1a1b1d;
        }
      }
    }
  }

  :deep(.complete-bottom-btns button.plain) {
    border: 0.01rem solid #ddd;
    color: #616568;
    font-weight: 400;
  }

  .refund-rule {
    font-size: 0.14rem;
    color: #b2b1b7;
    line-height: 0.2rem;
  }
  .purchaser {
    display: flex;
    .purchaser-item {
      display: block;
      padding: 0 0.02rem;
    }
  }

  .count-down {
    display: inline-block;
    color: #e50615;
  }
</style>
