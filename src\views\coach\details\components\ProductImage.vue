<template>
  <Skeleton class="product-images--skeleton" :loading="loading" animated>
    <template #skeleton>
      <SkeletonImage height="3.15rem" />
    </template>
    <template #content>
      <van-swipe class="product-images" lazy-render :loop="false">
        <van-swipe-item
          v-for="(item, index) in medium"
          :key="index"
          @click="handleClickPreview(index)"
        >
          <template v-if="item.type === 'image'">
            <div class="product-images__item">
              <Image :src="item.url" fit="cover" block :show-loading="false" />
            </div>
          </template>
          <template v-if="item.type === 'video'">
            <div class="product-images__item">
              <div class="play-button">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="0.6rem"
                  height="0.6rem"
                  viewBox="0 0 70 70"
                >
                  <path
                    transform="translate(15,15) scale(0.04,0.04)"
                    d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"
                  ></path>
                </svg>
              </div>
              <Image :src="item.poster" fit="cover" block :show-loading="false" />
            </div>
          </template>
        </van-swipe-item>
        <template #indicator="{ active, total }">
          <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
        </template>
      </van-swipe>
      <van-image-preview
        v-model:show="showPreview"
        :images="previewMedia"
        :start-position="previewStartPosition"
        closeable
      >
        <template #image="{ src: item }">
          <video
            v-if="jsonParse(item).type === 'video'"
            :src="jsonParse(item).url"
            :poster="jsonParse(item).poster"
            style="width: 100%"
            controls
            autoplay
          />
          <Image v-if="jsonParse(item).type === 'image'" :src="jsonParse(item).url" fit="cover" />
        </template>
      </van-image-preview>
    </template>
  </Skeleton>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { Skeleton, SkeletonImage } from '@/components/skeleton'
  import Image from '@/components/image'

  const props = defineProps({
    medium: { type: Array, default: () => [] },
    loading: Boolean,
  })

  const medium = computed(() => {
    return props.medium
  })
  console.log(123, medium.value)
  // 避免 van-image-preview props 类型错误，先将对象转字符串
  const previewMedia = computed(() => {
    return medium.value.map((item) => JSON.stringify(item))
  })

  const showPreview = ref(false)
  const previewStartPosition = ref(0)

  function handleClickPreview(index) {
    showPreview.value = true
    previewStartPosition.value = index
  }

  function jsonParse(text) {
    return JSON.parse(text)
  }
</script>

<style scoped lang="scss">
  .product-images--skeleton {
    width: 100%;
    height: 3.15rem;
  }

  .custom-indicator {
    position: absolute;
    right: 0.05rem;
    bottom: 0.2rem;
    padding: 0.02rem 0.05rem;
    font-size: 0.12rem;
    border-radius: 25%;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
  }

  .product-images :deep(img) {
    width: 100%;
    height: 3.15rem;
  }

  .product-images__item-img {
    width: 100%;
    height: 3.15rem;
  }

  .product-images__item {
    position: relative;
    width: 100%;
    height: 100%;

    .play-button {
      position: absolute;
      left: 50%;
      top: 50%;
      z-index: 1;
      border-radius: 50%;
      display: inline-block;
      width: 0.6rem;
      height: 0.6rem;
      background: rgba(0, 0, 0, 0.38);
      overflow: hidden;
      text-align: center;
      line-height: 0.6rem;
      vertical-align: middle;
      margin: -0.3rem auto auto -0.3rem;
      cursor: pointer;

      &:active {
        opacity: 0.85;
      }

      svg {
        fill: hsla(0, 0%, 100%, 0.7);
      }
    }
  }
</style>
