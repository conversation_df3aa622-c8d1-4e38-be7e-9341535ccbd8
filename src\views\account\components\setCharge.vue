<template>
  <ijl-action-sheet
    v-model:show="showPop"
    :showConfirmButton="false"
    title="收费设置"
    @open="open"
    @confirm="submit"
    @cancel="onClose"
    @close="onClose"
  >
    <div class="charge-box">
      <!-- v-if为了解决组件数据没响应的问题 -->
      <i-select
        v-if="showPop"
        class="teach-type"
        required
        title="选择授课方式"
        label="授课方式"
        placeholder="选择授课方式"
        v-model="formData.feesType"
        :disable="feesTypeDisable"
        :isLink="!feesTypeDisable"
        :isClean="isClean"
        :options="teachList"
      />
      <van-field
        v-model="formData.feesMoney"
        class="green-require"
        required
        type="number"
        label="设置收费"
        placeholder="¥ 请设置价格"
      >
        <!-- <template #button> <span class="unit"> 元/小时</span> </template>430 -->
      </van-field>
      <div class="price-tips">
        <p>温馨提示：</p>
        <p>学员将按此价格付费购课，平台实行三七分成模式<span class="tip">（教练占七成）</span></p>
        <p>请根据实际情况定价，互利共赢，携手并进！</p>
      </div>
      <van-field
        v-model="formData.feesVirtualMoney"
        class="green-require"
        type="number"
        label="设置划线价"
        placeholder="¥ 请设置划线价（选填）"
      />
      <van-field required class="adress" label="课时包">
        <template #input>
          <van-checkbox-group v-model="formData.feesClassHour" direction="horizontal">
            <van-checkbox class="checkbox" :name="10" shape="square" checked-color="#FF9B26">
              10个课时
            </van-checkbox>
            <van-checkbox class="checkbox" :name="20" shape="square" checked-color="#FF9B26">
              20个课时
            </van-checkbox>
            <van-checkbox class="checkbox" :name="30" shape="square" checked-color="#FF9B26">
              30个课时
            </van-checkbox>
            <van-checkbox class="checkbox" :name="50" shape="square" checked-color="#FF9B26">
              50个课时
            </van-checkbox>
          </van-checkbox-group>
        </template>
      </van-field>
      <div class="form-item-block">
        <div class="label">
          <span class="label-title">上传图片<br />（选填）</span>
          <!-- <span class="desc">最多1张，&lt;10M/张，jpg/jpeg/png/gif格式</span> -->
        </div>
        <div class="value">
          <p class="desc">仅1张，&lt;10M/张，jpg/jpeg/png/gif格式</p>
          <upload-file
            preview-size="0.7rem"
            file-type="jpg|png|gif|jpeg"
            :max-size="10240 * 1024"
            v-model="formData.feesImageList"
            :max-count="1"
          />
        </div>
      </div>
    </div>
  </ijl-action-sheet>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue'
  import ISelect from '@/components/form/i-select'
  import UploadFile from '@/components/upload-file'
  import Schema from 'async-validator'
  import { Toast } from 'vant'
  import { ossURLJoin } from '@/common'

  const isClean = ref(false)
  const validator = new Schema({
    feesType: [
      {
        message: '请设置授课方式',
        validator: function (rule, value) {
          if (!value) {
            this.message = '请设置授课方式'
            return false
          }
          return true
        },
      },
    ],
    feesMoney: [
      {
        message: '请设置收费价格',
        validator: function (rule, value) {
          console.log(rule, value)
          if (!value) {
            this.message = '请设置收费价格'
            return false
          } else if (+value <= 0) {
            this.message = '收费价格要大于0'
            return false
          }
          return true
        },
      },
    ],
    feesVirtualMoney: [
      {
        message: '价格不能小于0',
        validator: function (rule, value) {
          if (!value) return true
          if (+value <= 0) {
            this.message = '划线价格要大于0'
            return false
          }
          return true
        },
      },
    ],
    feesClassHour: [
      {
        message: '请设置课时包',
        validator: function (rule, value) {
          if (value.length < 1) {
            this.message = '请设置课时包'
            return false
          }
          return true
        },
      },
    ],
  })

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    teachSelectList: {
      type: Array,
      default: () => [],
    },
    classData: {
      type: Object,
      default: () => {},
    },
  })
  const teachList = ref([
    { text: '私教1对1', id: 1, disabled: false },
    { text: '小班1对2', id: 2, disabled: false },
    { text: '小班1对4', id: 3, disabled: false },
    { text: '亲子班', id: 4, disabled: false },
  ])
  const formData = reactive({
    feesType: '', // 授课方式
    feesMoney: null, // 课时价格
    feesVirtualMoney: null, // 划线价格
    feesClassHour: [10, 20], // 课时
    feesImageList: [], // 教练图片
    feesImage: '', // 教练图片
  })
  const showPop = ref(props.show)

  const teachWaySelectList = ref([])
  const reset = () => {
    formData.feesType = '' // 授课方式
    formData.feesMoney = null // 课时价格
    formData.feesVirtualMoney = null // 划线价格
    formData.feesClassHour = [10, 20] // 课时价格
    formData.feesImageList = [] // 教练图片
  }
  const feesTypeDisable = ref(false)
  watch(
    () => props.teachSelectList,
    (newVal) => {
      teachWaySelectList.value = newVal
    },
    {
      immediate: true,
      deep: true,
    },
  )

  watch(
    () => props.show,
    (newVal) => (showPop.value = newVal),
    { immediate: true },
  )
  watch(
    () => props.isEdit,
    (newVal) => {
      // 编辑/修改
      if (newVal) {
        // console.log(props.classData, "点中要修改的数据");
        isEditState.value = newVal
        const classTemp = JSON.parse(JSON.stringify(props.classData))
        formData.feesType = classTemp.feesType
        formData.feesVirtualMoney = classTemp.feesVirtualMoney
        formData.feesMoney = classTemp.feesMoney
        // formData.feesImageList = classTemp.feesImageList;
        if (classTemp.feesImage) {
          formData.feesImage = classTemp.feesImage
        }
        formData.feesClassHour = classTemp.feesClassHour
        console.log(ossURLJoin(classTemp.feesImage))
        // 本地用base64回显，或者拼接oss+接口url
        if (Array.isArray(classTemp.feesImageList)) {
          formData.feesImageList = classTemp.feesImageList
        } else if (classTemp.feesImage) {
          formData.feesImageList.push({
            url: ossURLJoin(classTemp.feesImage),
            path: classTemp.feesImage || '',
          })
        } else {
          formData.feesImageList = []
        }
        // console.log(formData.feesImageList, "classTemp222222222");

        feesTypeDisable.value = true
      }
    },
  )
  const isEditState = ref(props.isEdit)
  const teachTempList = ref([
    { text: '私教1对1', id: 1, disabled: false },
    { text: '小班1对2', id: 2, disabled: false },
    { text: '小班1对4', id: 3, disabled: false },
    { text: '亲子班', id: 4, disabled: false },
  ])
  const open = () => {
    // 列表过滤掉已选中的(新增)
    if (!isEditState.value) {
      const teachTemp = JSON.parse(JSON.stringify(teachTempList.value))
      teachList.value = teachTemp.filter((a) => !teachWaySelectList.value.some((b) => a.id === b))
      // console.log(teachWaySelectList.value, "teachWaySelectList.value");
      // console.log(teachList.value, "teachList.value6666666666666");
    }
  }

  const emit = defineEmits(['close', 'update:show', 'update:isEdit', 'getClassData'])

  const handleFormData = () => {
    let data = JSON.parse(JSON.stringify(formData))
    data.feesType = +data.feesType
    data.feesMoney = Math.round(parseFloat(data.feesMoney) * 100) / 100
    if (data.feesVirtualMoney) {
      data.feesVirtualMoney = parseFloat((+data.feesVirtualMoney).toFixed(2))
    } else {
      data.feesVirtualMoney = null
    }

    return data
  }
  const submit = () => {
    let formData = handleFormData()
    validator
      .validate(formData)
      .then(() => {
        console.log('验证成功')
        const formDataTemp = JSON.parse(JSON.stringify(formData))
        if (Array.isArray(formDataTemp.feesImageList) && formDataTemp.feesImageList.length > 0) {
          formDataTemp.feesImage = formDataTemp.feesImageList[0].path
        } else if (formDataTemp.feesImageList?.length === 0) {
          formDataTemp.feesImage = ''
        }
        formDataTemp.feesClassHour = formDataTemp.feesClassHour.sort()
        // console.log(formDataTemp, "submit1111111111");
        emit('getClassData', formDataTemp)
        onClose()
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  const onClose = () => {
    emit('update:isEdit', false)
    isEditState.value = false
    showPop.value = false
    teachList.value = JSON.parse(JSON.stringify(teachTempList.value))
    feesTypeDisable.value = false
    reset()
    emit('update:show', false)
  }
</script>

<style scoped lang="scss">
  .charge-box {
    padding: 0 0.1rem;
  }
  // .teach-type :deep(.van-cell){
  //   border-bottom: ;
  // }
  .i-select :deep(.van-cell) {
    // border-bottom: 0.01rem solid #f2f2f2;
    border-bottom: none;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      display: block;
      left: 4%;
      bottom: 0;
      width: 3.25rem;
      border-bottom: 0.01rem solid #f2f2f2;
      z-index: 0;
    }
    &::after {
      border-bottom: none;
    }
  }
  // .adress {
  //   border-top: 0.01rem solid #f2f2f2;
  // }
  .checkbox {
    margin-bottom: 0.1rem;
    width: 2rem;
    flex-shrink: 0;
    &:first-child {
      margin-top: 0.05rem;
    }
  }
  .form-item-block {
    display: flex;
    padding: 0.17rem 0;
    font-size: 0.14rem;

    .label {
      display: flex;

      .label-title {
        padding-top: 0.1rem;
        width: 1rem;
      }
    }

    .value {
      margin-top: 0.05rem;
      // padding-left: 1rem;
      .desc {
        margin-bottom: 0.04rem;
        font-size: 0.1rem;
        color: #959595;
      }
    }
  }

  .price-tips {
    padding: 0.1rem;
    font-size: 12px;
    color: rgba(151, 151, 151, 1);
    line-height: 17px;
    .tip {
      color: rgba(255, 155, 38, 1);
    }
  }
  :deep(.van-action-sheet__header) {
    font-weight: 600;
    color: #616568;
  }
</style>
