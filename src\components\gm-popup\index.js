import { createVNode, render } from 'vue'
import main from './main'

const Popup = {
  open(config = {}) {
    const container = document.createElement('div')
    const vNode = createVNode(main)
    render(vNode, container)
    const instance = vNode.component
    document.body.appendChild(container)

    const { props, exposed } = instance
    Object.assign(props, config)

    return {
      clear() {
        exposed.close()
        document.body.removeChild(container)
      },
    }
  },
}

export default Popup
