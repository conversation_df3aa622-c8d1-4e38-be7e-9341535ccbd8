import { shallowRef, ref } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'

export function useGetLocation() {
  let AMap = shallowRef(null)
  const positionInfo = ref(null) //当前高精度定位信息
  const cityInfo = ref(null)

  AMap = AMapLoader.load({
    key: '80912f26af58c13f0cfd7d4ff10dfb1e', // 申请好的Web端开发者Key，首次调用 load 时必填
    version: '2.0', // JSAPI版本，默认 1.4.15
    plugins: ['AMap.Geolocation', 'AMap.PlaceSearch'], // 要使用的插件列表
  }).then((aMap) => {
    AMap.value = aMap
    getLocation()
  })

  function getLocation() {
    let geolocation = new AMap.value.Geolocation({
      enableHighAccuracy: true, // 是否获取高精度定位，可能影响效率，默认false
      timeout: 10000, // 定位超时时间，ms
      needAddress: true, // 是否需要将定位结果进行逆地理编码操作
      extensions: 'all', // 是否需要详细的你地理编码信息，默认'base'
      offset: [10, 20],
    })
    // 获取精确位置
    geolocation.getCurrentPosition(function (status, result) {
      if (status === 'complete') {
        positionInfo.value = result
        console.log(result, '精确位置')
      } else {
        console.log('获取高精度位置失败')
      }
    })
    // 获取城市信息
    geolocation.getCityInfo(function (status, result) {
      console.log(result, 'result')
      if (status === 'complete') {
        cityInfo.value = result
        console.log(result, '城市信息')
      } else {
        console.log('获取城市信息失败')
      }
    })
  }

  return {
    AMap,
    positionInfo,
    cityInfo,
  }
}
