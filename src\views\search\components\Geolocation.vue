<template>
  <div class="geolocation" @click="toSelectCityPage">
    <div class="geolocation-select-btn">
      <span class="geolocation-city omit">{{ selectedGeolocation.city }}</span>
      <van-icon class="icon" name="arrow-down" color="#453838" size="0.12rem" />
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { reqIpLocation } from '@/api/common'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'

  const router = useRouter()

  // 当前定位
  const curGeolocation = ref({})
  // 当前选择的城市
  const selectedGeolocation = ref({})

  const getGeolocation = async () => {
    const { data } = await reqIpLocation()
    if (data.status === '1' && typeof data.adcode === 'string' && data.adcode !== '') {
      // 解决高德地图返回省的编码情况
      if (data.adcode.indexOf('0000') > -1) {
        data.adcode = data.adcode.substring(0, 2) + '0100'
      }

      curGeolocation.value = { adcode: data.adcode, city: data.city }
    } else {
      curGeolocation.value = { adcode: '440100', city: '广州市' }
    }

    sessionProxyStorage.curGeolocation = curGeolocation.value

    // 选择的城市赋值
    selectedGeolocation.value = localProxyStorage.selectedGeolocation
      ? localProxyStorage.selectedGeolocation
      : curGeolocation.value
    localProxyStorage.selectedGeolocation = selectedGeolocation.value
  }

  const toSelectCityPage = () => {
    router.push({
      name: 'selectCity',
    })
  }

  const initGeolocation = () => {
    if (sessionProxyStorage.curGeolocation) {
      curGeolocation.value = sessionProxyStorage.curGeolocation
      // 选择的城市赋值
      selectedGeolocation.value = localProxyStorage.selectedGeolocation
        ? localProxyStorage.selectedGeolocation
        : curGeolocation.value
      localProxyStorage.selectedGeolocation = selectedGeolocation.value
    } else {
      getGeolocation()
    }
  }

  initGeolocation()
</script>

<style lang="scss" scoped>
  .geolocation {
    display: flex;
    align-items: center;
    font-size: 0.14rem;
    color: #1a1b1d;
    margin-right: 0.08rem;
    user-select: none;

    .geolocation-select-btn {
      .icon {
        margin-left: 0.03rem;
        font-weight: bold;
      }

      .geolocation-city {
        max-width: 0.7rem;
        display: inline-block;
        vertical-align: top;
      }
    }
  }
</style>
