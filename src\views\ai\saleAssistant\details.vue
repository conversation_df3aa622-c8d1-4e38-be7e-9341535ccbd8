<template>
  <Page title="历史对话详情">
    <template #page>
      <!-- 聊天内容区域 -->
      <div class="chat-content" ref="messageListRef" @scroll="handleScroll">
        <!-- 聊天消息列表 -->
        <!-- <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="messages.length < 1 ? '' : '- 已经到底啦 -'"
          @load="onLoad"
        > -->
        <div
          v-for="(msg, index) in messages"
          :key="index"
          :class="['message', msg.role + '-message']"
        >
          <div
            class="recommend-empty"
            v-if="
              msg.role === 'assistant' &&
              msg.botId === 'recommend_coachs' &&
              msg.contentType === 'json' &&
              msg.content.length === 0
            "
          >
            <img class="nothing_icon" :src="imageUrl('nothing_icon.png')" />
            <p class="text">根据用户画像，暂无合适的教练推荐~</p>
            <van-button type="primary" block class="btn" @click="findCoach"> 查找教练 </van-button>
          </div>
          <div
            v-else
            :class="
              msg.role === 'assistant' && msg.botId === 'recommend_coachs'
                ? 'message-content recommend-content'
                : 'message-content'
            "
          >
            <template v-if="msg.role === 'assistant'">
              <template v-if="msg.botId === 'recommend_coachs' && msg.contentType === 'json'">
                <div>
                  <img
                    class="recommend_coach_header"
                    :src="imageUrl('recommend_coach_header.png')"
                  />
                  <h3 class="recommend-title">根据用户画像，推荐以下教练~</h3>
                  <div
                    v-for="(item, index) in handleJson(msg.content)"
                    class="flex justify-between align-center recommend-list"
                    :key="index"
                    @click="openCoachDetail(item)"
                  >
                    <div class="flex align-center">
                      <img
                        class="coach-image"
                        v-default-avatar
                        :src="ossURL + '/' + item.coachImage"
                      />
                      <div class="coach-card">
                        <p class="name"
                          >{{ item.coachName }}
                          <span class="price f10"
                            >￥<span class="strong">{{ item.price }}</span
                            >/课时</span
                          >
                        </p>
                        <p class="teach-title">{{ item.teachTitle }}</p>
                      </div>
                    </div>
                    <!-- <div><van-button plain round type="primary">生成推荐语</van-button></div> -->
                  </div>
                  <div class="find-more-coach flex justify-between align-center">
                    <div class="des">没有合适的教练？可自行查询</div>
                    <van-button class="btn" type="primary" @click="findCoach">查找教练</van-button>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="assistant-message">
                  <Chat :bot-id="msg.botId" :type="msg.contentType" :content="msg.content" />
                </div>
              </template>
              <div class="message-bottom flex justify-between align-center">
                <div class="message-icon flex align-center">
                  <template v-if="msg.mark === 1">
                    <img :src="imageUrl('icon_like_yes.png')" @click="markMessage(msg, 0)" />
                  </template>
                  <template v-else>
                    <img :src="imageUrl('icon_like.png')" @click="markMessage(msg, 1)" />
                  </template>
                  <template v-if="msg.mark === 2">
                    <img
                      class="dislike"
                      :src="imageUrl('icon_dislike_yes.png')"
                      @click="markMessage(msg, 0)"
                    />
                  </template>
                  <template v-else>
                    <img
                      class="dislike"
                      :src="imageUrl('icon_dislike.png')"
                      @click="markMessage(msg, 2)"
                    />
                  </template>
                </div>
                <div
                  v-if="!(msg.role === 'assistant' && msg.botId === 'recommend_coachs')"
                  class="message-icon"
                >
                  <img :src="imageUrl('icon_copy.png')" @click="clipboardData(msg)" />
                </div>
              </div>
            </template>
            <template v-else>
              <div v-if="msg.botId === 'default'" class="user-content">
                {{ msg.content }}
              </div>
              <div v-else class="user-content">
                {{
                  msg.botId === 'recommend_coachs' || msg.botId === 'customer_portrait'
                    ? '@' + msg.content
                    : msg.content
                }}
              </div>
            </template>
          </div>
        </div>
        <div class="finished_box"></div>

        <!-- </van-list> -->
      </div>
    </template>
  </Page>
</template>

<script setup>
  import Page from '@/layout/components/Page'
  import { ossURL, miniAppid } from '@/config/index.js'
  import { ref, reactive, nextTick, onMounted } from 'vue'
  import Chat from '../components/Chat/index.vue'
  import { getMessagesList } from '@/api/ai-server'
  import { useRoute } from 'vue-router'
  import authWorkChat from '../utils/workChat/authWorkChat'

  const { query } = useRoute()

  //const loading = ref(false)
  const finished = ref(false)
  const messages = ref([])
  const params = reactive({
    limit: 60,
    ...query,
    cursor: null,
  })
  const isFetchingHistory = ref(false) // 用于防止重复请求
  const scrollThreshold = 50 // 滚动到顶部多少距离内开始加载历史数据
  // 处理滚动事件
  const handleScroll = (event) => {
    const container = event.target
    if (container.scrollTop <= scrollThreshold && !isFetchingHistory.value) {
      onLoad()
    }
  }
  const ww = ref(null)
  onMounted(async () => {
    onLoad()
    try {
      ww.value = await authWorkChat()
    } catch (error) {
      ww.value = window.ww
    }
  })
  const onLoad = () => {
    if (isFetchingHistory.value || finished.value) return
    isFetchingHistory.value = true
    getMessagesList(params)
      .then((res) => {
        const { data } = res
        messages.value = [...res.data.list, ...messages.value]
        scrollToBottom()
        // 加载状态结束
        // 数据全部加载完成
        if (data.list.length === 0 || !data.hasMore) {
          console.log('数据全部加载完成')
          finished.value = true
        } else {
          params.cursor = data.list[0].id
        }
      })
      .catch(() => {
        // 数据全部请求完成
        finished.value = true
      })
      .finally(() => {
        isFetchingHistory.value = false
      })
  }
  const imageUrl = (url) => {
    return ossURL + '/h5-assets/ai/saleAssistant/' + url
  }
  // 查找教练
  const findCoach = () => {
    ww.value.launchMiniprogram({
      appid: miniAppid,
      path: 'pages/category/category',
    })
  }
  // 打开教练详情页
  const openCoachDetail = (item) => {
    console.log(item)

    ww.value.launchMiniprogram({
      appid: miniAppid,
      path: 'pages/coach/details/details?id=' + item.coachId,
    })
  }
  // 处理JSON 字符串数据
  const handleJson = (json) => {
    return JSON.parse(json)
  }
  // 剪切板
  const clipboardData = (msg) => {
    const text = msg.content
    ww.value.setClipboardData({
      data: text,
      success: (res) => {
        console.log('复制成功', res)
      },
    })
  }

  // 滚动到底部
  const messageListRef = ref(null)
  const scrollToBottom = () => {
    nextTick(() => {
      const container = messageListRef.value
      if (container) {
        // 兼容iOS和安卓的平滑滚动
        if ('scrollBehavior' in document.documentElement.style) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        } else {
          container.scrollTop = container.scrollHeight
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  :deep(ijiaolian-page-body) {
    background: #f5f6fa;
  }
  .chat-content {
    height: calc(100vh - 0.44rem);
    -webkit-overflow-scrolling: touch; /* iOS弹性滚动 */
    overscroll-behavior: contain; /* 防止滚动穿透 */
    flex: 1;
    overflow-y: auto;
    padding: 0.16rem;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
    overflow-anchor: none;

    .message {
      margin-bottom: 0.16rem;
      display: flex;

      &.assistant-message {
        justify-content: flex-start;
        .message-content {
          min-width: 40%;
          background: #fff;
          border-radius: 0.16rem 0.16rem 0.16rem 0.02rem;
          color: #333;
          padding-top: 0;
        }
      }

      &.user-message {
        justify-content: flex-end;
        .message-content {
          background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
          border-radius: 0.16rem 0.16rem 0.02rem 0.16rem;
          // text-decoration-line: underline;
          color: #ffffff;
        }
      }
      .recommend-content {
        min-width: 100% !important;
        padding-top: 0.15rem !important;
      }
      .recommend_coach_header {
        width: 100%;
        height: 0.96rem;
        position: absolute;
        top: 0;
        border: #fff solid 1px;
        left: 0;
        object-fit: cover;
        border-radius: 0.12rem 0.12rem 0 0;
      }
      .recommend-title {
        font-size: 0.15rem;
        color: #333;
        margin-bottom: 0.22rem;
        font-weight: 500;
      }
      .find-more-coach {
        height: 0.48rem;
        background: #f4f6fc;
        border-radius: 0.08rem;
        margin-bottom: 0.12rem;
        margin-top: 0.16rem;
        padding: 0 0.12rem;
        .des {
          font-weight: 400;
          font-size: 0.14rem;
          color: #1a1b1d;
          line-height: 0.2rem;
        }
        .btn {
          background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
          line-height: 0.3rem;
          height: 0.3rem;
          padding: 0.08rem 0.14rem;
          border-radius: 0.2rem;
        }
      }
      .recommend-list {
        background-color: #fff;
        padding: 0.1rem 0;
        // width: 3.1875rem;
        border-bottom: 0.01rem solid #ebebeb;
        &:last-of-type {
          border-bottom: none;
        }
        .coach-image {
          width: 0.48rem;
          height: 0.48rem;
          border-radius: 0.04rem;
          object-fit: cover;
        }
        .coach-card {
          margin-left: 0.08rem;
          .name {
            font-weight: 500;
            font-size: 0.15rem;
            color: #1a1b1d;
          }
          .teach-title {
            font-weight: 400;
            font-size: 0.12rem;
            color: #969699;
          }
          .price {
            font-weight: 400;
            color: #ff6445;
            .strong {
              font-size: 0.14rem;
              font-weight: 600;
            }
          }
        }
      }
      .message-content {
        position: relative;
        padding: 0.15rem;
        max-width: 100%;
        word-wrap: break-word;
        font-size: 0.15rem;

        .assistant-message & {
          background: #f0f7ff;
        }
        .message-bottom {
          margin-top: 0.12rem;
          padding-top: 0.1rem;
          border-top: 0.01rem solid #ebebeb;
          .message-icon {
            width: 0.2rem;
            height: 0.2rem;
            img {
              width: 100%;
              height: 100%;
            }
            .dislike {
              margin-left: 0.12rem;
            }
          }
        }

        .student-info {
          h4 {
            font-size: 0.15rem;
            color: #333;
            margin: 0.12rem 0 0.08rem;
            font-weight: 500;

            &:first-child {
              margin-top: 0;
            }
          }

          p {
            font-size: 0.14rem;
            color: #666;
            line-height: 1.5;
            margin: 0;
          }
        }

        .user-message & {
          background: #e8f5e9;
        }
      }
    }
    .message-empty {
      position: relative;
      background-color: #fff;
      font-weight: 400;
      font-size: 0.14rem;
      color: #333333;
      line-height: 0.24rem;
      border-radius: 0.16rem 0.16rem 0.16rem 0;
      .empty-container {
        padding: 0.14rem 0.5rem 0.02rem 0.15rem;
      }
      .empty-title {
        margin-bottom: 0.13rem;
      }
      .empty-bottom {
        color: #606266;
        padding-left: 0.15rem;
        width: 100%;
        height: 0.59rem;
        background: linear-gradient(rgba(250, 237, 225, 0) 0%, #fefbf9 100%);
        border-radius: 0px 0.16rem 0px 0px;
      }
      .empty_icon {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 1.16rem;
        height: 1.16rem;
      }
    }
    .finished_box {
      width: 100%;
      height: 0.8rem;
    }
  }
</style>
