<template>
  <div>
    <van-field
      v-bind="attrs"
      v-model="inputValue"
      :label="label"
      @click="pickerShow = true"
      readonly
      :disabled="disabled"
      :placeholder="placeholder"
    />
    <week-picker v-model="selected" v-model:show="pickerShow" @confirm="handleConfirm" />
  </div>
</template>

<script setup>
  import { ref, watch, useAttrs } from 'vue'
  import WeekPicker from '../working-day-picker'
  import { transformWeek } from '@/utils/day'

  const attrs = useAttrs()

  let props = defineProps({
    modelValue: Array,
    label: String,
    placeholder: String,
    format: Function,
    disabled: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const inputValue = ref('')

  const pickerShow = ref(false)

  const selected = ref([])

  watch(
    () => props.modelValue,
    (newVal) => {
      if (Array.isArray(newVal)) {
        if (newVal !== selected.value) {
          selected.value = newVal
        }
      }
    },
    {
      immediate: true,
    },
  )

  const updateInputValue = () => {
    if (props.format) {
      inputValue.value = props.format(selected.value)
    }

    let value = ''
    selected.value.forEach((day) => {
      value += transformWeek(day) + '/'
    })
    inputValue.value = value.substring(0, value.length - 1)
  }

  const handleConfirm = (value) => {
    if (!value) return
    selected.value = value.sort()
    updateInputValue()
    emit('update:modelValue', selected.value)
    pickerShow.value = false
  }
</script>
