<template>
  <page :title="$route.meta.title">
    <template #page>
      <div class="page-content page-bg-white">
        <div class="form">
          <FormItem
            v-model="formData.coachName"
            label="姓名"
            placeholder="请输入真实姓名，营造真实平台环境"
            required
          />
          <FormItem
            v-model="formData.sports"
            label="授课科目"
            placeholder="请选择授课科目"
            @click="skillPickerShow = true"
            required
            is-link
          />
          <FormItem
            v-model="formData.teachYear"
            label="教学年限"
            is-link
            @click="workingYearsShow = true"
            placeholder="请选择教学年限"
          />
          <FormItem
            v-model="formData.address"
            label="授课区域"
            placeholder="请选择"
            @click="distPickerShow = true"
            is-link
          />
          <div class="upload-avatar-wrap">
            <div class="avatar-label">
              <p>头像</p>
              <p class="tip"><van-icon name="warning-o" />数据显示学员对教练真人形象照更有好感。</p>
            </div>
            <UploadAvatar
              ref="uploadAvatarRef"
              :image="coachAvatar"
              @success="onUploadAvatarSuccess"
            />
          </div>
          <div class="avatar-demo-wrap">
            <div class="avatar-demo">
              <div class="demo-title">图片样例</div>
              <div class="demo-tip">上传真实个人形象照，更容易赢得专业感</div>
              <div class="demo-images">
                <img src="./images/demo-avatar-1.png" alt="" />
                <img src="./images/demo-avatar-2.png" alt="" />
              </div>
            </div>
          </div>
          <div class="explain">
            以上信息仅用于活动报名，如需入驻爱教练，请前往<br />【个人中心-入驻教练】完善个人信息
          </div>
        </div>
        <div class="fixed-page-footer">
          <button
            class="submit-btn"
            :disabled="submitBtnDisabled"
            v-preventReClick
            @click="onSubmit"
          >
            提交
          </button>
        </div>

        <SkillType
          v-model:show="skillPickerShow"
          :selectedDefault="defaultSkillType"
          radio
          @confirm="handleSkillConfirm"
        />

        <DistPicker
          v-model:show="distPickerShow"
          :default-selected="defaultAddress"
          @finish="handledAddressFinish"
        />

        <van-popup v-model:show="workingYearsShow" round position="bottom">
          <van-picker
            title="教学年限"
            :columns="columns"
            @cancel="workingYearsShow = false"
            @confirm="onConfirm"
          />
        </van-popup>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import Schema from 'async-validator'
  import FormItem from './components/FormItem'
  import UploadAvatar from './components/UploadAvatar'
  import SkillType from '@/components/skill-type'
  import DistPicker from '@/components/dist-picker'
  import {
    reqCoachInfo,
    reqUserEnterFor,
    reqUserRegistrationInfo,
    reqUpdateRegistrationInfo,
  } from './api'
  import { Toast } from 'vant'

  const route = useRoute()
  const router = useRouter()
  const actionType = route.query.actionType
  const skillPickerShow = ref(false)
  const distPickerShow = ref(false)
  const defaultSkillType = ref({})
  const defaultAddress = ref({})
  const coachAvatar = ref('')
  const uploadAvatarRef = ref(null)

  const columns = Array.from(Array(50).keys(), (n) => n + 1)
  const workingYearsShow = ref(false)

  const onConfirm = (value) => {
    formData.value.teachYear = value
    workingYearsShow.value = false
  }

  const submitBtnDisabled = computed(() => {
    if (!uploadAvatarRef.value) return false
    return uploadAvatarRef.value.status === 'uploading'
  })

  const formData = ref({
    activityId: '1',
    coachName: '',
    firstCategoriesId: '',
    firstCategoriesName: '',
    secondCategoriesId: '',
    secondCategoriesName: '',
    thirdlyCategoriesId: '',
    thirdlyCategoriesName: '',
    teachYear: '',
    province: '',
    provinceName: '',
    city: '',
    cityName: '',
    county: '',
    countyName: '',
    coachImage: '',
    id: undefined,
    address: '',
    sports: '',
  })

  let validator = new Schema({
    coachName: { message: '请输入真实姓名', required: true },
    thirdlyCategoriesId: { message: '请选择授课科目', required: true },
  })

  const handleSkillConfirm = ({ selected }) => {
    let types = selected[0] || []

    console.log(selected, 'selected')

    if (types.length === 3) {
      formData.value.sports = types[2].name

      formData.value.firstCategoriesId = types[0].id
      formData.value.firstCategoriesName = types[0].name
      formData.value.secondCategoriesId = types[1].id
      formData.value.secondCategoriesName = types[1].name
      formData.value.thirdlyCategoriesId = types[2].id
      formData.value.thirdlyCategoriesName = types[2].name

      skillPickerShow.value = false
    }
  }

  const handledAddressFinish = (obj) => {
    const { selectedOptions } = obj
    formData.value.address = obj.address

    formData.value.province = selectedOptions[0].adCode
    formData.value.provinceName = selectedOptions[0].adName
    formData.value.city = selectedOptions[1].adCode
    formData.value.cityName = selectedOptions[1].adName
    formData.value.county = selectedOptions[2].adCode
    formData.value.countyName = selectedOptions[2].adName
  }

  const onUploadAvatarSuccess = (data) => {
    formData.value.coachImage = data
  }

  const toActHome = () => {
    router.push('/act/a20230321jlzm')
  }

  const onSubmit = () => {
    const params = JSON.parse(JSON.stringify(formData.value))
    delete params.address
    delete params.sports

    validator
      .validate(params)
      .then(() => {
        if (actionType === 'edit') {
          reqUpdateRegistrationInfo(params).then(() => {
            Toast('修改报名资料成功')
            toActHome()
          })
        } else {
          reqUserEnterFor(params).then(() => {
            Toast('报名成功')
            toActHome()
          })
        }
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  function setFormData(data) {
    formData.value.id = data.id
    formData.value.coachName = data.coachName
    formData.value.teachYear = data.teachYear
    formData.value.coachImage = data.coachImage
    coachAvatar.value = data.coachImage

    // 授课科目
    formData.value.sports = data.thirdlyCategoriesName
    formData.value.firstCategoriesId = data.firstCategoriesId
    formData.value.firstCategoriesName = data.firstCategoriesName
    formData.value.secondCategoriesId = data.secondCategoriesId
    formData.value.secondCategoriesName = data.secondCategoriesName
    formData.value.thirdlyCategoriesId = data.thirdlyCategoriesId
    formData.value.thirdlyCategoriesName = data.thirdlyCategoriesName
    defaultSkillType.value[data.thirdlyCategoriesId] = [
      { id: data.firstCategoriesId, name: data.firstCategoriesName },
      { id: data.secondCategoriesId, name: data.secondCategoriesName },
      { id: data.thirdlyCategoriesId, name: data.thirdlyCategoriesName },
    ]

    // 授课区域
    defaultAddress.value = data.county
    formData.value.province = data.province
    formData.value.provinceName = data.provinceName
    formData.value.city = data.city
    formData.value.cityName = data.cityName
    formData.value.county = data.county
    formData.value.countyName = data.countyName
    if (data.provinceName && data.cityName && data.countyName) {
      formData.value.address = `${data.provinceName}/${data.cityName}/${data.countyName}`
    }
  }

  function initialize() {
    if (actionType !== 'edit') {
      reqCoachInfo().then((res) => {
        const { data } = res
        if (data) {
          setFormData(data)
        }
      })
    } else {
      reqUserRegistrationInfo().then((res) => {
        const { data } = res
        if (data) {
          setFormData(data)
        }
      })
    }
  }

  initialize()
</script>

<style lang="scss" scoped>
  .page-content {
    padding-bottom: 0.7rem;
  }

  .form {
    padding: 0 0.1rem;
  }

  .upload-avatar-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.1rem 0;

    .avatar-label {
      padding-left: 0.1rem;
      flex: 1;

      .tip {
        font-size: 0.12rem;
        color: #ff9b26;
      }

      .tip .van-icon {
        font-size: 0.12rem;
        margin-right: 0.04rem;
      }
    }
  }

  .avatar-demo-wrap {
    .avatar-demo {
      width: 3.14rem;
      height: 2.47rem;
      background: #fafafa;
      border-radius: 0.1rem;
      border: 1px solid #e8e8e8;
      margin: 0.06rem auto 0 auto;
    }

    .demo-title {
      color: #453938;
      margin-top: 0.17rem;
      text-align: center;
    }

    .demo-tip {
      font-size: 0.12rem;
      color: #b2b1b7;
      margin-top: 0.03rem;
      text-align: center;
    }

    .demo-images {
      margin-top: 0.16rem;
      text-align: center;

      img {
        width: 1.1rem;
        height: 1.5rem;

        &:first-child {
          margin-right: 0.2rem;
        }
      }
    }
  }

  .explain {
    margin-top: 0.1rem;
    font-size: 0.12rem;
    color: #b2b1b7;
    text-align: center;
  }

  .fixed-page-footer {
    background-color: #fff;

    .submit-btn {
      width: 3.45rem;
      height: 0.4rem;
      font-size: 0.16rem;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 1px rgba(245, 176, 76, 0.1);
      border-radius: 20px;
      color: #fff;
      margin: 0.1rem auto;

      &:disabled {
        opacity: 0.5;
      }
    }
  }

  .auth-code {
    color: #e02020;
  }

  :deep(.inviter) {
    display: none;
  }
</style>
