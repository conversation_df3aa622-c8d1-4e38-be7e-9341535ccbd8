<template>
  <div>
    <div v-if="pageShow" class="min-height-100 shop-page">
      <goods-swipe :swipes="swipes" />

      <div class="shop-details">
        <div class="title">
          <div class="name">{{ shop.venueName }}</div>
          <div class="pageviews">
            <span v-if="shop.viewNum < 999" class="num">{{ shop.viewNum }}浏览</span>
            <span v-else class="num">999+浏览</span>
          </div>
        </div>

        <div class="shop-title">{{ shop.title }}</div>

        <div class="rate-box">
          <van-rate
            v-model="shop.score"
            readonly
            color="#ffd21e"
            void-icon="star"
            void-color="#eee"
          />
          <span class="star-value">{{ shop.score }}分</span>
        </div>

        <tags class="shop-tags" :list="shop.categoryNames" />

        <div class="shop-desc">
          <unfold>
            {{ shop.metaTitle }}
          </unfold>
        </div>

        <div class="shop-info">
          <div class="item">
            <div class="label">营业时间</div>
            <div class="value">{{ openTimeWeekStr }}</div>
          </div>
          <div class="item">
            <div class="label">授课地址</div>
            <div class="value">{{ shop.location || '-' }}</div>
          </div>
        </div>
      </div>

      <div class="block-container shop-info">
        <div class="title">场馆说明</div>
        <div class="content">
          <template v-if="shop.content">
            <!-- <unpack> -->
            <image-preview-wrapper>
              <div class="rich-text" v-html="shop.content" />
            </image-preview-wrapper>
            <!-- </unpack> -->
          </template>
          <template v-else>
            <van-empty description="暂无内容">
              <template #image>
                <img src="../../assets/images/empty.png" alt="empty" />
              </template>
            </van-empty>
          </template>
        </div>
      </div>

      <div class="mt10">
        <ArticleInfo :articleList="userArticle" :videoList="userVideo" :trainList="userTrain" />
      </div>
      <div class="block-container mt10">
        <div class="title">场馆评价</div>
        <div class="content">
          <van-empty description="暂无评价">
            <template #image>
              <img src="../../assets/images/empty.png" alt="empty" />
            </template>
          </van-empty>
        </div>
      </div>

      <div class="action-bar">
        <button class="action-bar-button" @click="connection">联系场馆</button>
      </div>
    </div>
    <details-empty v-if="emptyShow" />
  </div>
</template>

<script>
  export default { name: 'shopDetails' }
</script>

<script setup>
  import { ref, computed, onBeforeUnmount } from 'vue'
  import { useRoute } from 'vue-router'
  import { ossURL, baseURL } from '@/config'
  import Tags from '@/components/tags'
  // import Unpack from "@/components/unpack";
  import Unfold from '@/components/unpack/unfold'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import GoodsSwipe from '@/views/common/components/goodsSwipe'
  import ArticleInfo from '../common/components/ArticleInfo'
  import DetailsEmpty from '@/views/common/components/details-empty'
  import gm from '@/components/gm-popup'
  import { getShopDetails } from '@/api/coach-server'
  import { getUserArticles, getTrainsByUserId, getVideosByUserId } from '@/api/generic-server'
  import { transformWeek } from '@/utils/day'
  import setWxShare from '@/utils/weChat/share'
  import { isLogin, ossURLJoin, toLogin } from '@/common'
  import { Toast } from 'vant'
  // import useKeepAliveStore from "@/store/keepAlive";

  // const keepAliveStore = useKeepAliveStore();

  const route = useRoute()
  const shopId = route.params.id // 店铺id
  const shop = ref(null) // 店铺详情
  const userArticle = ref([])
  const userVideo = ref([])
  const userTrain = ref([])
  const pageShow = ref(false)
  const emptyShow = ref(false)
  let gmInstance = null

  //轮播图数据
  const swipes = computed(() => {
    if (!shop.value) return []
    let data = []
    if (shop.value.videoUrl) {
      data.push({
        url: ossURL + '/' + shop.value.videoUrl,
        type: 'video',
      })
    }

    shop.value.imageUrls &&
      shop.value.imageUrls.forEach((url) => {
        data.push({
          url: ossURL + '/' + url,
          type: 'image',
        })
      })

    return data
  })

  const openTimeWeekStr = computed(() => {
    if (!shop.value) return ''

    let str = ''
    let timeStr = ''
    if (typeof shop.value.openTimeWeek === 'string') {
      let arr = shop.value.openTimeWeek.split(',')
      arr.forEach((item) => {
        str += transformWeek(item) + '/'
      })
    }

    if (Array.isArray(shop.value.teachingTimeRange)) {
      timeStr = shop.value.teachingTimeRange.join('-')
    }

    return str.substring(0, str.length - 1) + '' + timeStr
  })

  // 设置微信分享
  const setShare = (data) => {
    let imgUrl = ossURL + '/h5-assets/logo.png' // 默认logo图

    if (Array.isArray(data.imageUrls) && data.imageUrls.length > 0) {
      imgUrl = ossURLJoin(data.imageUrls[0])
    }

    setWxShare({
      title: '【爱教练】' + data.venueName,
      desc: data.title,
      link: baseURL + '/shop/details/' + shopId,
      imgUrl: imgUrl,
    })
  }

  const initialize = async () => {
    // 获取店铺详情
    let { data } = await getShopDetails({ id: shopId })
    shop.value = data

    // 设置页面title keywords description
    document.title = shop.value.title + ',爱教练私教网'
    document
      .querySelector('meta[name="keywords"]')
      .setAttribute('content', shop.value.title + ',' + shop.value.venueName, ',爱教练私教网')
    document
      .querySelector('meta[name="description"]')
      .setAttribute('content', shop.value.venueName + ',' + shop.value.metaTitle, ',爱教练私教网')

    if (data.status) {
      pageShow.value = true
    } else {
      emptyShow.value = true
    }

    // 获取文章列表
    let article = await getUserArticles({ userId: shop.value.userId, identityType: 'venues' })
    let video = await getVideosByUserId({ userId: shop.value.userId, identityType: 'venues' })
    let train = await getTrainsByUserId({ userId: shop.value.userId, identityType: 'venues' })
    userArticle.value = article.data || []
    userVideo.value = video.data.records || []
    userTrain.value = train.data.records || []

    setShare(data)
  }

  const connection = () => {
    if (isLogin()) {
      gmInstance = gm.open({
        title: '联系场馆',
      })
    } else {
      Toast('请登录')
      toLogin()
    }
  }

  onBeforeUnmount(() => {
    gmInstance && gmInstance.clear()
  })

  initialize()

  // onBeforeRouteLeave((to) => {
  //   let pages = ["newsDetails", "videoDetails", "trainDetails"];
  //   if (!pages.includes(to.name)) {
  //     // 卸载缓存
  //     keepAliveStore.removeKeepAlive("shopDetails");
  //   }
  // });
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';
  @import '~@/styles/block-container.scss';

  @include Icon('eye', 0.16rem, 0.12rem);

  .shop-page {
    padding-bottom: 1rem;
  }

  .mt10 {
    margin-top: 0.1rem;
  }

  .swipe .van-swipe-item {
    height: 3.15rem;
    background-color: #39a9ed;
  }

  .swipe {
    .swipe-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    :deep(.van-swipe__indicators) {
      bottom: 0.26rem;
    }

    :deep(.van-swipe__indicator) {
      width: 0.04rem;
      height: 0.04rem;
      margin-right: 0.06rem;
    }

    :deep(.van-swipe__indicator--active) {
      width: 0.2rem;
      height: 0.04rem;
      background: #ff9b26;
      border-radius: 0.02rem;
    }
  }

  .shop-details {
    position: relative;
    // top: -0.1rem;
    margin-top: 0.1rem;
    z-index: 2;
    padding: 0.15rem;
    background: #fff;
    border-radius: 0.16rem 0.16rem 0 0;

    .title {
      display: flex;

      .name {
        flex: 1;
        font-size: 0.18rem;
        font-weight: 600;
        color: #1f1f1f;
      }

      .pageviews {
        padding-top: 0.05rem;
        .num {
          font-size: 0.12rem;
          margin-left: 0.06rem;
          color: #b2b1b7;
          vertical-align: top;
        }
      }
    }

    .shop-title {
      margin-top: 0.05rem;
      font-size: 0.14rem;
      color: #616568;
    }

    .shop-tags {
      margin-top: 0.1rem;
    }

    .shop-info {
      margin-top: 0.17rem;

      .item {
        display: flex;
        font-size: 0.14rem;
        margin-bottom: 0.06rem;

        .label {
          width: 0.8rem;
          color: #616568;
          //margin-right: 24px;
        }

        .value {
          flex: 1;
          color: #1a1b1d;
        }
      }
    }
  }

  .shop-desc {
    margin-top: 0.04rem;
  }

  .rate-box {
    margin-top: 0.05rem;
    font-size: 0.2rem;
    display: flex;
    align-content: center;
    align-items: center;

    .star-value {
      margin-left: 0.1rem;
      font-size: 0.12rem;
      color: #979797;
    }
  }

  :deep(.van-empty__image) {
    width: 1rem;
    height: 1rem;
  }

  .article-item {
    padding: 0.07rem 0.1rem 0.05rem 0.1rem;
    margin-bottom: 0.08rem;
    box-shadow: 0 0 0.02rem 0 rgba(0, 0, 0, 0.12);
  }

  .action-bar {
    position: fixed;
    bottom: 0;
    z-index: 100;
    width: 3.75rem;
    height: 0.6rem;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    box-sizing: content-box;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: #fff;
    box-shadow: 0px -1px 0.04rem 0x rgba(0, 0, 0, 0.06);

    .action-bar-button {
      flex: 1;
      height: 0.45rem;
      font-size: 0.16rem;
      background: #ff8c00;
      border-radius: 0.25rem;
      opacity: 0.8;
      color: #fff;
      margin: 0 0.15rem;
    }
  }

  :deep(.rich-text) {
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }
  .shop-info {
    padding-bottom: 0.3rem;
  }
</style>
