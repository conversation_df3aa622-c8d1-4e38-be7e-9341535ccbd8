<template>
  <page :title="$route.meta?.title" navigation-bar-type="transparent" :navigationBarCoverage="9999">
    <template #page>
      <div class="sign-in">
        <div class="logo-wrapper">
          <img class="logo-bg" src="../../assets/images/logoView/logo-bg.png" alt="bg" />
          <div class="half-circle">
            <img class="logo" src="../../assets/images/logo.png" alt="logo" />
          </div>
        </div>

        <!-- 登录表单 -->
        <form class="auth-form" @submit.prevent="signIn">
          <i-field
            v-model.trim="authForm.mobile"
            left-icon="icon-phone"
            placeholder="请输入用户名/手机号"
            @blur="realNameBlur"
          />
          <ErrorTip v-if="isShowAccountTip" :tipTxt="tipNameTxt" />
          <i-field
            v-model.trim="authForm.password"
            type="password"
            v-if="!isSMSLogin"
            left-icon="icon-password"
            placeholder="请输入密码"
          />
          <!-- 解决ios14兼容问题，需要存在3个input -->
          <van-field v-model="authForm.mobile" class="text1" type="password" />

          <i-field
            v-if="isSMSLogin"
            v-model.trim="authForm.imageCode"
            left-icon="icon-shield"
            placeholder="请输入图形验证码"
          >
            <template #right>
              <ImageCaptcha class="img-code" />
            </template>
          </i-field>

          <i-field
            v-if="isSMSLogin"
            v-model.trim="authForm.verifyCode"
            left-icon="icon-phone"
            placeholder="请输入验证码"
          >
            <template #right>
              <span v-if="isGetCaptcha" class="field-right" @click="getVerifyCode">获取验证码</span>
              <CountDown
                class="count-down"
                v-else
                :time="time"
                format="ss 秒"
                @finish="handleCountDownFinish"
              />
            </template>
          </i-field>

          <div class="remember">
            <Checkbox
              v-model="authForm.nL"
              shape="square"
              checked-color="#FF9B26"
              icon-size="0.16rem"
            >
              一周内免登录
            </Checkbox>
          </div>

          <button class="submit-button">登录</button>
        </form>

        <div class="other-login">
          <div
            v-if="isSMSLogin"
            class="other-login-name"
            @click="toggleLoginMode('password_login')"
          >
            账号登录
          </div>
          <div v-if="!isSMSLogin" class="other-login-name" @click="toggleLoginMode('sms_login')">
            短信登录
          </div>
          <div class="spacer" />
          <div class="other-login-name" @click="toSignUp">新用户注册</div>
          <div class="spacer" />
          <div class="other-login-name" @click="$router.push('/account/password-reset')">
            忘记密码
          </div>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { reactive, ref, computed } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { loginByPassword, loginByVerifyCode } from '@/api/auth-server'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'
  import Schema from 'async-validator'
  import { Checkbox, Toast, CountDown } from 'vant'
  import IField from '@/views/login/components/IField'
  import ImageCaptcha from '@/components/image-captcha'
  import { useSendLoginRegisterSMSCode } from '@/use/useSendVerifyCode'
  import { checkPhone } from '@/utils/validate'
  import { verifyUserInfoByMobile } from '@/api/user-server'
  import ErrorTip from '../account/components/ErrorTip.vue'
  import { isWeChat, isQyWeChat } from '@/utils'
  import { toWxAuth } from '@/utils/weChat/webPageAuth'

  const router = useRouter()
  const route = useRoute()

  // 缓存backUrl，在别的地方也会用到此数据
  const setBackURL = () => {
    if (route.query.backUrl) {
      sessionProxyStorage.backUrl = route.query.backUrl
    }
  }

  setBackURL()

  if (isWeChat() && !sessionProxyStorage.weChatUId && !isQyWeChat()) {
    toWxAuth()
  }

  // 表单数据
  const formData = () => {
    return {
      mobile: '',
      password: '',
      imageCode: '',
      verifyCode: '',
      nL: false,
      expired: *********,
    }
  }

  //账号登录表单校检
  let loginValidator = new Schema({
    mobile: { required: true, message: '请输入用户名/手机号' },
    password: { required: true, message: '请输入密码' },
  })

  //短信登录表单校检
  let smsLoginValidator = new Schema({
    mobile: { message: '请输入手机号码', validator: checkPhone },
    imageCode: { required: true, message: '请输入图形验证码' },
    verifyCode: { required: true, message: '请输入短信验证码' },
  })

  let authForm = reactive(formData())

  // 登录方式 password_login: 密码登录 sms_login: 短信验证码登录
  let loginMode = ref('password_login')

  const isShowAccountTip = ref(false)

  // 是否短信登录
  const isSMSLogin = computed(() => loginMode.value === 'sms_login')

  const time = ref(60 * 1000)

  const isGetCaptcha = ref(true)

  // 倒计时结束后
  const handleCountDownFinish = () => {
    isGetCaptcha.value = true
  }

  // 获取验证码
  const getVerifyCode = () => {
    useSendLoginRegisterSMSCode(authForm, () => {
      isGetCaptcha.value = false
      Toast('验证码发送成功')
    })
  }

  //切换登录模式
  const toggleLoginMode = (mode) => {
    if (loginMode.value === mode) return
    loginMode.value = mode

    Object.assign(authForm, formData()) // 重置表单
  }

  // 登录后的操作
  const afterLogin = (data) => {
    Toast('登录成功')
    window.sensors.loginWithKey('$identity_login_id', data.userId)
    localProxyStorage.user = data
    let backUrl = route.query.backUrl || '/'
    window.location.replace(decodeURIComponent(backUrl))
  }

  // 密码登录
  const passwordLogin = () => {
    let loginLoading = Toast.loading({
      message: '登录中...',
      duration: 0,
    })
    let params = {
      mobile: authForm.mobile,
      password: authForm.password,
      expired: authForm.nL ? authForm.expired : undefined,
      unionId: sessionProxyStorage.weChatUId, // 用户微信 unionId
    }

    loginByPassword(params)
      .then((res) => {
        loginLoading.clear()
        afterLogin(res.data)
      })
      .catch(() => {})
  }

  // 短信验证码登录
  const smsLogin = () => {
    let loginLoading = Toast.loading({
      message: '登录中...',
      duration: 0,
    })
    let params = {
      mobile: authForm.mobile,
      verifyCode: authForm.verifyCode,
      expired: authForm.nL ? authForm.expired : undefined,
      unionId: sessionProxyStorage.weChatUId, // 用户微信unionId
    }
    loginByVerifyCode(params)
      .then((res) => {
        loginLoading.clear()
        afterLogin(res.data)
      })
      .catch(() => {})
  }

  // 登录
  const signIn = () => {
    // 短信登录
    if (isSMSLogin.value) {
      smsLoginValidator
        .validate(authForm)
        .then(() => {
          smsLogin()
        })
        .catch(({ errors }) => {
          Toast(errors[0].message)
        })
    } else {
      // 普通账号登录
      loginValidator
        .validate(authForm)
        .then(() => {
          passwordLogin()
        })
        .catch(({ errors }) => {
          Toast(errors[0].message)
        })
    }
  }

  const tipNameTxt = ref('账号不存在')

  const realNameBlur = () => {
    if (!authForm.mobile) {
      isShowAccountTip.value = true
      tipNameTxt.value = '请输入用户名/手机号'
      return
    }
    verifyUserInfoByMobile({ mobile: authForm.mobile, userName: authForm.mobile }).then((res) => {
      isShowAccountTip.value = !res.data
      tipNameTxt.value = '账号不存在'
    })
  }

  // 去注册
  const toSignUp = () => {
    router.push({
      path: '/sign-up',
    })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';

  @include Icon('error-tip', 0.13rem, 0.13rem);

  .error {
    display: flex;
    align-items: center;
    padding-top: 0.02rem;

    span {
      margin-left: 0.05rem;
      font-size: 0.12rem;
      color: #ff6445;
    }
  }

  .sign-in {
    min-height: 100vh;
    background-color: #fff;
    padding-bottom: 0.5rem;
  }

  .logo-wrapper {
    width: 100%;
    height: 2.77rem;
    position: relative;

    .logo-bg {
      height: 2.26rem;
    }

    .half-circle {
      width: 0.84rem;
      height: 0.84rem;
      position: relative;
      z-index: 2;
      background-color: #fff;
      margin: -0.44rem auto 0 auto;
      border-top-left-radius: 50%;
      border-top-right-radius: 50%;
      padding-top: 0.16rem;
    }

    .logo {
      width: 0.64rem;
      display: block;
      margin: 0 auto;
      //margin: -0.44rem auto 0 auto;
    }
  }

  .auth-form {
    margin-top: 0.34rem;
    padding: 0.25rem;

    .remember {
      margin-top: 0.2rem;
      padding-left: 0.09rem;

      :deep(.van-checkbox__label) {
        color: #999999;
      }
    }

    .submit-button {
      width: 3.25rem;
      height: 0.45rem;
      margin-top: 0.42rem;
      font-size: 0.17rem;
      font-weight: 600;
      letter-spacing: 0.05rem;
      color: #ffffff;
      outline: none;
      border: none;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 1px rgba(245, 176, 76, 0.1);
      border-radius: 0.23rem;
    }
  }

  .other-login {
    text-align: center;
    padding: 0 0.25rem;

    .other-login-name {
      font-size: 0.12rem;
      color: #2c71a5;
      display: inline-block;
      margin: 0 0.1rem;
    }

    .spacer {
      width: 1px;
      height: 0.2rem;
      background-color: #ebebeb;
      display: inline-block;
      vertical-align: top;
    }
  }

  .img-code {
    width: 1rem;
    height: 0.42rem;
    display: inline-block;
    border: 1px solid #ebebeb;
    position: absolute;
    right: 0;
    top: 0;
  }

  .field-right,
  .count-down {
    font-size: 0.14rem;
    position: absolute;
    right: 0;
  }

  .text1 {
    width: 0rem;
    height: 0rem;
    padding: 0;
  }
</style>
