<template>
  <div v-html="md.render(props.content)" :class="props.botId || ''"></div>
</template>

<script setup>
  import { defineProps } from 'vue'
  import markdownit from 'markdown-it'
  const md = markdownit({
    html: true,
    linkify: true,
    typographer: true,
    smartypants: true,
    xhtmlOut: true,
  })

  // 配置 marked 选项
  // marked.setOptions({ breaks: true, gfm: true })
  const props = defineProps({
    type: {
      type: String,
      default: 'text',
    },
    botId: {
      type: String,
      default: 'default',
    },
    content: {
      type: String,
      default: '',
    },
  })
</script>

<style lang="scss" scoped>
  :deep(.markdown-it-preview) * {
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: normal;
  }
  :deep(ul, ol) {
    margin-top: 0;
    margin-bottom: 0.1rem;
    list-style: disc;
    li {
      display: list-item;
      text-align: -webkit-match-parent;
      unicode-bidi: isolate;
    }
  }
  :deep(.language-markdown) {
    display: inline-block;
    width: 100%;
  }
  :deep(pre) {
    white-space: pre-line;
  }
  :deep(ul) {
    display: block;
    list-style-type: disc;
    margin-block-start: 1em;
    margin-block-end: 1em;
    padding-inline-start: 0.4rem;
    unicode-bidi: isolate;
  }
  :deep(p) {
    margin-block-start: 1em;
    display: block;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    unicode-bidi: isolate;
  }
  :deep(h1) {
    margin-block-start: 1em;
    font-size: 0.2rem;
  }
  :deep(h2) {
    margin-block-start: 1em;
    font-size: 0.18rem;
  }
  :deep(h3) {
    margin-block-start: 1em;
    font-size: 0.16rem;
  }
  :deep(h4) {
    margin-block-start: 1em;
    font-size: 0.14rem;
  }
  :deep(h5) {
    margin-block-start: 1em;
    font-size: 0.14rem;
  }
</style>
