<template>
  <van-swipe
    width="2.28rem"
    class="notice-swipe"
    :autoplay="3000"
    vertical
    :touchable="false"
    indicator-color="#FF9B26FF"
    :show-indicators="false"
  >
    <van-swipe-item v-for="(item, index) in noticeList" :key="index">
      <div class="message">
        <ijl-image class="avatar" :src="item.avatar" />
        <div class="text">{{ item.messages }}</div>
      </div>
    </van-swipe-item>
  </van-swipe>
</template>

<script setup>
  import { noticeList } from '@/views/home/<USER>'
  import IjlImage from '@/components/image'
</script>

<style lang="scss" scoped>
  .notice-swipe {
    height: 0.23rem;
    background-color: rgba(255, 255, 255, 0.75);
    border-radius: 0.12rem;
    overflow: hidden;

    .van-swipe-item {
      color: #fff;
      height: 2rem;
      text-align: center;
    }

    .message {
      display: flex;
      align-items: center;
      padding-right: 0.05rem;

      .avatar {
        width: 0.23rem;
        height: 0.23rem;
        border-radius: 0.12rem;
        opacity: 0.75;
        border: 1px solid #cccdcd;
      }

      .text {
        font-size: 0.12rem;
        color: #000000;
        margin-left: 0.05rem;
      }
    }
  }
</style>
