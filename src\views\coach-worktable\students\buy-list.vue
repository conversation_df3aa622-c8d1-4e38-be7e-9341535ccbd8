<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <van-list
        class="good-list"
        v-model:loading="loading"
        :finished="finished"
        finished-text="-没有更多了-"
        @load="onLoadStudentBuyHistory"
      >
        <div v-for="item in buyList" :key="item" class="goods-item" @click="toOrderDetail(item)">
          <div class="goods-top">
            <van-row class="buy-other" justify="space-between" align="center">
              <van-col class="buy-time">{{ item.createTime }}</van-col>
              <van-col class="goods-state">{{ item.orderStatus.statusName }}</van-col>
            </van-row>
            <div class="buy-details">
              <img :src="getOssURL(item.orderItemList[0].imageUrl)" class="goods-images" alt="" />
              <div class="goods-info">
                <div>
                  <van-row justify="space-between" align="center">
                    <van-col class="goods-name omit">
                      <span>{{ item.orderItemList[0].spuName }}｜</span>
                      <span>{{ item.orderItemList[0].skuName }}</span>
                    </van-col>
                    <van-col class="buy-price">¥{{ item.orderItemList[0].totalAmount }}</van-col>
                  </van-row>
                </div>
                <div class="goods-spec">
                  授课方式：{{ item.orderItemList[0].teachingWay.typeName }}
                </div>
                <div class="buy-number">课时数：{{ item.orderItemList[0].quantity }}个课时</div>
                <div
                  v-if="item.orderItemList[0].afterSaleStatus.status !== 'NONE'"
                  class="buy-state"
                >
                  {{ item.orderItemList[0].afterSaleStatus.statusName }}
                </div>
              </div>
            </div>
          </div>
          <div class="goods-bottom">
            <van-row justify="space-between" align="center">
              <van-col class="purchaser">
                <div class="purchaser-head-portrait">
                  <img :src="getOssURL(item.studentInfo.avatarUrl)" alt="" />
                </div>
                <div class="purchaser-name">{{ item.studentInfo.studentName }}</div>
              </van-col>
              <van-col class="residue">
                <span class="label">剩余课时：</span>
                <span class="value">{{ item.showRemainQuantity }}个课时</span>
              </van-col>
            </van-row>
          </div>
        </div>
      </van-list>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { reqStudentBuyHistory } from '@/api/coach-worktable'
  import { getOssURL } from '@/common'

  const route = useRoute()
  const router = useRouter()
  const studentUserId = route.query.studentUserId
  const buyList = ref([])
  const loading = ref(false)
  const finished = ref(false)

  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  const onLoadStudentBuyHistory = () => {
    pagination.pageNum += 1
    let params = {
      ...pagination,
      heId: studentUserId,
    }

    reqStudentBuyHistory(params).then((res) => {
      const { data } = res
      buyList.value = buyList.value.concat(data)
      // 加载结束
      loading.value = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        finished.value = true
      }
    })
  }

  const toOrderDetail = (item) => {
    router.push({
      name: 'myWorktableOrderDetails',
      query: {
        orderId: item.id,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .good-list {
    padding: 0 0.08rem;

    .goods-item {
      background: #fff;
      padding: 0 0.15rem;
      margin: 0.08rem 0;
      border-radius: 0.06rem;
    }

    .goods-top {
      border-bottom: 1px solid #eeeeee;
    }

    .buy-other {
      padding: 0.08rem 0;
    }

    .buy-time {
      font-size: 12px;
      color: #b2b1b7;
    }

    .goods-state {
      color: #ff9b26;
    }

    .buy-details {
      display: flex;
      padding-bottom: 0.1rem;
    }

    .goods-images {
      width: 0.74rem;
      height: 0.74rem;
      object-fit: cover;
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
      font-weight: bold;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.04rem;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      text-align: right;
    }

    .goods-bottom {
      padding: 0.1rem 0;

      .purchaser {
        display: flex;
        align-items: center;
      }

      .purchaser-head-portrait {
        width: 32px;
        height: 32px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .purchaser-name {
        margin-left: 0.07rem;
        color: #1a1b1d;
      }

      .residue {
        .label {
          color: #979797;
        }
        .value {
          color: #1f1f1f;
          font-weight: bold;
        }
      }
    }
  }
</style>
