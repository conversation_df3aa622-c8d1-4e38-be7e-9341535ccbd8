<template>
  <page :title="pageTitle">
    <template #page>
      <div class="page">
        <div v-if="isModify" class="tip-text">
          修改手机号码后，可使用新号码登录爱教练用户端和教练工作台
        </div>

        <div class="form" :class="{ 'mt-20': !isModify }">
          <div class="form-item">
            <label class="form-item-label">手机号</label>
            <div class="form-item-content">
              <input
                class="form-item-input"
                v-model="form.mobile"
                placeholder="请填写新手机号"
                @blur="verifyMobile"
              />
            </div>
            <div v-if="formError['mobile']" class="form-error-message">
              <i class="icon-error-tip"></i>
              <div class="message">{{ formError['mobile'] }}</div>
            </div>
          </div>

          <div class="form-item">
            <label class="form-item-label">图形验证码</label>
            <div class="form-item-content">
              <input
                class="form-item-input"
                maxlength="6"
                v-model="form.imageCode"
                placeholder="请输入图形验证码"
              />
            </div>
            <div class="form-item-content-right">
              <ImageCaptcha />
            </div>
          </div>

          <div class="form-item">
            <label class="form-item-label">验证码</label>
            <div class="form-item-content">
              <input
                class="form-item-input"
                maxlength="6"
                v-model="form.verifyCode"
                placeholder="请输入验证码"
              />
            </div>
            <div class="form-item-content-right">
              <span v-if="!isSendSMSCode" class="sms-code" @click="getVerifyCode">获取验证码</span>
              <CountDown
                class="count-down"
                v-else
                :time="time"
                format="ss 秒"
                @finish="handleCountDownFinish"
              />
            </div>
          </div>

          <div class="form-button-wrap">
            <button class="i-button form-submit-button" @click="onConfirm">确定</button>
          </div>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { useRoute } from 'vue-router'
  import Schema from 'async-validator'
  import ImageCaptcha from '@/components/image-captcha'
  import { checkPhone, validate } from '@/utils/validate'
  import { updateUserMobile, verifyUserInfoByMobile } from '@/api/user-server'
  import { useSendLoginRegisterSMSCode } from '@/use/useSendVerifyCode'
  import { Toast, CountDown } from 'vant'
  import { signOut } from '@/common'

  const route = useRoute()
  const type = route.query.type || 'binding'
  const isModify = ref(type === 'modify')
  const isSendSMSCode = ref(false)
  const time = ref(60 * 1000)
  const verifyCodeBtnDisable = ref(false)

  let validator = new Schema({
    mobile: { message: '请输入手机号码', validator: checkPhone },
  })

  const pageTitle = computed(() => {
    if (isModify.value) return '更改手机号'
    return route.meta?.title
  })

  const form = reactive({
    mobile: '',
    imageCode: '',
    verifyCode: '',
  })

  const formError = ref({})

  const verifyMobile = () => {
    formError.value = {}

    if (!form.mobile) {
      formError.value.mobile = '请输入手机号'
      return
    }

    if (validate('mobile', form.mobile)) {
      verifyUserInfoByMobile({ mobile: form.mobile }).then((res) => {
        const { data } = res
        if (data) {
          formError.value.mobile = '该手机号码已被绑定'
          verifyCodeBtnDisable.value = true
        } else {
          verifyCodeBtnDisable.value = false
        }
      })
    } else {
      formError.value.mobile = '手机号码格式不正确'
    }
  }

  // 获取验证码
  const getVerifyCode = () => {
    if (verifyCodeBtnDisable.value) {
      Toast('该手机号码已被绑定')
      return
    }

    let params = {
      mobile: form.mobile,
      imageCode: form.imageCode,
    }
    useSendLoginRegisterSMSCode(params, () => {
      isSendSMSCode.value = true
    })
  }

  // 倒计时结束后
  const handleCountDownFinish = () => {
    isSendSMSCode.value = false
  }

  const onConfirm = () => {
    validator
      .validate(form)
      .then(() => {
        updateUserMobile(form).then(() => {
          Toast('更改手机号码成功，请重新登录')
          signOut()
        })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/mixins/mixins.scss';

  @include Icon('error-tip', 0.13rem, 0.13rem) {
    margin-top: 0.02rem;
  }

  .tip-text {
    padding: 0.13rem 0.15rem 0 0.15rem;
    margin-bottom: 0.37rem;
    font-size: 14px;
    color: #616568;
  }

  .mt-20 {
    margin-top: 0.2rem;
  }

  .sms-code {
    font-size: 0.14rem;
    color: #e02020;
  }

  .disable {
    color: #eeeeee;
  }

  .form {
    padding: 0 0.15rem;

    .form-item {
      position: relative;
      //padding: 0.18rem 0 0.16rem 0;
    }

    .form-item-label {
      width: 0.98rem;
      line-height: 0.54rem;
      position: absolute;
      left: 0.05rem;
      color: #453938;
    }

    .form-item-content {
      display: flex;
      line-height: 0.54rem;
    }

    .form-item-content-right {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .form-item-input {
      width: 100%;
      padding: 0.18rem 0 0.18rem 0.98rem;
      outline: none;
      border: none;
      border-bottom: 1px solid #eeeeee;

      &:focus {
        border-bottom-color: #f5b04c;
      }

      &::-webkit-input-placeholder {
        color: #b2b1b7;
      }
    }

    .form-error-message {
      font-size: 0.12rem;
      color: #ff6445;
      display: flex;
      margin-top: 0.02rem;

      .message {
        margin-left: 0.04rem;
      }
    }

    .tip-text {
      margin-top: 0.08rem;
      color: #979797;
    }

    .form-button-wrap {
      margin-top: 0.35rem;
    }

    .form-submit-button {
      width: 100%;
      height: 0.4rem;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.22rem;
      font-size: 0.16rem;
      color: #ffffff;
    }
  }
</style>
