<template>
  <page :title="pageTitle">
    <template #page>
      <van-tabs
        v-model:active="active"
        sticky
        :offset-top="isMiniprogramEvn ? 0 : '0.44rem'"
        @change="handledTabsChange"
      >
        <van-tab v-for="item in tabList" :key="item.title" :title="item.title">
          <div
            :class="['content', item.title === '关于我们' && 'img-content']"
            v-html="item.content"
          ></div>
        </van-tab>
        <!-- <van-tab title="关于我们">
          <company-profile />
        </van-tab>
        <van-tab title="联系我们">
          <contact-us />
        </van-tab>
        <van-tab title="平台规则">
          <pingtaiguize />
        </van-tab>
        <van-tab title="用户协议">
          <user-agreement />
        </van-tab>
        <van-tab title="隐私声明">
          <legal-notices />
        </van-tab>
        <van-tab title="学员须知">
          <student-notes />
        </van-tab>
        <van-tab title="服务协议">
          <spread-agreement />
        </van-tab> -->
      </van-tabs>
    </template>
  </page>
</template>

<script>
  export default { name: 'help' }
</script>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  // import UserAgreement from '@/views/doc/user-agreement'
  // import LegalNotices from '@/views/doc/legal-notices'
  // import ContactUs from '@/views/doc/contact-us'
  // import CompanyProfile from '@/views/doc/company-profile'
  // import StudentNotes from '@/views/doc/student-notes'
  // import SpreadAgreement from '@/views/doc/spread-agreement'
  // import Pingtaiguize from '@/views/doc/pingtaiguize'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { getAgreement } from '@/api/generic-server'

  const route = useRoute()

  onMounted(() => {
    getAgreementText()
  })

  const tabList = ref([
    {
      title: '关于我们',
      content: '',
    },
    {
      title: '联系我们',
      content: '',
    },
    {
      title: '平台规则',
      content: '',
    },
    {
      title: '用户协议',
      content: '',
    },
    {
      title: '隐私声明',
      content: '',
    },
    {
      title: '学员须知',
      content: '',
    },
    {
      title: '教练合作协议',
      content: '',
    },
  ])

  const getAgreementText = () => {
    getAgreement({
      title: tabList.value[active.value].title,
    }).then((res) => {
      console.log(res)
      tabList.value[active.value].content = res.data.content
    })
  }
  const handledTabsChange = () => {
    getAgreementText()
  }

  const pageTitle = computed(() => {
    return route.meta?.title
  })

  const isMiniprogramEvn = localStorage.getItem('BROWSER_ENV') === 'miniprogram'

  const active = ref(Number(route.query.tabIndex) || 0)

  setWxShare({
    title: '爱教练-全国专业的体育私教预约平台',
    desc: '10年行业耕耘，服务百万学员，购课退款无忧，退课零手续费',
    link: baseURL + '/help',
    imgUrl: ossURL + '/h5-assets/logo.png',
  })
</script>

<style lang="scss" scoped>
  :deep(.van-tabs__line) {
    background: #ff9b26;
  }

  :deep(.inviter) {
    display: none;
  }

  .content {
    padding: 0.15rem;
    background-color: #fff;
  }
  .img-content {
    padding: 0;
    :deep(img) {
      width: 100%;
      // margin-bottom: 0.2rem;
      object-fit: cover;
    }
  }
</style>
