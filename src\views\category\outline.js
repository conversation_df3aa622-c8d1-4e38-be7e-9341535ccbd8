import { onMounted, onUnmounted } from 'vue'
import { throttle } from '@/utils'

let PAGE_OFFSET = 72

function getAnchorTop(anchor) {
  try {
    return anchor.offsetTop - PAGE_OFFSET
  } catch {
    return 0
  }
}

function isAnchorActive(index, anchor, nextAnchor) {
  const scrollY = Math.ceil(document.documentElement.scrollTop || document.body.scrollTop)

  if (index === 0 && scrollY === 0) {
    return [true, anchor.getAttribute('data-anchor-id')]
  }

  if (scrollY < getAnchorTop(anchor)) {
    return [false, null]
  }

  if (!nextAnchor || scrollY < getAnchorTop(nextAnchor)) {
    return [true, anchor.getAttribute('data-anchor-id')]
  }

  return [false, null]
}

export function useActiveAnchor() {
  const onScroll = throttle(setActiveLink, 20)
  let prevActiveLink = null

  onMounted(() => {
    PAGE_OFFSET = document.querySelector('.header-search').offsetHeight + 12
    window.addEventListener('scroll', onScroll)
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', onScroll)
  })

  function bindTabClick() {
    const links = [].slice.call(document.querySelectorAll('.outline-link'))

    // 绑定点击事件同时，默认选择第一个
    if (links[0]) {
      links[0].classList.add('tab-activity')
      prevActiveLink = links[0]
    }

    const onClick = (event) => {
      const hash = event.target.getAttribute('data-hash')
      let anchor = document.querySelector(`h4[data-anchor-id="${decodeURIComponent(hash)}"]`)

      if (anchor) {
        const top = getAnchorTop(anchor)
        window.scrollTo({ top: top })
      }
    }

    links.forEach((link) => link.addEventListener('click', onClick))
  }

  function setActiveLink() {
    const anchors = [].slice.call(document.querySelectorAll('.anchor'))
    const scrollY = Math.ceil(document.documentElement.scrollTop || document.body.scrollTop)

    const innerHeight = window.innerHeight
    const offsetHeight = document.body.offsetHeight
    const isBottom = Math.abs(scrollY + innerHeight - offsetHeight) < 1

    if (anchors.length && isBottom) {
      activateLink(anchors[anchors.length - 1].getAttribute('data-anchor-id'))
      return
    }

    for (let i = 0; i < anchors.length; i++) {
      const anchor = anchors[i]
      const nextAnchor = anchors[i + 1]

      const [isActive, hash] = isAnchorActive(i, anchor, nextAnchor)

      if (isActive) {
        activateLink(hash)
        return
      }
    }
  }

  function activateLink(hash) {
    if (prevActiveLink) {
      prevActiveLink.classList.remove('tab-activity')
    }

    prevActiveLink = document.querySelector(`a[data-hash="${decodeURIComponent(hash)}"]`)
    const activeLink = prevActiveLink

    if (activeLink) {
      activeLink.classList.add('tab-activity')
      activeLink.scrollIntoView({ block: 'center' })
    }
  }

  return {
    bindTabClick,
  }
}
