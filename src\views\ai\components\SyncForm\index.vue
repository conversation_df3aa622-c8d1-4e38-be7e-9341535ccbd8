<template>
  <van-popup
    v-model:show="show1"
    teleport="#app"
    safe-area-inset-bottom
    position="bottom"
    round
    closeable
    :style="{ height: '90%', position: 'absolute', overflow: 'hidden' }"
  >
    <div class="sync_form_header"
      ><img :src="ossURL + '/h5-assets/ai/saleAssistant/sync_form_header.png'"
    /></div>
    <div class="sync-title">同步客户字段</div>
    <div class="form-container">
      <van-field
        v-model="formDataText.area"
        is-link
        readonly
        label-class="sync-field-label"
        label="地区"
        placeholder="请选择地区"
        @click="showRegionPicker = true"
      />
      <van-field
        v-model="formDataText.wantLearningSport"
        is-link
        readonly
        label-class="sync-field-label"
        label="想学习什么运动类型"
        placeholder="请选择运动类型"
        @click="showSportPicker = true"
      />
      <van-field
        v-model="formDataText.learningObject"
        is-link
        readonly
        label-class="sync-field-label"
        label="是自己学还是孩子"
        placeholder="请选择"
        @click="showLearnerPicker = true"
      />
      <van-field
        v-model="formDataText.teachingWay"
        is-link
        readonly
        label-class="sync-field-label"
        label="授课形式"
        placeholder="请选择"
        @click="showTeachingPicker = true"
      />
      <van-field
        v-model="formDataText.mentalPriceRange"
        is-link
        readonly
        label-class="sync-field-label"
        label="学员心里价位"
        placeholder="请选择价位区间"
        @click="showPricePicker = true"
      />
      <van-field
        v-model="formDataText.studentLevel"
        is-link
        readonly
        label-class="sync-field-label"
        label="目前学员水平怎样"
        placeholder="请选择学员水平区间"
        @click="showLevelPicker = true"
      />
      <van-field
        v-model="formDataText.trainingObjective"
        is-link
        readonly
        label-class="sync-field-label"
        label="您的训练目的"
        placeholder="请选择"
        @click="showPurposePicker = true"
      />
      <van-field
        label-class="sync-field-label"
        v-model="formData.otherRemark"
        label="客户的其他需要"
        placeholder="请填写客户要求~"
        type="textarea"
        rows="3"
        show-word-limit
      />
    </div>
    <div class="submit-btn-wrap">
      <van-button type="primary" block class="submit-btn" @click="handleSubmit">
        同步到探马
      </van-button>
    </div>

    <!-- 地区选择器 -->
    <van-popup
      v-model:show="showRegionPicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-cascader
        v-model="regionValue"
        :options="regionOptions"
        active-color="#4A79FC"
        title="请选择地区"
        :field-names="{ text: 'name', value: 'id', children: 'children' }"
        @close="showRegionPicker = false"
        @finish="onRegionFinish"
      />
    </van-popup>

    <!-- 运动类型选择器 -->
    <van-popup
      v-model:show="showSportPicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-cascader
        v-model="wantLearningSportValue"
        :options="wantLearningSport"
        active-color="#4A79FC"
        title="请选择运动类型"
        :field-names="{ text: 'name', value: 'id', children: 'subOptions' }"
        @close="showSportPicker = false"
        @finish="onSportConfirm"
      />
    </van-popup>

    <!-- 学习对象选择器 -->
    <van-popup
      v-model:show="showLearnerPicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-picker
        title="请选择学习对象"
        :columns="learningObject"
        :columns-field-names="{ text: 'name' }"
        :default-index="formData.learningObject"
        @cancel="showLearnerPicker = false"
        @confirm="onLearnerConfirm"
      />
    </van-popup>

    <!-- 授课形式选择器 -->
    <van-popup
      v-model:show="showTeachingPicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-picker
        title="请选择授课形式"
        :columns="teachingWay"
        :columns-field-names="{ text: 'name' }"
        :default-index="formData.teachingWay"
        @cancel="showTeachingPicker = false"
        @confirm="onTeachingConfirm"
      />
    </van-popup>

    <!-- 价格区间选择器 -->
    <van-popup
      v-model:show="showPricePicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-picker
        title="请选择价格区间"
        :columns="mentalPriceRange"
        :default-index="formData.mentalPriceRange"
        :columns-field-names="{ text: 'name' }"
        @cancel="showPricePicker = false"
        @confirm="onPriceConfirm"
      />
    </van-popup>

    <!-- 学员水平选择器 -->
    <van-popup
      v-model:show="showLevelPicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-picker
        title="请选择学员水平区间"
        :columns="studentLevel"
        :default-index="formData.studentLevel"
        :columns-field-names="{ text: 'name' }"
        @cancel="showPricePicker = false"
        @confirm="onLevelConfirm"
      />
    </van-popup>

    <!-- 训练目的选择器 -->
    <van-popup
      v-model:show="showPurposePicker"
      round
      position="bottom"
      teleport="#app"
      :style="{ position: 'absolute' }"
    >
      <van-picker
        title="请选择训练目的"
        :columns="trainingObjective"
        :default-index="formData.trainingObjective"
        :columns-field-names="{ text: 'name' }"
        @cancel="showPurposePicker = false"
        @confirm="onPurposeConfirm"
      />
    </van-popup>
  </van-popup>
</template>

<script setup>
  import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
  import { getFieldOptions, setCustomerFields } from '@/api/ai-server'
  import { Toast } from 'vant'
  import { reqTanAreaOptions } from '@/api/common'
  import { ossURL } from '@/config/index.js'

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    formInfo: {
      type: Object,
      default: () => ({}),
    },
  })

  const emit = defineEmits(['update:show'])

  const show1 = ref(props.show)

  // 监听props.show变化
  watch(
    () => props.show,
    (newVal) => {
      show1.value = newVal
      if (newVal) {
        formInit()
      }
    },
  )

  onMounted(() => {
    getTanAreaOptions()
  })
  const getTanAreaOptions = () => {
    reqTanAreaOptions().then((res) => {
      const { data } = res
      regionOptions.value = JSON.parse(data)
    })
  }
  // 监听内部show变化并emit
  watch(show1, (newVal) => {
    if (newVal !== props.show) {
      emit('update:show', newVal)
    }
  })
  const getIdByValue = (arr, target, filed = 'id', targetFiled = 'name') => {
    const item = arr.find((obj) => obj[filed] === target)
    return item ? item[targetFiled] : ''
  }

  const formInit = async () => {
    formData.value = {
      contactId: null, // 企微联系人id
      area: '', // 地区，传全名,示例值(广东省/广州市/天河区)
      wantLearningSport: '', // 想学习什么运动类型
      learningObject: '', // 自己学习还是孩子
      teachingWay: '', // 授课形式
      mentalPriceRange: '', // 心里价位
      studentLevel: '', // 学生级别
      trainingObjective: '', // 训练目的
      otherRemark: '', // 客户其他需求备注
    }
    formDataText.value = {
      area: '', // 地区，传全名,示例值(广东省/广州市/天河区)
      wantLearningSport: '', // 想学习什么运动类型
      learningObject: '', // 自己学习还是孩子
      teachingWay: '', // 授课形式
      mentalPriceRange: '', // 心里价位
      studentLevel: '', // 学生级别
      trainingObjective: '', // 训练目的
    }
    const res = await getFieldOptions()
    learningObject.value = res.data.learningObject
    mentalPriceRange.value = res.data.mentalPriceRange
    studentLevel.value = res.data.studentLevel
    teachingWay.value = res.data.teachingWay
    trainingObjective.value = res.data.trainingObjective
    wantLearningSport.value = res.data.wantLearningSport
    wantLearningSportValue.value = props.formInfo.wantLearningSport
    if (props.formInfo.contactId) {
      formData.value = props.formInfo
      formDataText.value.contactId = props.formInfo.contactId
      if (props.formInfo.area && props.formInfo.area.length > 0) {
        props.formInfo.area.forEach((element) => {
          if (element.id !== '0') {
            regionValue.value = Number(element.id)
          }
        })

        const area = props.formInfo.area.map((item) => item.name).join('/')
        formData.value.area = area
        formDataText.value.area = area
      }

      formDataText.value.learningObject = getIdByValue(
        learningObject.value,
        props.formInfo.learningObject,
      )
      formDataText.value.teachingWay = getIdByValue(teachingWay.value, props.formInfo.teachingWay)
      formDataText.value.mentalPriceRange = getIdByValue(
        mentalPriceRange.value,
        props.formInfo.mentalPriceRange,
      )
      formDataText.value.studentLevel = getIdByValue(
        studentLevel.value,
        props.formInfo.studentLevel,
      )
      formDataText.value.trainingObjective = getIdByValue(
        trainingObjective.value,
        props.formInfo.trainingObjective,
      )
      const result = findNodeById(
        props.formInfo.wantLearningSport,
        wantLearningSport.value,
        'subOptions',
      )
      formDataText.value.wantLearningSport = result.name
    }
  }
  const formData = ref({
    contactId: '', // 企微联系人id
    area: '', // 地区，传全名,示例值(广东省/广州市/天河区)
    wantLearningSport: '', // 想学习什么运动类型
    learningObject: '', // 自己学习还是孩子
    teachingWay: '', // 授课形式
    mentalPriceRange: '', // 心里价位
    studentLevel: '', // 学生级别
    trainingObjective: '', // 训练目的
    otherRemark: '', // 客户其他需求备注
  })
  const formDataText = ref({
    area: '', // 地区，传全名,示例值(广东省/广州市/天河区)
    wantLearningSport: '', // 想学习什么运动类型
    learningObject: '', // 自己学习还是孩子
    teachingWay: '', // 授课形式
    mentalPriceRange: '', // 心里价位
    studentLevel: '', // 学生级别
    trainingObjective: '', // 训练目的
  })

  const showRegionPicker = ref(false)
  const regionValue = ref('')
  const wantLearningSportValue = ref('')
  const regionOptions = ref([])

  // const onRegionChange = async ({ value }) => {
  //   const result = findNodeById(value, regionOptions.value)
  //   const res = await getAreaOptions({ parentId: value })
  //   if (result.children) {
  //     result.children = res.data.map((item) => {
  //       if (item.level < 3) {
  //         item.children = []
  //       }
  //       return item
  //     })
  //   }
  // }
  // 递归查找函数
  const findNodeById = (nodeId, nodes, childrenName = 'children') => {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node
      }
      if (node[childrenName] && node[childrenName].length > 0) {
        const foundNode = findNodeById(nodeId, node[childrenName])
        if (foundNode) {
          return foundNode
        }
      }
    }
    return null // 未找到节点时返回null
  }

  // 递归查找函数
  const findNodeByIds = (nodeId, nodes, childrenName = 'children') => {
    for (const node of nodes) {
      if (node[childrenName] && node[childrenName].length > 0) {
        const result = findNodeById(nodeId, node[childrenName])
        if (result) {
          // 如果在子节点中找到目标节点，则返回当前节点 ID（父级 ID）和目标节点的 ID
          return [node.id, nodeId]
        }
      }
      if (node.id === nodeId) {
        // 如果当前节点就是目标节点，返回父级 ID（如果存在）和当前层级 ID
        // 注意：这里的父级 ID 可能不存在（例如，节点位于根层级）
        return [null, node.id]
      }
    }
    return null // 未找到节点时返回 null
  }
  const onRegionFinish = ({ value }) => {
    const result = findNodeById(value, regionOptions.value)
    formData.value.area = result.fullName
    formDataText.value.area = result.fullName
    showRegionPicker.value = false
  }

  // 想学习什么运动类型
  const showSportPicker = ref(false)
  const wantLearningSport = ref([])
  const onSportConfirm = ({ value }) => {
    const result = findNodeById(value, wantLearningSport.value, 'subOptions')
    formData.value.wantLearningSport = value
    formDataText.value.wantLearningSport = result.name
    showSportPicker.value = false
  }

  // 是自己学还是孩子
  const showLearnerPicker = ref(false)
  const learningObject = ref([])
  const onLearnerConfirm = (values) => {
    formData.value.learningObject = values.id
    formDataText.value.learningObject = values.name
    showLearnerPicker.value = false
  }

  // 授课形式
  const showTeachingPicker = ref(false)
  const teachingWay = ref([])
  const onTeachingConfirm = (values) => {
    formData.value.teachingWay = values.id
    formDataText.value.teachingWay = values.name
    showTeachingPicker.value = false
  }

  // 学员心里价位
  const showPricePicker = ref(false)
  const mentalPriceRange = ref([])
  const onPriceConfirm = (values) => {
    formData.value.mentalPriceRange = values.id
    formDataText.value.mentalPriceRange = values.name
    showPricePicker.value = false
  }
  // 学员心里价位
  const showLevelPicker = ref(false)
  const studentLevel = ref([])
  const onLevelConfirm = (values) => {
    formData.value.studentLevel = values.id
    formDataText.value.studentLevel = values.name
    showPricePicker.value = false
  }

  // 训练目的
  const showPurposePicker = ref(false)
  const trainingObjective = ref([])
  const onPurposeConfirm = (values) => {
    formData.value.trainingObjective = values.id
    formDataText.value.trainingObjective = values.name
    showPurposePicker.value = false
  }

  const handleSubmit = async () => {
    const arr = findNodeByIds(
      formData.value.wantLearningSport,
      wantLearningSport.value,
      'subOptions',
    )
    const params = { ...formDataText.value, wantLearningSport: arr, area: regionValue.value }
    console.log('表单提交:', params)
    try {
      console.log('params:', params)

      await setCustomerFields(params)
      Toast.success('提交成功')
      show1.value = false
      emit('update:show', false)
    } catch (e) {
      console.log(e)
    }
  }
</script>

<style lang="scss" scoped>
  .sync_form_header {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1.28rem;
    // z-index: -1;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .sync-title {
    font-weight: 600;
    font-size: 0.16rem;
    color: #1a1b1d;
    line-height: 0.22rem;
    text-align: center;
    padding: 0.21rem 0;
    position: relative;
  }
  .form-container {
    position: relative;
    padding: 0.16rem;
    overflow-y: auto;
    height: calc(100% - 1.2rem);
    :deep(.van-cell:after) {
      border: none;
    }

    :deep(.van-field) {
      background: #f5f7fa;
      border-radius: 0.08rem;
      padding: 0.16rem;
      margin-bottom: 0.1rem;
      .sync-field-label {
        width: 1.42rem;
        font-weight: 600;
        font-size: 0.14rem;
        color: #1a1b1d;
        line-height: 0.2rem;
      }
    }
  }
  .submit-btn-wrap {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0.16rem;
    background-color: #fff;
    // border-top: 1px solid #ebebeb;
    .submit-btn {
      box-sizing: border-box;
      background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
      border-radius: 0.2rem;
      font-size: 0.16rem;
      font-weight: 500;
    }
  }
  :deep(.van-picker__confirm) {
    color: #4a79fc;
  }
  :deep(.van-picker__title) {
    color: #1a1b1d;
  }
  :deep(.van-cascader__title) {
    color: #1a1b1d;
  }
</style>
