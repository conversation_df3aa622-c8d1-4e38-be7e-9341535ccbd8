<template>
  <ijl-action-sheet
    v-model="show"
    title="评论回复"
    :showConfirmButton="false"
    :showCancelButton="false"
    @open="onOpen"
    @cancel="onCancel"
    @close="onClose"
  >
    <div class="parent-comment-wrap">
      <comment-item :comment="comment" hideSubComment @reply="onReply" />
    </div>

    <div class="sub-comment-wrap">
      <div class="title">回复({{ subCommentCount }})</div>
      <van-list
        v-model:loading="loading"
        ref="listRef"
        :finished="finished"
        @load="onLoad"
        finished-text="-没有更多了-"
      >
        <div v-for="subComment in list" :key="subComment.replyId" class="sub-comment-item">
          <div class="head-portrait">
            <ijl-image round fit="cover" :src="subComment.authorUserInfo.headImg" />
          </div>
          <div class="content-box">
            <div class="username">
              <span>{{ subComment.authorUserInfo.userName }}</span>
              <span v-if="subComment.replyUserInfo.userId" class="arrow-username">
                {{ subComment.replyUserInfo.userName }}
              </span>
            </div>
            <div class="content" v-html="subComment.content" />
            <div class="content-other">
              <div class="content-time">{{ formatCommentTime(subComment.createTime) }}</div>
              <div class="reply-comment" @click="onSubCommentReply(subComment.replyId)">
                <i class="icon-reply"></i>
                <span>回复</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </div>

    <div class="placeholder" @click="onReply">
      <div class="fake-input">写下你想说的或想了解的…</div>
    </div>

    <!-- 发送评论组件 -->
    <reply-comment ref="replyCommentRef" @submit="onSubmit" />
  </ijl-action-sheet>
</template>

<script setup>
  import { ref, watch, reactive, nextTick } from 'vue'
  import CommentItem from './comment-item'
  import ReplyComment from './reply-comment'
  import { formatCommentTime, isLogin, toLogin } from '@/common'
  import { Toast } from 'vant'
  import {
    reqCommentReplyList,
    reqGetSubCommentCount,
    reqUserReplyComment,
  } from '@/api/user-server'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    comment: {
      type: Object,
      default() {
        return {}
      },
    },
  })

  const emit = defineEmits(['update:modelValue', 'replySuccess'])

  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })
  const replyCommentRef = ref(null)
  const show = ref(true)
  const selectReplyId = ref(null)
  const replayType = ref('')
  const listRef = ref(null)
  const subCommentCount = ref(0)

  watch(
    () => props.modelValue,
    (newValue) => {
      show.value = newValue
    },
    { immediate: true },
  )

  const onOpen = () => {
    resetList()
    getSubCommentCount()
  }

  const getSubCommentCount = () => {
    let params = {
      commentId: props.comment.commentId,
    }
    reqGetSubCommentCount(params).then((res) => {
      subCommentCount.value = res.data
    })
  }

  const onLoad = (callback) => {
    pagination.pageNum += 1
    let params = {
      commentId: props.comment.commentId,
      ...pagination,
    }

    reqCommentReplyList(params).then((res) => {
      const { data } = res
      callback && callback(data)

      list.value = list.value.concat(data)

      loading.value = false

      if (data.length === 0 || data.length < params.pageSize) {
        finished.value = true
      }
    })
  }

  const resetList = () => {
    list.value = []
    loading.value = false
    finished.value = false
    pagination.pageNum = 0
    nextTick(() => {
      listRef.value?.check()
    })
  }

  const onReply = () => {
    if (isLogin()) {
      replayType.value = 'commentReplay'
      replyCommentRef.value?.open()
    } else {
      Toast('你尚未登录，登录后即可发布评论')
      toLogin()
    }
  }

  const onSubCommentReply = (replyId) => {
    if (isLogin()) {
      replayType.value = 'subCommentReply'
      selectReplyId.value = replyId
      replyCommentRef.value?.open()
    } else {
      Toast('你尚未登录，登录后即可发布评论')
      toLogin()
    }
  }

  const onSubmit = (value) => {
    let params = {
      replyId: replayType.value === 'subCommentReply' ? selectReplyId.value : null,
      commentId: props.comment.commentId,
      content: value,
    }
    reqUserReplyComment(params).then((res) => {
      // 关闭和重置发布评论组件
      replyCommentRef.value?.close()
      replyCommentRef.value?.reset()

      const { data } = res
      list.value.unshift(data)
      subCommentCount.value += 1

      let cloneList = JSON.parse(JSON.stringify(list.value))
      emit('replySuccess', {
        count: subCommentCount.value,
        replyList: cloneList.splice(0, 2),
      })

      Toast('发布成功')
    })
  }

  const onCancel = () => {
    emit('update:modelValue', false)
  }

  const onClose = () => {
    emit('update:modelValue', false)
  }
</script>

<style lang="scss" scoped>
  @import './index';

  .parent-comment-wrap {
    border-bottom: 0.08rem solid #f7f7f7;
  }

  .sub-comment-wrap {
    padding-bottom: var(--wx-safe-area-inset-bottom);
    .title {
      padding: 0.1rem 0.15rem;
      color: #1a1b1d;
      border-bottom: 1px solid #f3f3f3;
    }
  }

  .placeholder {
    background: #ffffff;
    box-shadow: 0 -0.02rem 0.04rem 0px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: var(--window-left);
    right: var(--window-right);
    padding: 0.08rem 0.15rem;
    padding-bottom: calc(var(--wx-safe-area-inset-bottom) + constant(safe-area-inset-bottom));
    padding-bottom: calc(var(--wx-safe-area-inset-bottom) + env(safe-area-inset-bottom));

    user-select: none;
    display: flex;
    height: 0.5rem;

    .fake-input {
      flex: 1;
      height: 0.32rem;
      padding: 0.06rem 0.15rem;
      background: #f7f7f7;
      border-radius: 0.16rem;
      border: 1px solid #eeeeee;
      color: #b2b1b7;
    }
  }
</style>
