<template>
  <page navigationBarType="none">
    <template #page>
      <div class="page-content user-info">
        <div class="header">
          <div class="header-l">
            <img
              v-if="isLoginState && userInfo.headImg"
              :src="
                getOssURL(userInfo.headImg) +
                '?x-oss-process=image/interlace,1/format,jpg/quality,q_30'
              "
              v-error-img
              alt=""
            />
            <img v-else src="../../assets/images/user/unlogin-img.png" v-error-img alt="" />
          </div>
          <div class="header-r">
            <div v-if="isLoginState" class="message">
              <div class="message-item" @click="$router.push({ path: '/message' })">
                <img src="../../assets/images/user/message.png" alt="" />
                <div v-if="messageNum > 0" class="message-num">{{ messageNum }}</div>
              </div>
            </div>
            <h3 v-if="!isLoginState" class="title" @click="toLogin">登录/注册</h3>
            <h3 v-if="isLoginState" class="title">
              <span class="omit">
                {{ userInfo.realName || '' }}
              </span>
              <i v-if="userInfo.sex === 'lady'" class="icon icon-female female" />
              <i v-if="userInfo.sex === 'man'" class="icon icon-male male" />
            </h3>
            <div v-if="isLoginState" class="moblie">
              <span>用户编号: {{ userInfo.userCode || '-' }}</span>
              <div class="update-user" @click="$router.push({ path: '/personal-info' })">
                <span>修改资料</span>
                <i class="icon icon-arrow-right" />
              </div>
            </div>
          </div>
        </div>

        <!--        <div v-if="isLoginState" class="order-box">-->
        <!--          <div class="order-item" @click="$router.push({ name: 'studentAttentClass' })">-->
        <!--            <img src="../../assets/images/user/attend-class.png" alt="" />-->
        <!--            我要上课-->
        <!--          </div>-->
        <!--          <div class="order-item" @click="$router.push({ name: 'studentOrder' })">-->
        <!--            <img src="../../assets/images/user/order.png" alt="" />-->
        <!--            我的订单-->
        <!--          </div>-->
        <!--        </div>-->

        <div v-if="isLoginState" class="operation-panel">
          <div
            v-for="item in panels"
            :key="item.name"
            class="panel feedback"
            @click="$router.push({ name: item.routeName })"
          >
            <img class="panel-icon" :src="item.icon" alt="icon" />
            <div class="panel-name">{{ item.name }}</div>
            <div class="icon-arrow-box">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
        </div>

        <div class="user-contain">
          <div
            v-if="isLoginState"
            class="item flex feedback"
            @click="$router.push({ path: '/coach-collect' })"
          >
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-collect.png" alt="" />
              <span>收藏教练</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div
            v-if="!isCoach"
            class="item flex feedback"
            @click="$router.push({ name: 'coachIntro' })"
          >
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-coach.png" alt="" />
              <span>入驻教练</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div
            v-if="isCoach"
            class="item flex feedback"
            @click="$router.push({ name: 'myWorktableCoach' })"
          >
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-coach.png" alt="" />
              <span>教练工作台</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div class="item flex feedback" @click="$router.push({ name: 'shopApplyForm' })">
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-shop.png" alt="" />
              <span>入驻场馆</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div class="item flex feedback" @click="$router.push({ path: '/help' })">
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-help.png" alt="" />
              <span>帮助中心</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div class="item flex feedback" @click="$router.push({ name: 'helpFeedback' })">
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-edit.png" alt="" />
              <span>找不到想要的？</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div class="item flex feedback" @click="$router.push({ path: '/account/settings' })">
            <div class="item-l flex">
              <img src="../../assets/images/user/icon-safe.png" alt="" />
              <span>账号与安全</span>
            </div>
            <div class="item-r">
              <i class="icon icon-arrow-right" />
            </div>
          </div>
        </div>

        <div class="share-wrap">
          <button class="share-btn" @click="openPosterPopup">我要分享</button>
        </div>

        <poster-popup v-model:show="posterPopupShow" />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { checkUserIsCoach } from '@/api/coach-server'
  import { getOssURL } from '@/common'
  import { getUserInfo, getUserMessageUnreadCount } from '@/api/user-server'
  import { isLogin, toLogin } from '@/common'
  import PosterPopup from './poster-popup'

  const posterPopupShow = ref(false)
  const isCoach = ref(false)
  const isLoginState = ref(isLogin())

  const userInfo = ref({})
  const messageNum = ref(null)

  const panels = [
    {
      name: '我要上课',
      icon: require('../../assets/images/user/attend-class.png'),
      routeName: 'studentAttentClass',
    },
    {
      name: '我的订单',
      icon: require('../../assets/images/user/order.png'),
      routeName: 'studentOrder',
    },
  ]

  onMounted(() => {
    if (isLoginState.value) {
      checkUserIsCoach().then((res) => {
        isCoach.value = res.data
      })
      getUserInfo().then((res) => {
        const { data } = res
        userInfo.value = data
      })
      getUserMessageUnreadCount().then((res) => {
        messageNum.value = res.data
      })
    }
  })

  const openPosterPopup = () => {
    if (isLoginState.value) {
      posterPopupShow.value = true
    } else {
      toLogin()
    }
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';
  @include Icon('female', 0.13rem, 0.17rem);
  @include Icon('male', 0.19rem, 0.19rem);
  @include Icon('arrow-right', 0.08rem, 0.13rem);

  .flex {
    display: flex;
    align-items: center;
  }

  .header {
    display: flex;
    align-items: center;
    padding: 0.2rem 0.14rem 0.2rem 0.24rem;
    width: 100%;
    background-color: #fff;

    .header-l {
      width: 0.54rem;
      height: 0.54rem;
      border-radius: 50%;
      border: 0.01rem solid #f8f8f8;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .header-r {
      position: relative;
      margin-left: 0.15rem;

      .message {
        position: absolute;
        top: 0;
        right: 0;

        .message-item {
          position: relative;
          width: 0.3rem;
          height: 0.3rem;

          .message-num {
            position: absolute;
            padding-top: 0.01rem;
            top: -0.05rem;
            right: 0.03rem;
            width: 0.15rem;
            height: 0.15rem;
            line-height: 0.15rem;
            text-align: center;
            border-radius: 50%;
            background-color: #ff6445;
            font-size: 0.12rem;
            color: #ffffff;
          }

          img {
            position: relative;
            width: 0.18rem;
            height: 0.2rem;
          }
        }
      }

      .title {
        height: 0.27rem;
        font-size: 0.18rem;
        font-weight: 600;
        color: #1f1f1f;
        display: flex;
        align-items: center;

        span {
          display: inline-block;
          max-width: 2rem;
          flex-shrink: 0;
        }

        i {
          margin-left: 0.06rem;
          // width: 0.13rem;
          // height: 0.17rem;
          vertical-align: middle;
        }
      }

      .moblie {
        width: 2.68rem;
        display: flex;
        justify-content: space-between;
        font-size: 0.12rem;
        color: #616568;
        margin-top: 0.05rem;

        .update-user {
          display: flex;
          align-items: center;

          span {
            font-size: 0.12rem;
            color: #616568;
          }

          i {
            width: 0.06rem;
            height: 0.09rem;
            margin-left: 0.05rem;
          }
        }
      }
    }
  }

  .user-contain {
    background-color: #fff;

    .item {
      img {
        width: 0.22rem;
        height: 0.22rem;
        margin-right: 0.07rem;
      }

      padding: 0.16rem 0.2rem;
      justify-content: space-between;
    }
  }

  .share-wrap {
    position: absolute;
    bottom: 0.72rem;
    width: 3.75rem;
    text-align: center;

    .share-btn {
      width: 1.68rem;
      height: 0.45rem;
      background: #ffffff;
      border-radius: 0.23rem;
      border: 1px solid #b2b1b7;
    }
  }

  .operation-panel {
    display: flex;
    padding: 0.06rem 0.2rem;
    //margin: 0.06rem 0;
    background: #fff;

    .panel {
      width: 1.6rem;
      display: flex;
      align-items: center;
      position: relative;
      background: #ffffff;
      box-shadow: 0 0 10px 0 rgba(182, 201, 219, 0.26);
      border-radius: 0.1rem;
      padding: 0.13rem 0.32rem 0.13rem 0.15rem;
    }

    .panel + .panel {
      margin-left: 0.15rem;
    }

    .panel-icon {
      width: 0.24rem;
      height: 0.24rem;
    }

    .panel-name {
      margin-left: 0.08rem;
      font-size: 0.14rem;
      font-weight: 500;
      color: #616568;
    }

    .icon-arrow-box {
      width: 0.32rem;
      height: 0.32rem;
      position: absolute;
      right: 0;
      top: 0.09rem;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-arrow-right {
        width: 0.06rem;
        height: 0.11rem;
      }
    }
  }
</style>
