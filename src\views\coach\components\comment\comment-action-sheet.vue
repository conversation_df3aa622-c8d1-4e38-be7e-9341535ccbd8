<template>
  <ijl-action-sheet
    class="coach-comment-dialog"
    ref="actionSheetRef"
    v-model="show"
    :title="title"
    :showConfirmButton="false"
    :showCancelButton="false"
    @open="onOpen"
    @cancel="onCancel"
    @close="onClose"
  >
    <div class="popup-box">
      <comment-list :coach-id="coachId" ref="listRef" />
      <div class="placeholder" @click="showMakeComment">
        <div class="fake-input">写下你想说的或想了解的…</div>
      </div>
    </div>
    <!-- 发送评论组件 -->
    <reply-comment ref="replyCommentBoxRef" @submit="onSubmitComment" />
  </ijl-action-sheet>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import IjlActionSheet from '@/components/action-sheet'
  import CommentList from './comment-list'
  import ReplyComment from './reply-comment'
  import { reqGetCoachCommentCount, reqUserSubmitComment } from '@/api/user-server'
  import { isLogin, toLogin } from '@/common'
  import { Toast } from 'vant'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    coachId: {
      type: [String, Number],
      default: null,
    },
    commentCount: {
      type: Number,
      default: 0,
    },
  })

  const emit = defineEmits(['update:modelValue', 'replySuccess'])

  const show = ref(true)
  const listRef = ref(null)
  const replyCommentBoxRef = ref(null)
  const commentCount = ref(0)
  const actionSheetRef = ref(null)

  const title = computed(() => {
    let countStr = commentCount.value
    if (commentCount.value >= 1000) {
      countStr = '999+'
    }
    return `全部评论(${countStr})`
  })

  watch(
    () => props.modelValue,
    (newValue) => {
      show.value = newValue
    },
    { immediate: true },
  )

  const onOpen = () => {
    listRef.value?.reset()
    getCoachCommentCount()
  }

  // 获取评论总数
  const getCoachCommentCount = () => {
    let params = {
      coachId: props.coachId,
    }
    reqGetCoachCommentCount(params).then((res) => {
      commentCount.value = res.data
    })
  }

  const showMakeComment = () => {
    if (isLogin()) {
      replyCommentBoxRef.value?.open()
    } else {
      Toast('你尚未登录，登录后即可发布评论')
      toLogin()
    }
  }

  const listScrollTop = () => {
    let dialogElem = document.querySelector('.coach-comment-dialog')
    if (dialogElem) {
      let scrollBox = dialogElem.querySelector('.van-action-sheet__content')
      if (!scrollBox) return
      scrollBox.scrollTo(0, 0)
    }
  }

  const onSubmitComment = (value) => {
    let params = {
      coachId: props.coachId,
      mappingId: props.coachId,
      content: value,
      type: 'COACH',
    }
    reqUserSubmitComment(params).then((res) => {
      const { data } = res
      emit('replySuccess')
      // 评论组件，关闭and重置
      replyCommentBoxRef.value?.close()
      replyCommentBoxRef.value?.reset()

      // 将数据push到列表
      listRef.value?.addComment(data)
      commentCount.value += 1

      listScrollTop()
      Toast('发布成功')
    })
  }

  const onCancel = () => {
    emit('update:modelValue', false)
  }

  const onClose = () => {
    emit('update:modelValue', false)
  }
</script>

<style lang="scss" scoped>
  .popup-box {
    position: relative;
    padding-bottom: var(--wx-safe-area-inset-bottom);

    :deep(.i-empty) {
      padding-top: 1.5rem !important;
    }

    .placeholder {
      background: #ffffff;
      box-shadow: 0 -0.02rem 0.04rem 0px rgba(0, 0, 0, 0.1);
      position: fixed;
      bottom: 0;
      left: var(--window-left);
      right: var(--window-right);
      padding: 0.08rem 0.15rem;
      //padding-bottom: calc(var(--wx-safe-area-inset-bottom) + constant(safe-area-inset-bottom));
      //padding-bottom: calc(var(--wx-safe-area-inset-bottom) + env(safe-area-inset-bottom));

      padding-bottom: calc(0.08rem + constant(safe-area-inset-bottom));
      padding-bottom: calc(0.08rem + env(safe-area-inset-bottom));

      user-select: none;
      display: flex;
      height: 0.5rem;

      .fake-input {
        flex: 1;
        height: 0.32rem;
        padding: 0.06rem 0.15rem;
        background: #f7f7f7;
        border-radius: 0.16rem;
        border: 1px solid #eeeeee;
        color: #b2b1b7;
      }
    }
  }
</style>
