<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="page-content page-bg-white">
        <div class="news">
          <van-sticky offset-top="0.44rem">
            <van-dropdown-menu active-color="#ff6445" duration="0">
              <van-dropdown-item
                :title="classifyMenuTitle"
                :title-class="classifyTitleClass"
                ref="classifyRef"
                @open="handleClassifyMenuOpen"
                @close="handleClassifyMenuClose"
              >
                <classify-cascader v-model="classifyValue" @change="onClassifyChange" />
              </van-dropdown-item>
            </van-dropdown-menu>
          </van-sticky>

          <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
          >
            <div
              class="news-item flex"
              v-for="item in teachingVideo"
              :key="item.id"
              @click="onViewDetails(item)"
            >
              <div class="news-item-l">
                <p class="news-title">{{ item.title }}</p>
                <div class="news-info flex">
                  <div>
                    <span class="type-label">视频</span>
                    <span class="author">{{ ellipsis(item.realName) }}</span>
                    <span class="news-time"> {{ item.releaseTime.substring(0, 10) }}</span>
                    <span v-if="item.hits < 999" class="pageviews">{{ item.hits }}浏览 </span>
                    <span v-else class="pageviews">999+浏览</span>
                  </div>
                </div>
              </div>
              <div v-if="item.imageList?.length > 0" class="news-item-r">
                <i-image width="1rem" height="0.7rem" :src="ossURLJoin(item.imageList[0])" />
              </div>
              <div class="news-item-r" v-else>
                <div class="cover-img">
                  <img src="../../assets/images/logo.png" alt="" />
                </div>
                <div class="overlay">
                  <img src="../../assets/images/icon/play-btn.png" alt="" />
                </div>
              </div>
            </div>
          </van-list>
          <empty class="empty" v-show="emptyShow" description="暂无内容" top="1.96rem" />
        </div>
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'videoList' }
</script>

<script setup>
  import { ref, computed } from 'vue'
  import { onBeforeRouteLeave, useRouter } from 'vue-router'
  import { ossURLJoin } from '@/common'
  import { getVideoList } from '@/api/generic-server'
  import IImage from '@/components/image'
  import useKeepAliveStore from '@/store/keepAlive'
  import ClassifyCascader from '@/views/search/components/ClassifyCascader'
  import Empty from '@/components/empty'

  const router = useRouter()
  const loading = ref(false)
  const finished = ref(false)
  const emptyShow = ref(false)
  const classifyMenuTitle = ref('分类')
  const classifyRef = ref(null)
  const classifyValue = ref([])
  let beforeClassifyValue = []
  const selectedClassifyItems = ref([])
  const teachingVideo = ref([])
  const keepAliveStore = useKeepAliveStore()

  const finishedText = computed(() => (teachingVideo.value.length > 0 ? '-没有更多了-' : ''))

  const initQueryParams = () => {
    return {
      pageNum: 0,
      pageSize: 10,
      firstCategoriesId: '',
      secondCategoriesId: '',
      thirdlyCategoriesId: '',
    }
  }
  const queryParams = ref(initQueryParams())

  const classifyTitleClass = computed(() => {
    return classifyValue.value.length !== 0 ? 'dropdown-title-active' : ''
  })

  const onCriteriaQuery = () => {
    loading.value = false
    finished.value = false
    teachingVideo.value.length = 0
    queryParams.value = initQueryParams()

    queryParams.value.pageNum = 1
    queryParams.value.firstCategoriesId = classifyValue.value[0] || ''
    queryParams.value.secondCategoriesId = classifyValue.value[1] || ''
    queryParams.value.thirdlyCategoriesId = classifyValue.value[2] || ''

    getTeachingVideo()
  }

  const onClassifyChange = (value) => {
    selectedClassifyItems.value = value.selected
    if (value.selected.length === 3) {
      classifyRef.value?.toggle(false)
    }
  }

  const handleClassifyMenuOpen = () => {
    beforeClassifyValue = classifyValue.value
  }

  const handleClassifyMenuClose = () => {
    if (beforeClassifyValue.toString() === classifyValue.value.toString()) return

    if (selectedClassifyItems.value.length === 0) {
      classifyMenuTitle.value = '分类'
    } else {
      let lastItem = selectedClassifyItems.value[selectedClassifyItems.value.length - 1]
      classifyMenuTitle.value = lastItem.label
    }

    onCriteriaQuery()
  }

  const getTeachingVideo = () => {
    getVideoList(queryParams.value).then((res) => {
      const { data } = res
      teachingVideo.value = teachingVideo.value.concat(data.records)
      emptyShow.value = teachingVideo.value.length === 0

      loading.value = false
      if (data.records.length === 0 || data.records.length < queryParams.value.pageSize) {
        finished.value = true
      }
    })
  }

  const onLoad = () => {
    queryParams.value.pageNum += 1
    getTeachingVideo()
  }

  const ellipsis = (item) => {
    if (!item) return ''
    if (item.split('').length > 5) {
      return item.substring(0, 5) + '...'
    } else {
      return item
    }
  }

  const onViewDetails = (article) => router.push('/video/details/' + article.id)

  onBeforeRouteLeave((to) => {
    let pages = ['videoDetails']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('videoList')
    }
  })
</script>
<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';
  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: text-top;
    margin-right: 0.06rem;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .news {
    width: 3.75rem;
    background-color: #fff;

    .news-item {
      padding: 0.15rem;
      border-bottom: 1px solid #efefef;

      .news-item-r {
        margin-left: 0.05rem;
        width: 1rem;
        height: 0.7rem;
        flex-shrink: 0;
        position: relative;
      }

      .news-item-r img {
        width: 100%;
        height: 100%;
        border-radius: 0.04rem;
      }
      .cover-img {
        padding-top: 0.12rem;
        width: 1rem;
        height: 0.7rem;
        background: #f6f6f6;
        border: 0.01rem solid #eee;
        img {
          display: block;
          margin: 0 auto;
          width: 0.42rem;
          height: 0.48rem;
          // object-fit: cover;
        }
      }

      .news-item-l {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .news-title {
          height: 0.4rem;
          font-size: 0.15rem;
          color: #414141;
          line-height: 0.2rem;
          margin-bottom: 0.08rem;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }

        .news-info {
          justify-content: space-between;
        }
        .type-label {
          margin-right: 0.04rem;
          padding: 0.01rem 0.04rem;
          background-color: #dbedfe;
          font-size: 0.1rem;
          color: #0083fc;
          border-radius: 0.02rem;
        }
        .author,
        .news-time {
          font-size: 0.12rem;
          color: #b2b1b7;
          line-height: 0.17rem;
          margin-right: 0.1rem;
        }

        .pageviews {
          font-size: 0.12rem;
          color: #b2b1b7;
        }
      }
    }
  }

  .no-more {
    bottom: 0.05rem;
    padding: 0.2rem 0;
    text-align: center;
    font-size: 0.12rem;
    color: #999999;
    background-color: #fff;
  }

  .overlay {
    width: 1rem;
    height: 0.7rem;
    // background: rgba(#f6f6f6, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.04rem;

    img {
      width: 0.26rem !important;
      height: 0.26rem !important;
    }
  }

  .footer {
    position: absolute;
    width: 100%;
    bottom: 0;
    display: flex;
    border-top: 0.01rem solid #eeeeee;
    .reset,
    .submit {
      flex: 1;
      height: 0.4rem;
      line-height: 0.4rem;

      text-align: center;
      font-size: 0.14rem;
      color: #1a1b1d;
    }
    .submit {
      background: #ff9b26;
      color: #fff;
    }
    .reset {
      background-color: #fff;
    }
  }
  :deep(.van-dropdown-menu__item) {
    justify-content: flex-start;
    padding-left: 0.1rem;
  }
  :deep(.van-dropdown-menu__bar) {
    box-shadow: none;
  }
</style>
