import { Quill } from '@vueup/vue-quill'

import { undo, redo, fonts, packUp } from './icons'

// 自定义字体
let fontSizeStyle = Quill.import('attributors/style/size')
fontSizeStyle.whitelist = ['12px', '14px', '16px', '20px', '24px', '36px']
Quill.register(fontSizeStyle, true)

// 修改 align 使用style方式。
const alignStyle = Quill.import('attributors/style/align')
alignStyle.whitelist = ['right', 'center', 'justify']
Quill.register(alignStyle, true)

// 自定义 icon 图标
const icons = Quill.import('ui/icons')
icons.undo = undo
icons.redo = redo
icons.fonts = fonts
icons.packUp = packUp
