import { defineComponent } from 'vue'
import toolbarConfig from './toolbarConfig'

export default defineComponent({
  name: 'toolBar',
  setup() {
    return () => {
      return (
        <div>
          {toolbarConfig.map((value) => {
            let toolbar = null
            if (Array.isArray(value) && value.length > 0) {
              toolbar = value.map((item) => {
                if (typeof item === 'string') {
                  return <button class={'ql-' + item}></button>
                }

                if (Object.prototype.toString.call(item) === '[object Object]') {
                  return Object.keys(item).map((itemKey) => {
                    let itemVal = item[itemKey]
                    if (Array.isArray(itemVal)) {
                      return (
                        <select className={'ql-' + itemKey}>
                          {itemVal.map((option) => {
                            return <option value={option}></option>
                          })}
                        </select>
                      )
                    } else {
                      return <button class={'ql-' + itemKey} value={itemVal}></button>
                    }
                  })
                }
              })
            }
            return toolbar
          })}
        </div>
      )
    }
  },
})
