<template>
  <div class="chat-container" @click="popoverShow = false">
    <div class="header" :style="headerStyle">
      <!-- AI助理信息 -->
      <div class="ai-info">
        <div class="avatar">
          <img :src="imageUrl('ai_avatar.png')" alt="AI头像" />
        </div>
        <div class="info">
          <div class="name">AI销售助理</div>
          <div class="sub-title">爱教练</div>
        </div>
      </div>

      <div class="history">
        <img
          class="history_icon"
          :src="imageUrl('history_icon.png')"
          alt="历史"
          @click="openHistory"
        />
      </div>
    </div>
    <!-- 聊天内容区域 -->
    <div ref="messageListRef" class="chat-content" @scroll="handleScroll">
      <!-- 聊天消息列表 - 虚拟滚动优化 -->
      <div class="message-list">
        <!-- 显示消息总数提示 -->
        <div v-if="messages.length > VISIBLE_MESSAGES" class="message-count-tip">
          共{{ messages.length }}条消息，显示最新{{ visibleMessages.length }}条
        </div>
        <div
          v-for="(msg, index) in visibleMessages"
          :key="`${msg.id || index}-${msg.content?.slice(0, 10) || ''}`"
          :class="['message', msg.role + '-message']"
        >
          <div
            class="recommend-empty"
            v-if="
              msg.role === 'assistant' &&
              msg.botId === 'recommend_coachs' &&
              msg.content?.length === 0
            "
          >
            <img class="nothing_icon" :src="imageUrl('nothing_icon.png')" />
            <p class="text">根据用户画像，暂无合适的教练推荐~</p>
            <van-button type="primary" block class="btn" @click="findCoach"> 查找教练 </van-button>
          </div>
          <div
            v-else
            :class="
              msg.role === 'assistant' && msg.botId === 'recommend_coachs'
                ? 'message-content recommend-content'
                : 'message-content'
            "
            :style="{ paddingBottom: msg.notShow ? '0' : '0.15rem' }"
          >
            <template v-if="msg.role === 'assistant'">
              <template v-if="msg.botId === 'recommend_coachs' && msg.contentType === 'json'">
                <div>
                  <h3 class="recommend-title">根据用户画像，推荐以下教练~</h3>
                  <img
                    class="recommend_coach_header"
                    :src="imageUrl('recommend_coach_header.png')"
                  />
                  <div
                    v-for="(item, index) in handleJson(msg.content)"
                    class="flex justify-between align-center recommend-list"
                    :key="index"
                    @click="openCoachDetail(item)"
                  >
                    <div class="flex align-center">
                      <img
                        class="coach-image"
                        v-default-avatar
                        :src="ossURL + '/' + item.coachImage"
                      />
                      <div class="coach-card">
                        <p class="name"
                          >{{ item.coachName }}
                          <span class="price f10"
                            >￥<span class="strong">{{ item.price }}</span
                            >/课时</span
                          >
                        </p>
                        <p class="teach-title">{{ item.teachTitle }}</p>
                      </div>
                    </div>
                    <div
                      ><van-button
                        plain
                        round
                        class="coach-recommend-btn"
                        type="primary"
                        color="#4A79FC"
                        @click.stop="coachRecommendText(item)"
                      >
                        生成推荐语
                      </van-button></div
                    >
                  </div>
                  <div class="find-more-coach flex justify-between align-center">
                    <div class="des">没有合适的教练？可自行查询</div>
                    <van-button class="btn" type="primary" @click="findCoach">查找教练</van-button>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="assistant-message">
                  <!-- {{ msg.content }} -->
                  <template v-if="isLastMessage(msg, index) && typingBuffer">
                    <Chat :bot-id="msg.botId" :type="msg.contentType" :content="typingBuffer" />
                  </template>
                  <template v-else>
                    <Chat :bot-id="msg.botId" :type="msg.contentType" :content="msg.content" />
                  </template>
                </div>
              </template>
              <div v-if="!msg.notShow" class="message-bottom flex justify-between align-center">
                <div class="message-icon flex align-center">
                  <template v-if="msg.mark === 1">
                    <img :src="imageUrl('icon_like_yes.png')" @click="markMessage(msg, 0)" />
                  </template>
                  <template v-else>
                    <img :src="imageUrl('icon_like.png')" @click="markMessage(msg, 1)" />
                  </template>
                  <template v-if="msg.mark === 2">
                    <img
                      class="dislike"
                      :src="imageUrl('icon_dislike_yes.png')"
                      @click="markMessage(msg, 0)"
                    />
                  </template>
                  <template v-else>
                    <img
                      class="dislike"
                      :src="imageUrl('icon_dislike.png')"
                      @click="markMessage(msg, 2)"
                    />
                  </template>
                </div>
                <div
                  v-if="
                    !(msg.role === 'assistant' && msg.botId === 'recommend_coachs') &&
                    msg.contentType === 'text'
                  "
                  class="message-icon"
                >
                  <img :src="imageUrl('icon_copy.png')" @click="clipboardData(msg)" />
                </div>
              </div>
            </template>
            <template v-else>
              <div v-if="msg.botId === 'default'" class="user-content" @click="copyTextarea(msg)">
                {{ msg.content }}
              </div>
              <div v-else class="user-content" style="text-decoration-line: none">
                {{
                  msg.botId === 'recommend_coachs' || msg.botId === 'customer_portrait'
                    ? '@' + msg.content
                    : msg.content
                }}
              </div>
            </template>
          </div>
        </div>
      </div>
      <transition name="van-slide-left">
        <div v-if="emptyShow" class="message-empty">
          <div class="empty-container">
            <div class="empty-title"> 我是您的AI销售助理，我有3大功能：</div>
            <p><strong>· 总结学员画像：</strong>让你更全面了解学员 </p>
            <p><strong>· 智能推荐教练：</strong>根据学员画像匹配教练</p>
            <p><strong>· 同步客户字段：</strong>客户关键字段一键同步</p>
          </div>
          <div class="empty-bottom">让我们一起提高业绩吧! 💪🏻</div>
          <img class="empty_icon" :src="imageUrl('empty_icon.png')" alt="#" />
        </div>
      </transition>
      <div v-if="nothingShow" class="message-empty">
        <div class="empty-container">
          <img class="nothing-icon" :src="imageUrl('nothing_icon.png')" alt="" />
          <div class="nothing-text">网络错误，请稍后再试~</div>
        </div>
      </div>
    </div>

    <!-- 底部功能区 -->
    <div :class="{ 'bottom-actions': true, 'disabled-bottom-actions': loading }">
      <div class="action-buttons">
        <div class="action-btn" @click="sendMessage('customer_portrait')">
          <img :src="imageUrl('summary-icon.png')" alt="总结" />
          <span>总结学员画像</span>
        </div>
        <div class="action-btn" @click="sendMessage('recommend_coachs')">
          <img :src="imageUrl('recommend-icon.png')" alt="推荐" />
          <span>智能推荐教练</span>
        </div>
        <div class="action-btn" @click="sendMessage('customer_fields')">
          <img :src="imageUrl('sync-icon.png')" alt="同步" />
          <span>同步客户字段</span>
        </div>
      </div>

      <!-- 输入框区域 -->
      <div class="input-area">
        <div class="chat-btn">
          <img :src="imageUrl('icon_new_chat_yes.png')" @click="handleCreate" />
          <div v-if="popoverShow" class="popover">
            <div class="arrow"></div>
            <div class="text">点击这里开始新对话</div>
          </div>
        </div>
        <div class="input-wrapper">
          <textarea
            v-model="message"
            placeholder="有什么问题都可以问我~"
            class="chat-input"
            @keydown.enter.exact.prevent="sendMessage"
            @input="adjustTextareaHeight"
            rows="1"
            ref="textareaRef"
          ></textarea>
        </div>
        <div class="send-btn" @click="sendMessage">
          <img v-if="message" :src="imageUrl('icon_send_yes.png')" alt="发送" />
          <img v-else :src="imageUrl('icon_send.png')" alt="发送" />
        </div>
      </div>
    </div>
    <div v-if="showScrollBottomIcon" class="scroll_bottom_icon">
      <img :src="imageUrl('scroll_bottom_icon.png')" @click="scrollToBottom('smooth')" />
    </div>
  </div>
  <Loading v-if="loading" :show-btn="showCancelBtn" @cancel="cancel" />
  <SyncForm v-model:show="showSyncFrom" :formInfo="formContent" />
  <van-overlay z-index="20" :show="showCreateTip">
    <div class="wrapper">
      <div
        class="create-popup"
        :style="{ backgroundImage: `url(${ossURL}/h5-assets/ai/saleAssistant/popup.png)` }"
      >
        <div class="create-popup-title">确认结束当前会话，开启新对话吗？</div>
        <div class="flex justify-between" style="width: 100%; padding: 0 0.24rem">
          <van-button class="btn" round plain type="default" @click="showCreateTip = false"
            >取消</van-button
          >
          <van-button class="btn confirm-btn" round type="primary" @click="createMessage"
            >确认</van-button
          >
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<!-- <script>
  export default { name: 'SaleAssistant' }
</script> -->

<script setup>
  import {
    ref,
    nextTick,
    onMounted,
    onUnmounted,
    watch,
    reactive,
    defineAsyncComponent,
    computed,
  } from 'vue'
  import { Toast } from 'vant'
  import { ossURL, miniAppid, aiApiURL } from '@/config/index.js'
  import {
    createChat,
    getChat,
    getMark,
    getConversationLast,
    getMessagesList,
    cancelStreamChat,
    // getStreamChat,
  } from '@/api/ai-server'
  import authWorkChat from '../utils/workChat/authWorkChat'
  import router from '@/router'
  import { localProxyStorage } from '@/utils/storage'
  import { useRoute } from 'vue-router'
  import { debounce, throttle } from '@/utils/index.js'

  // 组件懒加载优化
  const Loading = defineAsyncComponent(() => import('../components/Loading/index.vue'))
  const SyncForm = defineAsyncComponent(() => import('../components/SyncForm/index.vue'))
  const Chat = defineAsyncComponent(() => import('../components/Chat/index.vue'))

  const { query } = useRoute()
  // 消息列表
  const messages = ref([])
  const loading = ref(false)
  const emptyShow = ref(false)
  const nothingShow = ref(false)
  const popoverShow = ref(false)
  const showCancelBtn = ref(true)

  // 内存优化：限制消息列表长度
  const MAX_MESSAGES = 100
  const VISIBLE_MESSAGES = 50

  // 虚拟滚动优化：只渲染可见消息
  const visibleMessages = computed(() => {
    if (messages.value.length <= VISIBLE_MESSAGES) {
      return messages.value
    }
    // 只渲染最新的消息
    return messages.value.slice(-VISIBLE_MESSAGES)
  })

  // 判断是否为最后一条消息（用于流式响应显示）
  const isLastMessage = (msg) => {
    // 在虚拟滚动中，需要检查是否为原始消息列表的最后一条
    const lastMessage = messages.value[messages.value.length - 1]
    return msg === lastMessage
  }

  const headerStyle = {
    backgroundImage: `url(${ossURL}/h5-assets/ai/saleAssistant/header_icon.png)`,
  }
  // const contactId = ref('')
  const contactId = ref('')
  const conversationId = ref('')

  const showCreateTip = ref(false)
  const handleCreate = () => {
    if (loading.value) return
    showCreateTip.value = true
  }
  const createMessage = async () => {
    showCreateTip.value = false
    try {
      const toastLoading = Toast.loading({
        message: '加载中...',
        duration: 0,
        forbidClick: true,
      })
      const res = await createChat({ contactId: contactId.value })
      toastLoading.clear()
      messages.value = []
      emptyShow.value = true
      conversationId.value = res.data.conversationId
      scrollToTop()
    } catch (error) {
      Toast.fail(error?.msg || '创建会话失败')
    }
  }
  // 内存优化：监听消息列表长度，防止内存泄漏
  watch(
    () => messages.value,
    (newVal) => {
      if (newVal.length > 0) {
        emptyShow.value = false
      } else {
        emptyShow.value = true
      }

      // 内存优化：限制消息列表长度
      if (newVal.length > MAX_MESSAGES) {
        console.log(`🧹 [内存优化] 消息列表超过${MAX_MESSAGES}条，清理旧消息`)
        messages.value = newVal.slice(-MAX_MESSAGES)
        performanceMonitor.markMilestone(`消息列表清理完成，当前${messages.value.length}条`)
      }
    },
    { deep: true },
  )

  const message = ref('')
  const messageListRef = ref(null)
  const textareaRef = ref(null)
  const ww = ref()

  const cursor = ref(null)
  const isFetchingHistory = ref(false) // 用于防止重复请求
  const finished = ref(false) // 用于防止重复请求
  const scrollThreshold = 50 // 滚动到顶部多少距离内开始加载历史数据
  const getLastMessagesList = async () => {
    try {
      if (query.conversationId) {
        conversationId.value = query.conversationId
      } else {
        const resLast = await getConversationLast({ contactId: contactId.value })
        conversationId.value = resLast.data.conversationId
        console.log(resLast, '获取最新会话id')
      }
      loadMoreHistory()
    } catch (error) {
      console.log(error)
    }
  }
  // 处理滚动事件 - 防抖优化
  const showScrollBottomIcon = ref(false)

  // 滚动底部图标显示逻辑 - 节流优化
  const updateScrollBottomIcon = throttle((event) => {
    const messageContainer = messageListRef.value
    if (messageContainer && messageContainer.scrollHeight - event.target.scrollTop > 800) {
      showScrollBottomIcon.value = true
    } else {
      showScrollBottomIcon.value = false
    }
  }, 100)

  // 历史消息加载逻辑 - 防抖优化
  const checkLoadHistory = debounce((event) => {
    const container = event.target
    if (container.scrollTop <= scrollThreshold && !isFetchingHistory.value) {
      loadMoreHistory()
    }
  }, 200)

  const handleScroll = (event) => {
    updateScrollBottomIcon(event)
    checkLoadHistory(event)
  }
  // 加载更多历史数据
  const loadMoreHistory = async () => {
    if (isFetchingHistory.value || finished.value) return
    isFetchingHistory.value = true

    try {
      const response = await getMessagesList({
        conversationId: conversationId.value,
        cursor: cursor.value, // 假设每次加载最新的一页数据
        limit: 60, // 每页加载的消息数量
      })
      messages.value = [...response.data.list, ...messages.value]
      scrollToBottom('auto')
      if (response.data.list.length === 0 || !response.data.hasMore) {
        console.log('getMessagesList数据全部加载完成')
        finished.value = true
      } else {
        cursor.value = response.data.list[0].id
      }
    } finally {
      isFetchingHistory.value = false
    }
  }

  onMounted(async () => {
    performanceMonitor.markMilestone('组件开始挂载')

    // 开发环境直接设置contactId
    if (process.env.NODE_ENV === 'development') {
      contactId.value = 'wmM7_-MAAA7gxDfQ73lzEGJQ1fn4NR6A'
      // 并行执行预加载和获取消息列表
      Promise.all([preloadImages(), getLastMessagesList()])
      performanceMonitor.markMilestone('开发环境初始化完成')
    }

    // 设置popover显示状态
    nextTick(() => {
      if (!localProxyStorage?.popoverShow) {
        localProxyStorage.popoverShow = true
        popoverShow.value = true
      }
    })

    const toastLoading = Toast.loading({
      message: '加载中...',
      duration: 0,
      forbidClick: true,
    })

    try {
      performanceMonitor.markMilestone('开始并行请求')

      // API并行请求优化：同时执行企业微信授权和图片预加载
      const [wwInstance] = await Promise.all([
        authWorkChat(),
        preloadImages(), // 并行预加载图片
      ])

      performanceMonitor.markMilestone('并行请求完成')
      ww.value = wwInstance

      // 获取用户信息
      ww.value.getCurExternalContact({
        success: (res) => {
          console.log('getCurExternalContact:success', res)
          contactId.value = res.userId
          getLastMessagesList()
          performanceMonitor.markMilestone('用户信息获取完成')
        },
        fail: (err) => {
          Toast(err?.errMsg || '获取getCurExternalContact权限失败')
          console.log('getCurExternalContact:fail', err)
        },
      })
    } catch (error) {
      console.error('初始化失败:', error)
      Toast('初始化失败，请刷新重试')
    } finally {
      toastLoading.clear()
      performanceMonitor.markMilestone('组件挂载完成')
    }
  })

  const imageUrl = (url) => {
    return ossURL + '/h5-assets/ai/saleAssistant/' + url
  }

  // 性能监控工具
  const performanceMonitor = {
    startTime: Date.now(),

    markMilestone(name) {
      const time = Date.now() - this.startTime
      console.log(`🚀 [性能监控] ${name}: ${time}ms`)
    },
  }

  // 图片预加载优化
  const preloadImages = () => {
    return new Promise((resolve) => {
      const criticalImages = [
        'ai_avatar.png',
        'history_icon.png',
        'header_icon.png',
        'summary-icon.png',
        'recommend-icon.png',
        'sync-icon.png',
        'icon_new_chat_yes.png',
        'icon_send.png',
        'icon_send_yes.png',
        'empty_icon.png',
        'nothing_icon.png',
      ]

      let loadedCount = 0
      const totalImages = criticalImages.length

      criticalImages.forEach((img) => {
        const image = new Image()
        image.onload = image.onerror = () => {
          loadedCount++
          if (loadedCount === totalImages) {
            performanceMonitor.markMilestone('图片预加载完成')
            resolve()
          }
        }
        image.src = imageUrl(img)
      })

      // 如果没有图片需要加载，直接resolve
      if (totalImages === 0) {
        resolve()
      }
    })
  }

  // 调整输入框高度 - 防抖优化
  const adjustTextareaHeight = debounce(() => {
    nextTick(() => {
      const textarea = textareaRef.value
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
      }
    })
  }, 100)

  // 发送消息
  const sendMessage = async (type) => {
    if (typeof type === 'string') return handleTypeMessage(type)
    if (loading.value) return
    if (!message.value.trim()) return
    loading.value = true
    // 添加用户消息
    messages.value.push({
      role: 'user',
      botId: 'default',
      content: message.value,
    })

    // 重置输入框高度
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto'
    }
    scrollToBottom()

    // 模拟AI回复
    replyMessage(message.value, 'default')
    message.value = ''
  }

  const typingBuffer = ref('')
  // 创建控制器
  let controller = null
  // 流式响应优化：批量更新DOM
  let updateTimer = null

  const replyMessage = async (message, botId) => {
    controller = new AbortController()
    const signal = controller.signal
    const baseUrl = process.env.VUE_APP_RUN_ENV !== 'development' ? aiApiURL : ''
    const authToken = localProxyStorage.user.authToken || ''
    typingBuffer.value = ''
    // 调用接口获取聊天流
    nothingShow.value = false
    loading.value = true
    try {
      const response = await fetch(`${baseUrl}/api/ai/stream/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
        body: JSON.stringify({
          conversationId: conversationId.value,
          inputs: message,
          botId: botId,
          attachments: [],
          params: recommendParams.value,
        }),
        signal,
      })

      if (!response.ok) {
        loading.value = false
        nothingShow.value = true
        Toast('发送消息失败')
        throw new Error('发送消息失败')
      }
      if (response?.code && response?.code !== 0) {
        loading.value = false
        nothingShow.value = true
        Toast(response.data.msg || '发送消息失败')
        throw new Error('response.data.msg' || '发送消息失败')
      }
      const aiMessage = reactive({
        conversationId: conversationId.value,
      })
      messages.value.push(aiMessage)
      // loading.value = false
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let shouldContinue = true
      // 流式响应优化：批量更新DOM，减少重绘
      const handleEvent = (event, data) => {
        const { content } = data || {}
        if (!aiMessage?.role) {
          Object.assign(aiMessage, data)
          Object.assign(aiMessage, { content: '' })
        }
        if (content) {
          // console.log('content:', content)
          typingBuffer.value += content
          aiMessage.content += content

          // 批量更新DOM，减少重绘频率
          if (updateTimer) clearTimeout(updateTimer)
          updateTimer = setTimeout(() => {
            scrollToBottom()
          }, 50) // 50ms内的多次更新合并为一次
        }
        if (event === 'start') {
          taskId.value = data.taskId
        }
      }
      while (shouldContinue) {
        const { done, value } = await reader.read()
        if (done) {
          console.log('流式响应结束')
          // typingBuffer.value = ''
          loading.value = false
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        // console.log('chunk:', chunk)
        const lines = chunk.split('\n')
        // console.log('lines:', lines)

        let eventLine = ''

        // 处理完整的事件流数据
        lines.forEach((line) => {
          // console.log('line:', line)
          if (line.startsWith('event:')) {
            eventLine = line.slice(6).trim()
          }
          if (line.startsWith('data:')) {
            const data = line.slice(5)
            // console.log('data:', data)

            if (data && data.trim() !== '') {
              const parsed = JSON.parse(data)

              handleEvent(eventLine, parsed)
            }
            eventLine = '' // Reset event line after processing
          } else if (line.includes('botId')) {
            const parsed = JSON.parse(line)
            handleEvent(eventLine, parsed)
          }
        })
      }
    } catch (error) {
      console.error('流式响应失败:', error)
      typingBuffer.value = ''
      // Toast('获取流式响应失败')
      loading.value = false
    }
  }
  const taskId = ref(null)
  const cancelRequest = async () => {
    try {
      controller.abort()
      messages.value.pop()
      messages.value.push({
        role: 'assistant',
        content: '您已停止生成了本次回答，请重新编辑问题 ~',
        conversationId: conversationId.value,
        botId: 'default',
        notShow: true,
      })
      scrollToBottom()
      if (!taskId.value) throw new Error('未找到任务ID')
      await cancelStreamChat({ taskId: taskId.value })
      loading.value = false
    } catch (error) {
      Toast('取消失败!')
    }
  }
  const showSyncFrom = ref(false)
  let formContent = ref({})
  // 总结学员画像 智能推荐教练 同步客户字段
  const handleTypeMessage = async (type) => {
    loading.value = true
    nothingShow.value = false
    if (type === 'customer_portrait') {
      // 总结学员画像逻辑
      // 添加用户消息
      messages.value.push({
        role: 'user',
        content: '总结学员画像',
        botId: 'customer_portrait',
      })
      scrollToBottom()
      replyMessage('总结学员画像', 'customer_portrait')
    } else if (type === 'recommend_coachs') {
      // 智能推荐教练逻辑
      try {
        messages.value.push({
          role: 'user',
          content: '智能推荐教练',
          botId: 'recommend_coachs',
        })
        showCancelBtn.value = false
        scrollToBottom()
        const resRecommend = await getChat({
          conversationId: conversationId.value,
          inputs: '智能推荐教练',
          contactId: contactId.value,
          botId: 'recommend_coachs',
          attachments: [],
        })
        console.log(resRecommend.data)
        messages.value.push(resRecommend.data)
        scrollToBottom()
        // getRecommendStreamChat() // 流式获取智能推荐教练
      } catch (err) {
        nothingShow.value = true
      } finally {
        showCancelBtn.value = true
        loading.value = false
      }
    } else if (type === 'customer_fields') {
      try {
        showCancelBtn.value = false
        const resSync = await getChat({
          conversationId: conversationId.value,
          inputs: '同步客户字段',
          contactId: contactId.value,
          botId: 'customer_fields',
          attachments: [],
        })
        // 同步客户字段逻辑
        if (resSync.data.contentType === 'json') {
          formContent.value = JSON.parse(resSync.data.content)
          console.log(JSON.parse(resSync.data.content))
        }
        formContent.value.contactId = contactId.value
        showSyncFrom.value = true
      } finally {
        showCancelBtn.value = true
        loading.value = false
      }
    }
  }

  // const getRecommendStreamChat = async () => {
  //   let content = ''
  //   const resRecommend1 = await getStreamChat({
  //     conversationId: conversationId.value,
  //     inputs: '智能推荐教练',
  //     botId: 'recommend_coachs',
  //     attachments: [],
  //   })
  //   const reader = resRecommend1.data
  //   const lines = reader.split('\n')
  //   const aiMessage = {
  //     conversationId: conversationId.value,
  //   }
  //   lines.forEach((line) => {
  //     if (line.startsWith('data:')) {
  //       const data = line.slice(5)
  //       if (data && data.trim() !== '') {
  //         const parsed = JSON.parse(data)
  //         if (parsed.content) {
  //           content += parsed.content
  //         }
  //         Object.assign(aiMessage, parsed, { content })
  //       }
  //     }
  //   })
  //   messages.value.push(aiMessage)
  //   scrollToBottom()
  // }

  // 教练推荐语
  const recommendParams = ref(null)
  const coachRecommendText = (data) => {
    let text = `根据当前的用户画像，帮我生成${data.coachName}教练的销售推荐语 ~`
    recommendParams.value = JSON.stringify(data)
    messages.value.push({
      role: 'user',
      content: text,
      botId: 'coach_recommend',
    })
    replyMessage(text, 'coach_recommend')
  }

  // 打标
  const markMessage = (msg, mark) => {
    console.log('打标', msg)
    getMark({
      messageId: msg.id,
      mark,
    }).then((res) => {
      console.log(res)
      msg.mark = res.data
    })
  }
  // 剪切板
  const clipboardData = (msg) => {
    const text = msg.content
    ww.value.setClipboardData({
      data: text,
      success: (res) => {
        console.log('复制成功', res)
      },
    })
  }

  // 复制文本区域内容到输入框
  const copyTextarea = (msg) => {
    if (msg.botId === 'default' || msg.botId === 'coach_recommend') {
      textareaRef.value?.focus() // 聚焦文本域
      message.value = msg.content
    } else {
      handleTypeMessage(msg.content, msg.botId)
    }
  }

  // 取消
  const cancel = () => {
    cancelRequest()
  }

  // 滚动到底部
  const scrollToBottom = (text) => {
    nextTick(() => {
      const container = messageListRef.value
      if (container) {
        // 兼容iOS和安卓的平滑滚动
        if ('scrollBehavior' in document.documentElement.style) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: text ? text : 'smooth',
          })
        } else {
          container.scrollTop = container.scrollHeight
        }
      }
    })
  }
  // 滚动到顶部
  const scrollToTop = () => {
    nextTick(() => {
      const container = messageListRef.value
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      }
    })
  }

  // 查找教练
  const findCoach = () => {
    ww.value.launchMiniprogram({
      appid: miniAppid,
      path: 'pages/category/category',
    })
  }
  // 打开教练详情页
  const openCoachDetail = (item) => {
    console.log(item)
    let path = item?.courseId
      ? `pages/coach/details/details?id=${item.coachId}&courseId=${item.courseId}`
      : 'pages/coach/details/details?id=' + item.coachId
    ww.value.launchMiniprogram({
      appid: miniAppid,
      path,
    })
  }

  // 会话历史
  const openHistory = () => {
    if (loading.value) return
    router.push({
      name: 'ChatHistory',
      query: {
        contactId: contactId.value,
      },
    })
  }
  // 处理JSON 字符串数据
  const handleJson = (json) => {
    return JSON.parse(json)
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    performanceMonitor.markMilestone('组件开始卸载')

    // 取消未完成的请求
    if (controller) {
      controller.abort()
    }

    // 清理定时器
    if (updateTimer) {
      clearTimeout(updateTimer)
      updateTimer = null
    }

    // 清理防抖和节流函数
    if (updateScrollBottomIcon.cancel) {
      updateScrollBottomIcon.cancel()
    }
    if (checkLoadHistory.cancel) {
      checkLoadHistory.cancel()
    }

    // 清理大量消息数据，释放内存
    if (messages.value.length > 50) {
      console.log(`🧹 [内存清理] 组件卸载时清理${messages.value.length}条消息`)
      messages.value = []
    }

    performanceMonitor.markMilestone('组件卸载完成')
  })
</script>

<style scoped lang="scss">
  .chat-container {
    position: relative;
    display: flex;
    flex-direction: column;
    height: calc(100vh - constant(safe-area-inset-bottom));
    height: calc(100vh - env(safe-area-inset-bottom));
    background: #f5f6fa;
    .scroll_bottom_icon {
      z-index: 19;
      position: absolute; /* 绝对定位 */
      left: 50%;
      transform: translateX(-50%);
      bottom: 1.5rem;
      img {
        width: 0.68rem;
        height: 0.68rem;
      }
    }
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.14rem;
    position: relative;
    background-size: cover;

    .ai-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 0.32rem;
        height: 0.33rem;
        margin-right: 0.06rem;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .info {
        .name {
          font-weight: 500;
          font-size: 0.14rem;
          color: #333333;
        }

        .sub-title {
          font-weight: 400;
          font-size: 0.12rem;
          color: #909399;
        }
      }
    }

    .history {
      display: flex;
      align-items: center;
      .history_icon {
        width: 0.32rem;
        height: 0.32rem;
      }
    }
  }

  .chat-content {
    -webkit-overflow-scrolling: touch; /* iOS弹性滚动 */
    overscroll-behavior: contain; /* 防止滚动穿透 */
    flex: 1;
    overflow-y: auto;
    padding: 0.16rem;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
    overflow-anchor: none;
    will-change: scroll-position; /* 提示浏览器优化滚动 */
    contain: layout style paint; /* 限制重排重绘范围 */

    .message-count-tip {
      text-align: center;
      font-size: 0.12rem;
      color: #999;
      padding: 0.08rem 0;
      margin-bottom: 0.12rem;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 0.12rem;
    }

    .message {
      margin-bottom: 0.16rem;
      display: flex;

      &.assistant-message {
        justify-content: flex-start;
        .message-content {
          min-width: 40%;
          background: #fff;
          border-radius: 0.16rem 0.16rem 0.16rem 0.02rem;
          color: #333;
          padding-top: 0;
        }
      }

      &.user-message {
        justify-content: flex-end;
        .message-content {
          background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
          border-radius: 0.16rem 0.16rem 0.02rem 0.16rem;
          color: #ffffff;
          .user-content {
            text-decoration-line: underline;
            min-width: 0.2rem;
          }
        }
      }
      .recommend-content {
        min-width: 100% !important;
        padding-top: 0.15rem !important;
      }
      .recommend_coach_header {
        width: 100%;
        height: 0.96rem;
        position: absolute;
        top: 0;
        border: #fff solid 1px;
        left: 0;
        z-index: 1;
        object-fit: cover;
        border-radius: 0.12rem 0.12rem 0 0;
      }
      .recommend-title {
        position: relative;
        z-index: 2;
        font-size: 0.15rem;
        color: #333;
        margin-bottom: 0.11rem;
        font-weight: 500;
      }
      .find-more-coach {
        height: 0.48rem;
        background: #f4f6fc;
        border-radius: 0.08rem;
        margin-bottom: 0.12rem;
        margin-top: 0.16rem;
        padding: 0 0.12rem;
        .des {
          font-weight: 400;
          font-size: 0.14rem;
          color: #1a1b1d;
          line-height: 0.2rem;
        }
        .btn {
          background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
          line-height: 0.3rem;
          height: 0.3rem;
          padding: 0.08rem 0.14rem;
          border-radius: 0.2rem;
        }
      }
      .recommend-list {
        background-color: #fff;
        padding: 0.1rem 0;
        position: relative;
        z-index: 10;

        &:not(:last-of-type) {
          border-bottom: 0.01rem solid #ebebeb;
        }
        .coach-image {
          width: 0.48rem;
          height: 0.48rem;
          border-radius: 0.04rem;
          object-fit: cover;
        }
        .coach-card {
          margin-left: 0.08rem;
          .name {
            font-weight: 500;
            font-size: 0.15rem;
            color: #1a1b1d;
          }
          .teach-title {
            font-weight: 400;
            font-size: 0.12rem;
            color: #969699;
          }
          .price {
            font-weight: 400;
            color: #ff6445;
            .strong {
              font-size: 0.14rem;
              font-weight: 600;
            }
          }
        }
        .coach-recommend-btn {
          height: 0.3rem;
          line-height: 0.3rem;
          font-weight: 500;
          white-space: nowrap;
        }
      }
      .message-content {
        position: relative;
        padding: 0.15rem;
        max-width: 100%;
        word-wrap: break-word;
        // font-size: 0.15rem;

        .assistant-message & {
          background: #f0f7ff;
        }
        .message-bottom {
          margin-top: 0.12rem;
          padding-top: 0.1rem;
          border-top: 0.01rem solid #ebebeb;
          .message-icon {
            width: 0.2rem;
            height: 0.2rem;
            img {
              width: 100%;
              height: 100%;
            }
            .dislike {
              margin-left: 0.12rem;
            }
          }
        }

        .student-info {
          h4 {
            font-size: 0.15rem;
            color: #333;
            margin: 0.12rem 0 0.08rem;
            font-weight: 500;

            &:first-child {
              margin-top: 0;
            }
          }

          p {
            font-size: 0.14rem;
            color: #666;
            line-height: 1.5;
            margin: 0;
          }
        }

        .user-message & {
          background: #e8f5e9;
        }
      }
    }
    .message-empty {
      position: relative;
      background-color: #fff;
      font-weight: 400;
      font-size: 0.14rem;
      color: #333333;
      line-height: 0.24rem;
      border-radius: 0.16rem 0.16rem 0.16rem 0;
      .empty-container {
        position: relative;
        z-index: 2;
        padding: 0.14rem 0.5rem 0.02rem 0.15rem;
        .nothing-icon {
          width: 1.2rem;
          height: 1rem;
          margin: 0 auto;
          display: block;
        }
        .nothing-text {
          text-align: center;
          font-size: 0.12rem;
          color: #616568;
          line-height: 0.17rem;
          margin: 0.08rem 0 0.4rem;
        }
      }
      .empty-title {
        margin-bottom: 0.13rem;
      }
      .empty-bottom {
        color: #606266;
        padding-left: 0.15rem;
        width: 100%;
        height: 0.59rem;
        background: linear-gradient(rgba(250, 237, 225, 0) 0%, #fefbf9 100%);
        border-radius: 0px 0.16rem 0px 0px;
      }
      .empty_icon {
        position: absolute;
        z-index: 1;
        right: 0;
        bottom: 0;
        width: 1.16rem;
        height: 1.16rem;
      }
    }
  }

  .bottom-actions {
    background: #fff;
    padding: 0.12rem 0.16rem;
    border-top: 0.01rem solid #eee;

    .action-buttons {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.12rem;

      .action-btn {
        display: flex;
        align-items: center;
        border: 1px solid #eef0f6;
        border-radius: 0.17rem;
        padding: 0.05rem 0.1rem 0.05rem 0.06rem;
        cursor: pointer;

        img {
          width: 0.22rem;
          height: 0.22rem;
        }

        span {
          font-size: 0.11rem;
          font-weight: 500;
          color: #333333;
        }
      }
    }

    .input-area {
      // padding: 0 0.12rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .input-wrapper {
        display: flex;
        align-items: center;
        width: 100%;

        .chat-input {
          -webkit-appearance: none; /* 移除iOS默认样式 */
          -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          padding: 0.14rem 0.16rem;
          margin: 0 0.08rem;
          font-size: 0.14rem;
          color: #333;
          background: #f5f6fa;
          border-radius: 0.24rem;
          resize: none;
          max-height: 120px;
          overflow-y: hidden;
          line-height: 1.5;
          transition: height 0.2s;

          &:focus {
            overflow-y: auto;
          }

          &::placeholder {
            color: #999;
          }
        }
      }
      .send-btn,
      .chat-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 0 0.08rem;
        cursor: pointer;
        position: relative;
        .popover {
          position: absolute;
          z-index: 9999;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 0.12rem;
          border-radius: 0.08rem;
          box-shadow: 0 0.04rem 0.12rem rgba(0, 0, 0, 0.15);
          left: 0;
          transform: translateY(-110%);

          .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
            border-width: 0.06rem;
            left: 0.2rem;
            bottom: 0.01rem;
            border-top-color: rgba(0, 0, 0, 0.8);
            border-bottom-width: 0;
            transform: translate(-50%);
            margin-bottom: calc(0.06rem * -1);
          }
          .text {
            display: flex;
            font-size: 0.12rem;
            line-height: 1.5;
            white-space: nowrap;
          }
        }

        img {
          width: 0.32rem;
          height: 0.32rem;
        }
      }
    }
  }
  .recommend-empty {
    padding: 0.26rem 0 0.3rem;
    border-radius: 0.16rem 0.16rem 0.16rem 0;
    background-color: #fff;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .nothing_icon {
      width: 1.2rem;
      height: 1.2rem;
    }
    .text {
      font-size: 0.12rem;
      color: #616568;
      margin: 0.06rem 0 0.24rem;
    }
    .btn {
      width: 2rem;
      background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
      border-radius: 0.2rem;
    }
  }

  .disabled-bottom-actions {
    opacity: 0.5;
    pointer-events: none;
  }
  .wrapper {
    display: flex;
    justify-content: center;
    height: 100%;
    padding-top: 50%;
    .create-popup {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 2.9rem;
      height: 2.18rem;
      background-size: cover;
      .create-popup-title {
        margin-top: 1.13rem;
        font-weight: 500;
        font-size: 15px;
        color: #333333;
      }
      .btn {
        height: 0.4rem;
        padding: 0 0.4rem;
        margin-top: 0.28rem;
      }
      .confirm-btn {
        background: linear-gradient(90deg, #4687fc 0%, #5845fc 100%);
      }
    }
  }
</style>

