<template>
  <div class="search-head">
    <!-- TODO: 封装一个 geolocation（地理位置） 组件-->
    <div class="geolocation-box">
      <geolocation />
    </div>

    <div class="header-search-form">
      <div class="search-input-left-icon">
        <i class="icon-search"></i>
      </div>
      <div class="search-input">
        <input
          ref="inputRef"
          v-model="searchValue"
          @keyup.enter="onSearch"
          :placeholder="placeholder"
          maxlength="20"
        />
      </div>
      <button class="i-button search-submit-btn" v-preventReClick @click="onSearch">搜索</button>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import Geolocation from './Geolocation'

  let props = defineProps({
    placeholder: {
      type: String,
      default: '搜索感兴趣的教练',
    },
    autofocus: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['search'])

  const searchValue = ref('')
  const inputRef = ref(null)

  const inputAutofocus = () => {
    inputRef.value.focus()
  }

  const setInputValue = (value) => {
    searchValue.value = value
  }

  const onSearch = () => {
    emit('search', searchValue.value)
  }

  onMounted(() => {
    if (props.autofocus) {
      inputAutofocus()
    }
  })

  defineExpose({
    setInputValue,
  })
</script>

<style lang="scss" scoped>
  .search-head {
    display: flex;
    align-items: center;
    padding: 0.08rem 0.15rem;
  }

  .header-search-form {
    flex: 1;
    background: #f3f3f3;
    border-radius: 0.17rem;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0.02rem 0;
  }

  .search-input-left-icon {
    width: 0.18rem;
    height: 0.18rem;
    margin-left: 0.1rem;
  }

  .icon-search {
    width: 0.18rem;
    height: 0.18rem;
    display: block;
    background: url('../../../assets/images/icon/icon-search.png') no-repeat;
    background-size: 100% 100%;
  }

  .search-input {
    margin-left: 0.08rem;
    flex: 1;
    padding-right: 0.55rem;

    input {
      width: 100%;
      height: 0.3rem;
      color: #232326;
      border: none;
      font-size: 0.12rem;
      background: transparent;

      &::placeholder {
        color: #b2b1b7;
      }
    }
  }

  .search-submit-btn {
    width: 0.48rem;
    height: 0.3rem;
    color: #ffffff;
    background-color: #ff9b26;
    position: absolute;
    right: 0.02rem;
    top: 0.02rem;
    border-radius: 0.17rem;
    user-select: none;
  }
</style>
