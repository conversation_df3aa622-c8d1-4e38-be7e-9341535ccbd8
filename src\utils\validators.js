// 提供给 async-validator 插件的字段验证函数

import { validate } from '@/utils/validate'

// 验证手机号码
export function validateMobile(rule, value, callback) {
  if (!value) {
    callback('请输入手机号码')
  }

  if (!validate('mobile', value)) {
    callback('手机号码格式不正确')
  }

  callback()
}

// 验证字符串是否为空
export const validateStrIsEmpty = (rule, value, callback) => {
  if (value === undefined || value === null) {
    callback(rule?.message || '请输入')
  }

  if (typeof value === 'string') {
    if (value.trim() !== '') {
      callback()
    } else {
      callback(rule?.message || '请输入')
    }
  }
}
