<template>
  <div class="comment-item">
    <div class="head-portrait">
      <ijl-image
        round
        width="0.32rem"
        height="0.32rem"
        fit="cover"
        :src="comment.anonymousState ? '/user/default_man_3x.png' : comment.authorUserInfo.headImg"
      />
    </div>
    <div class="content-box">
      <!-- 评论人 -->
      <div class="username omit">
        <template v-if="comment.anonymousState">匿名用户</template>
        <template v-else>{{ comment.authorUserInfo.userName }}</template>
      </div>
      <!-- 评论内容 -->
      <div class="content">{{ comment.content }}</div>
      <!-- 评论中的图标 -->
      <div v-if="isHaveImages" class="content-images">
        <image-preview-wrapper multiple>
          <ijl-image
            class="content-image"
            v-for="url in comment.pictureList"
            :key="url"
            width="0.72rem"
            height="0.72rem"
            fit="cover"
            :src="url"
          />
        </image-preview-wrapper>
      </div>
      <!-- 评论中的其他数据展示区 -->
      <div class="content-other">
        <div class="content-time">{{ formatCommentTime(comment.createTime) }}</div>
        <div class="reply-comment" @click="onReply(comment.commentId, comment)">
          <i class="icon-reply"></i>
          <span>回复</span>
        </div>
      </div>
      <!-- 子评论 -->
      <div v-if="showSubComment" class="sub-comment-wrapper">
        <div
          class="sub-comment-list"
          v-for="subComment in subCommentList"
          :key="subComment.replyId"
        >
          <div class="sub-comment-item">
            <div class="head-portrait">
              <ijl-image round fit="cover" :src="subComment.authorUserInfo.headImg" />
            </div>
            <div class="content-box">
              <div class="username">
                <span>{{ subComment.authorUserInfo.userName }}</span>
                <span v-if="subComment.replyUserInfo.userId" class="arrow-username">
                  {{ subComment.replyUserInfo.userName }}
                </span>
              </div>
              <div class="content" v-html="subComment.content" />
              <div class="content-other">
                <div class="content-time">{{ formatCommentTime(subComment.createTime) }}</div>
                <div class="reply-comment" @click="onSubCommentReply(subComment.replyId)">
                  <i class="icon-reply" />
                  <span>回复</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button v-if="comment.replyCount > 2" class="fetch-more" @click="onLookAllReply">
          查看全部{{ comment.replyCount }}条回复<van-icon name="arrow" />
        </button>
      </div>
    </div>
    <reply-comment ref="replyCommentBoxRef" @submit="onSubmitComment" />
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import ReplyComment from './reply-comment'
  import { formatCommentTime, isLogin, toLogin } from '@/common'
  import { reqUserReplyComment } from '@/api/user-server'
  import { Toast } from 'vant'

  const props = defineProps({
    comment: {
      type: Object,
      default() {
        return {}
      },
    },
    hideSubComment: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['lookAllReply', 'reply', 'subCommentReply', 'addSubComment'])

  const replyCommentBoxRef = ref(null)
  let replayCommentLevel = 1 // 1 回复一级评论，2 回复一级评论的子评论
  let replaySubCommentId = null // 回复的子评论id

  const subCommentList = computed(() => {
    let cloneList = JSON.parse(JSON.stringify(props.comment.replyListVO))
    return cloneList.splice(0, 2)
  })

  const showSubComment = computed(() => {
    return (
      props.comment?.replyListVO && props.comment?.replyListVO?.length > 0 && !props.hideSubComment
    )
  })

  const isHaveImages = computed(() => {
    return props.comment?.pictureList && props.comment?.pictureList.length > 0
  })

  const onReply = (commentId, comment) => {
    if (isLogin()) {
      replayCommentLevel = 1
      replyCommentBoxRef.value?.open()
      emit('reply', { commentId, comment })
    } else {
      Toast('你尚未登录，登录后即可发布评论')
      toLogin()
    }
  }

  const onSubCommentReply = (replyId) => {
    if (isLogin()) {
      replaySubCommentId = replyId
      replayCommentLevel = 2
      replyCommentBoxRef.value?.open()
    } else {
      Toast('你尚未登录，登录后即可发布评论')
      toLogin()
    }
  }

  // 提交评论
  const onSubmitComment = (value) => {
    let params = {
      replyId: replayCommentLevel === 2 ? replaySubCommentId : null,
      commentId: props.comment.commentId,
      content: value,
    }
    reqUserReplyComment(params).then((res) => {
      const { data } = res
      replyCommentBoxRef.value?.close()
      replyCommentBoxRef.value?.reset()
      Toast('发布成功')
      emit('addSubComment', data)
    })
  }

  // 查看单个评论的所有回复列表
  const onLookAllReply = () => {
    emit('lookAllReply', {
      comment: props.comment,
    })
  }
</script>

<style lang="scss" scoped>
  @import './index';
</style>
