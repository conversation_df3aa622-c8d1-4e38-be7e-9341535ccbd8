<template>
  <page navigationBarType="none">
    <template #page>
      <div class="sign-up">
        <div class="notify"><span class="emoji">😃</span>欢迎加入爱教练</div>

        <div class="steps">
          <div class="step step-one" v-if="step === 1">
            <div class="step-desc">选择你想成为的用户角色？</div>
            <div class="step-content">
              <div class="roles">
                <div
                  class="role"
                  v-for="(role, name) in roles"
                  :key="name"
                  @click="toggleRole(role.code)"
                  :class="{ 'role-active': selectedRole === role.code }"
                >
                  <div class="role-illustration">
                    <img :src="role.img" alt="" />
                  </div>
                  <div class="role-desc">
                    <p class="role-name">{{ role.name }}</p>
                    <p class="role-can-do">
                      {{ role.desc }}
                    </p>
                  </div>
                  <div class="arrow">
                    <Icon name="arrow" size=".16rem" color="#AEA9A9" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="step step-two" v-if="step === 2">
            <div class="lastStep" @click="step--">
              <span>
                <van-icon name="arrow-left" />
                上一步
              </span>
            </div>
            <div class="step-desc">{{ rolesTip }}</div>
            <div class="step-content">
              <Form class="form">
                <Field
                  v-model="formData.mobile"
                  maxlength="11"
                  label="手机号"
                  type="number"
                  placeholder="请输入手机号"
                  @blur="mobileBlur"
                />
                <div v-if="isShowPhoneTip" class="error">
                  <i class="icon icon-error-tip" />
                  <span>手机号码已注册</span>
                  <span style="color: #3d7fff" @click="toLogin">立刻登录</span>
                </div>
                <!-- <ErrorTip v-if="isShowPhoneTip" tipTxt="手机号码不正确" /> -->
                <Field
                  v-model="formData.imageCode"
                  label="图形验证码"
                  placeholder="请输入图形验证码"
                >
                  <template #button>
                    <ImageCaptcha class="image-Captcha" />
                  </template>
                </Field>
                <Field v-model="formData.verifyCode" label="验证码" placeholder="请输入验证码">
                  <template #button>
                    <span v-if="isGetCaptcha" class="field-right" @click="getVerifyCode">
                      获取验证码
                    </span>
                    <CountDown
                      class="count-down"
                      v-else
                      :time="time"
                      format="ss 秒"
                      @finish="handleCountDownFinish"
                    />
                  </template>
                </Field>
              </Form>
            </div>
          </div>
        </div>

        <div class="fixed-bottom">
          <Checkbox v-if="step === 2" v-model="checked" checked-color="#FF9B26" shape="square">
            我已阅读并同意
            <span class="protocol" @click="$router.push({ path: '/help', query: { tabIndex: 3 } })">
              《用户协议》
            </span>
            <span class="protocol" @click="$router.push({ path: '/help', query: { tabIndex: 4 } })">
              《隐私协议》
            </span>
          </Checkbox>
          <!-- <button class="next-step" :disabled="disabled" @click="nextStep">
            下一步（{{ step }}/3）
          </button> -->
          <button class="next-step" :disabled="disabled" @click="nextStep">
            {{ step === 1 ? '去注册' : '注册' }}
          </button>
        </div>
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'signUp' }
</script>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { useRouter, onBeforeRouteLeave } from 'vue-router'
  import Schema from 'async-validator'
  import { Icon, Form, Field, CountDown, Checkbox, Toast } from 'vant'
  import ImageCaptcha from '@/components/image-captcha'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'
  import { sendRegistration } from '@/api/auth-server'
  import { useSendLoginRegisterSMSCode } from '@/use/useSendVerifyCode'
  import { checkPhone, checkEmpty } from '@/utils/validate'
  import { verifyUserInfoByMobile } from '@/api/user-server'
  import useKeepAliveStore from '@/store/keepAlive'
  import { isStringValid } from '@/utils/is'

  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()

  onBeforeRouteLeave((to) => {
    let pages = ['help']
    if (!pages.includes(to.name)) {
      // 卸载缓存
      keepAliveStore.removeKeepAlive('signUp')
    }
  })

  // 当前选择的角色
  let selectedRole = ref(null)

  let rolesTip = ref(null)

  // 当前进行的步骤数
  let step = ref(1)

  const checked = ref(false)
  const isShowPhoneTip = ref(false)

  const disabled = computed(() => {
    if (step.value === 1) {
      return !selectedRole.value
    }

    if (step.value === 2) {
      return !checked.value
    }

    return false
  })

  const roles = {
    member: {
      code: 'member',
      name: '普通用户',
      img: require('@/assets/images/logoView/general-user.png'),
      desc: '注册后，您可获取专业教练、专业场馆、专业教学视频等信息。',
      // successUrl: "/sign-up-success",
      successUrl: '/personal-info',
    },
    coach: {
      code: 'coach',
      name: '教练用户',
      img: require('@/assets/images/logoView/coach-user.png'),
      desc: '注册后，您可获取学员资源、专业营销推广工具、与学员沟通排课等。',
      successUrl: '/coach/intro',
    },
    venues: {
      code: 'venues',
      name: '场馆用户',
      img: require('@/assets/images/logoView/shop-owner-user.png'),
      desc: '注册后，您可获取订场用户、专业营销推广工具、发布场馆时事等。',
      successUrl: '/account/shop-apply-form',
    },
  }

  const formData = reactive({
    mobile: '',
    verifyCode: '',
    imageCode: '',
    identity: '', // 用户注册身份
  })

  let validator = new Schema({
    mobile: { message: '请输入手机号', validator: checkPhone },
    imageCode: { message: '请输入图形验证码', validator: checkEmpty },
    verifyCode: { message: '请输入短信验证码', validator: checkEmpty },
  })

  const toLogin = () => {
    router.push({ path: '/login' })
  }

  const time = ref(60 * 1000)

  const isGetCaptcha = ref(true)

  // 倒计时结束后
  const handleCountDownFinish = () => {
    isGetCaptcha.value = true
  }

  // 获取验证码
  const getVerifyCode = () => {
    useSendLoginRegisterSMSCode(formData, () => {
      isGetCaptcha.value = false
      Toast('验证码发送成功')
    })
  }

  // 选择角色
  const toggleRole = (identityCode) => {
    selectedRole.value = identityCode

    if (selectedRole.value === 'member') {
      rolesTip.value = '普通用户注册'
    } else if (selectedRole.value === 'coach') {
      rolesTip.value = '教练用户注册'
    } else if (selectedRole.value === 'venues') {
      rolesTip.value = '场馆用户注册'
    } else {
      rolesTip.value = ''
    }
  }

  // 下一步
  const nextStep = () => {
    if (step.value === 2) {
      signUpSubmit()
      return
    }

    step.value++
  }

  // 注册提交
  const signUpSubmit = () => {
    validator
      .validate(formData)
      .then(() => {
        let form = JSON.parse(JSON.stringify(formData))
        form.identity = selectedRole.value
        form.unionId = sessionProxyStorage.weChatUId // 用户微信 unionId

        // 有邀约码则携带上
        if (isStringValid(sessionProxyStorage.shareId)) {
          form.ijlShare = {
            shareId: sessionProxyStorage.shareId.replace(/ /g, '+'),
          }
        }

        delete form.imageCode

        sendRegistration(form)
          .then((res) => {
            localProxyStorage.user = res.data

            setTimeout(() => {
              router.push({
                path: roles[form.identity].successUrl,
                query: {
                  channel: 'signUp',
                },
              })
            }, 500)
          })
          .catch(() => {})
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  const mobileBlur = (e) => {
    verifyUserInfoByMobile({ mobile: formData.mobile }).then((res) => {
      console.log(res)
      isShowPhoneTip.value = res.data ? true : false
    })
    // if (!validate("mobile", formData.mobile) && formData.mobile) {
    //   mobileTxt.value = "手机号码不正确";
    //   isShowPhoneTip.value = true;
    // } else {
    //   isShowPhoneTip.value = false;
    // }
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';

  @include Icon('error-tip', 0.13rem, 0.13rem);
  @include Icon('arrow-left', 0.4rem, 0.4rem);
  .error {
    display: flex;
    align-items: center;
    padding-top: 0.02rem;
    span {
      margin-left: 0.05rem;
      font-size: 0.12rem;
      color: #ff6445;
    }
  }
  .sign-up {
    background-color: #fff;
    min-height: 100vh;
    padding: 0.15rem 0.15rem 1.5rem 0.15rem;
  }

  .notify {
    line-height: 0.34rem;
    font-size: 0.12rem;
    color: #7f581e;
    text-align: center;
    background: rgba(245, 176, 76, 0.1);
    border-radius: 0.05rem;

    .emoji {
      display: inline-block;
      margin-right: 0.05rem;
    }
  }

  .steps {
    padding-top: 0.3rem;
  }

  .step-title {
    font-size: 0.14rem;
    color: #666666;
    text-align: center;
  }

  .step-desc {
    margin-top: 0.15rem;
    font-size: 0.16rem;
    font-weight: 600;
    color: #1a1b1d;
    text-align: center;
  }

  .step-one {
    .roles {
      margin-top: 0.35rem;

      .role {
        display: flex;
        height: 0.9rem;
        background: #f5f6f9;
        box-shadow: 0 0.02rem 0.04rem 0 rgba(77, 77, 77, 0.1);
        border-radius: 10px;
        align-content: center;
        margin-bottom: 0.16rem;
        cursor: pointer;
        position: relative;

        .role-illustration {
          width: 1.02rem;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .role-desc {
          margin-left: 0.9rem;
          flex: 1;
          padding: 0.13rem 0 0.13rem 0;
        }

        .role-name {
          font-size: 0.16rem;
          font-weight: 600;
          color: #1a1b1d;
        }

        .role-can-do {
          margin-top: 0.05rem;
          padding-right: 0.26rem;
          font-size: 0.12rem;
          color: #666666;
        }

        .arrow {
          width: 0.32rem;
          height: 0.32rem;
          margin-top: 0.29rem;
          line-height: 0.32rem;
          text-align: center;
        }
      }

      .role-active {
        background: linear-gradient(135deg, #ff9a3b 0%, #ffca32 100%);

        .role-name,
        .role-can-do {
          color: #fff;
        }

        :deep(.van-icon-arrow) {
          color: #fff !important;
        }
      }
    }
  }

  .step-two {
    position: relative;
    .step-content {
      padding-top: 0.22rem;
    }
    .lastStep {
      height: 0.01rem;
      span {
        position: absolute;
        top: -5%;
        left: 0%;
        font-size: 0.1rem;
        color: #616568;
      }
    }
  }

  .form {
    :deep(.van-cell) {
      padding: 0.17rem 0 0.17rem 0;
      font-size: 0.14rem;
      border-bottom: 1px solid #eeeeee;
      align-items: center;

      &:after {
        border: none;
      }
    }

    :deep(.van-field__label) {
      width: 1.08rem;
      padding-left: 0.1rem;
      color: #453938;
    }
  }

  .image-Captcha {
    width: 1rem;
    height: 0.42rem;
    border: 1px solid #ebebeb;
    position: absolute;
    top: -0.08rem;
    right: 0;
  }

  .field-right {
    color: #e02020;
    font-size: 0.14rem;
  }

  .protocol {
    color: #e02525;
  }

  .fixed-bottom {
    position: fixed;
    width: 3.45rem;
    bottom: 0;
    background: #fff;
    padding-top: 0.1rem;
    //height: 0.75rem;

    .next-step {
      width: 3.45rem;
      height: 0.45rem;
      margin: 0.15rem 0;
      font-size: 0.17rem;
      color: #ffffff;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.23rem;
      cursor: pointer;

      &:disabled {
        background: #999999;
      }
    }
  }
</style>
