<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content">
        <div class="list-wrap">
          <van-list
            class="student-list"
            v-model:loading="loading"
            :finished="finished"
            @load="getStudentList"
          >
            <div
              v-for="item in students"
              :key="item"
              class="student"
              @click="toStudentsDetails(item)"
            >
              <div class="student-top">
                <div>
                  <span class="label">剩余课时：</span
                  ><span class="value">{{ item.showRemainQuantity }}个课时</span>
                </div>
                <div class="student-state">{{ item.studyStatus.statusName }}</div>
              </div>
              <div class="student-bottom">
                <div class="student-info">
                  <div class="student-headshots">
                    <img :src="ossURLJoin(item.studentInfo.avatarUrl)" alt="" />
                  </div>
                  <div class="order-info">
                    <div class="student-name">{{ item.studentInfo.studentName }}</div>
                    <div class="order-time">
                      <template v-if="item.studyStatus.status === 'WAITING_CLASS'">
                        下单时间：{{ sliceStr(item.orderTime, 0, 10) }}
                      </template>
                      <template v-else
                        >最近上课：{{ sliceStr(item.lastConsumeTime, 0, 10) }}
                      </template>
                    </div>
                  </div>
                </div>
                <div class="operation">
                  <!-- <a @click.stop :href="'tel:' + item.studentInfo.mobile">
                    <button class="btn contact-btn">联系学员</button>
                  </a> -->
                  <!-- 已结束不展示核销课时 -->
                  <button
                    v-if="item.studyStatus.status !== 'FINISH'"
                    class="btn verification-btn"
                    @click.stop="toStudentsDetails(item)"
                  >
                    核销课时
                  </button>
                </div>
              </div>
            </div>
          </van-list>
          <empty v-if="emptyShow" top="2.4rem" description="暂无学员购买课时哦～" />
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import Empty from '@/components/empty'
  import { getCoachStudentList } from '@/api/coach-worktable'
  import { ossURLJoin } from '@/common'
  import { sliceStr } from '@/utils'

  const router = useRouter()
  let students = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const emptyShow = ref(false)

  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  const getStudentList = () => {
    pagination.pageNum += 1
    let params = { ...pagination }
    getCoachStudentList(params).then((res) => {
      const { data } = res
      students.value = students.value.concat(data)
      emptyShow.value = students.value.length === 0
      // 加载结束
      loading.value = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        finished.value = true
      }
    })
  }

  const toStudentsDetails = (rowData) => {
    router.push({
      name: 'myWorktableStudentsDetails',
      query: {
        studentUserId: rowData.studentInfo.studentUserId,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .student-list {
    margin: 0 0.08rem;
    padding-top: 0.08rem;
  }

  .student {
    width: 100%;
    margin-bottom: 0.08rem;
    padding: 0 0.15rem;
    background: #fff;
    border-radius: 0.06rem;

    .student-top {
      padding: 0.1rem 0;
      border-bottom: 1px solid #eeeeee;
      font-size: 0.14rem;
      display: flex;
      justify-content: space-between;

      .label {
        color: #979797;
      }

      .value {
        color: #1f1f1f;
      }

      .student-state {
        color: #ff9b26;
      }
    }

    .student-headshots {
      width: 0.5rem;
      height: 0.5rem;
      margin-right: 0.1rem;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .student-info {
      padding: 0.1rem 0;
      display: flex;
    }

    .student-name {
      font-size: 0.14rem;
      color: #1a1b1d;
      margin-top: 0.04rem;
    }

    .order-time {
      margin-top: 0.05rem;
      font-size: 0.12rem;
      color: #616568;
    }

    .operation {
      padding-bottom: 0.14rem;
      text-align: right;

      .btn {
        height: 0.32rem;
        border-radius: 0.16rem;
        padding: 0.06rem 0.18rem;
        margin-left: 0.12rem;
      }

      .contact-btn {
        color: #ff9b26;
        border: 1px solid #ff9b26;
      }

      .verification-btn {
        background: #ff9b26;
        color: #ffffff;
      }
    }
  }
</style>
