/* eslint-disable no-undef */
/**
 * 高德地图定位
 * @type {{}}
 */
import AMapLoader from '@amap/amap-jsapi-loader'

const location = {
  initMap() {
    console.log(123)
    let geolocation
    const AMap = AMapLoader.load({
      key: '80912f26af58c13f0cfd7d4ff10dfb1e', // 申请好的Web端开发者Key，首次调用 load 时必填
      // version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [''], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
    }).then((res) => {
      return res
    })
    const mapObj = new AMap.Map('iCenter')
    AMap.plugin('AMap.Geolocation', function () {
      geolocation = new AMap.Geolocation({
        // 是否使用高精度定位，默认：true
        enableHighAccuracy: true,
        // 设置定位超时时间，默认：无穷大
        timeout: 10000,
        // 定位按钮的停靠位置的偏移量
        offset: [10, 20],
        //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        zoomToAccuracy: true,
        //  定位按钮的排放位置,  RB表示右下
        position: 'RB',
      })

      mapObj.addControl(geolocation)

      geolocation.getCurrentPosition(function (status, result) {
        console.log(2222, status, result)
        //   if (status == "complete") {
        //     // console.log(result.position.lng, "lng");
        //     // console.log(result.position.lat, "lat");
        //     console.log(calculateDiscount(23.176004, 113.493534, 23.163275, 113.403184));
        //     onComplete(result);
        //   } else {
        //     onError(result);
        //   }
        // });

        // function onComplete(data) {
        //   // data是具体的定位信息
        //   console.log(data, "onComplete");
        // }

        // function onError(data) {
        //   console.log(data, "onError");
        //   // 定位出错
        // }
      })
    })
    console.log(geolocation, 'geolocation1')
    return geolocation
  },
}
export default location
