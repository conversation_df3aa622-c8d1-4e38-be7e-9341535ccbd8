<template>
  <van-overlay class="ap-guide-dialog" :show="show">
    <div class="ap-guide-dialog-content">
      <div class="we-chat-tip"></div>
      <div class="we-chat-tip-content">
        请点击右上角<br />
        选择在浏览器打开，以完成支付
      </div>
      <div v-show="showButton" class="operation">
        <div><button class="confirm" @click="onPaySuccess">我已付款</button></div>
        <div><button class="cancel" @click="onPayCancel">取消付款</button></div>
      </div>
    </div>
  </van-overlay>

  <!--  支付成功弹窗 -->
  <i-dialog
    v-model:show="paySuccessDialog"
    showCancelButton
    @confirm="toOrderDetails"
    @cancel="toHome"
    confirmButtonText="查看订单"
    cancelButtonText="返回首页"
  >
    <div class="dialog-content">
      <i class="icon-success"></i>
      <p class="desc">支付成功</p>
    </div>
  </i-dialog>

  <i-dialog
    v-model:show="noPaySuccessDialog"
    showCancelButton
    confirmButtonText="继续支付"
    cancelButtonText="暂不支付"
    @confirm="onContinuePay"
    @cancel="toOrderDetails"
  >
    <div class="dialog-content">
      <i class="icon-error"></i>
      <p class="desc">订单未支付</p>
    </div>
  </i-dialog>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { isWeChat } from '@/utils'
  import IDialog from '@/components/dialog'
  import alipay from '../../../public/static/alipay/ap'
  import { reqCheckOrderPayStatus } from '@/api/pay'
  import { Toast } from 'vant'

  const router = useRouter()
  const route = useRoute()
  const show = ref(false)
  const paySuccessDialog = ref(false)
  const noPaySuccessDialog = ref(false)
  const showButton = ref(false)
  const orderNo = route.query.orderNo

  // 检查订单支付状态
  const checkOrderPayStatus = () => {
    return new Promise((resolve) => {
      const params = { orderId: orderNo }
      reqCheckOrderPayStatus(params).then((res) => {
        resolve(res.data)
      })
    })
  }

  const onPayCancel = async () => {
    let orderStatus = await checkOrderPayStatus()
    if (orderStatus) {
      const toast = Toast.loading({
        duration: 0,
        forbidClick: true,
        message: '订单已支付成功，3 秒后即将跳去订单详情页',
      })

      let second = 3
      const timer = setInterval(() => {
        second--
        if (second) {
          toast.message = `订单已支付成功，${second} 秒后即将跳去订单详情页`
        } else {
          clearInterval(timer)
          Toast.clear()
          toOrderDetails()
        }
      }, 1000)
    } else {
      toOrderDetails()
    }
  }

  const onPaySuccess = async () => {
    let orderStatus = await checkOrderPayStatus()
    if (orderStatus) {
      show.value = false
      paySuccessDialog.value = true
    } else {
      show.value = false
      noPaySuccessDialog.value = true
    }
  }

  // 继续支付
  const onContinuePay = () => {
    noPaySuccessDialog.value = false
    show.value = true
  }

  const toOrderDetails = () => {
    router.replace({
      name: 'studentOrderDetails',
      query: {
        orderId: orderNo,
      },
    })
  }

  const toHome = () => {
    router.replace({
      path: '/',
    })
  }

  const onVisibilityChange = () => {
    if (document.hidden) {
      showButton.value = true
    }
  }

  onMounted(() => {
    if (location.hash.indexOf('error') !== -1) {
      alert('参数错误，请检查')
    } else {
      if (isWeChat()) {
        show.value = true
        document.addEventListener('visibilitychange', onVisibilityChange)
      } else {
        const goto = route.query.goto || ''
        window.location.href = goto !== '' ? alipay.decode(goto) : location.pathname + '#error'
      }
    }
  })

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', onVisibilityChange)
  })
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';

  @include Icon('success', 0.36rem, 0.36rem);
  @include Icon('error', 0.36rem, 0.36rem);

  .ap-guide-dialog {
    user-select: none;

    .ap-guide-dialog-content {
      min-height: 100vh;
      position: relative;
    }

    .we-chat-tip {
      width: 0.91rem;
      height: 0.94rem;
      background: url('../../assets/images/payment/guide.png') no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0.02rem;
      right: 0.4rem;
    }

    .we-chat-tip-content {
      font-size: 0.16rem;
      line-height: 0.22rem;
      text-align: right;
      color: #fff;
      position: absolute;
      top: 0.82rem;
      right: 0.17rem;
      //font-family: PingFangSC-Regular, PingFang SC;
    }

    .operation {
      width: 100%;
      position: absolute;
      //top: 1.48rem;
      bottom: 1.69rem;
      text-align: center;

      .confirm {
        width: 1.2rem;
        height: 0.4rem;
        background: #ff9b26;
        border-radius: 0.22rem;
        color: #ffffff;
      }

      .cancel {
        margin-top: 0.2rem;
        color: #ffffff;
        text-decoration: underline;
        background: none;
      }

      .confirm,
      .cancel {
        font-size: 0.14rem;
        &:active {
          opacity: 0.5;
        }
      }
    }
  }

  .dialog-content {
    padding: 0.3rem 0 0.34rem 0;
    text-align: center;

    .desc {
      margin-top: 0.1rem;
      font-size: 0.16rem;
      color: #1f1f1f;
    }
  }
</style>
