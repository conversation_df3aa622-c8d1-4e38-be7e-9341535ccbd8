/* 微信分享 */

import authWeChatSDK from './authWeChatSDK'
import { isLogin } from '@/common'
import { isWeChat } from '@/utils'
import { localProxyStorage } from '@/utils/storage'

const setWxShare = (config, success) => {
  console.log('微信分享信息', config)
  if (!isWeChat()) return

  let { link } = config
  let separator = ''
  if (typeof link === 'string') {
    separator = link.indexOf('?') > -1 ? '&' : '?'
  }

  if (isLogin()) {
    link = link + separator + 'shareId=' + localProxyStorage.user.shareId
  }

  authWeChatSDK()
    .then((wx) => {
      // 获取“分享到朋友圈”按钮点击状态及自定义分享内容接口（即将废弃）
      wx.onMenuShareTimeline({
        title: config.title, // 分享标题
        link: link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl: config.imgUrl, // 分享图标
        success: function () {
          // 用户点击了分享后执行的回调函数
          if (success) {
            success()
          }
        },
      })

      // 获取“分享给朋友”按钮点击状态及自定义分享内容接口（即将废弃）
      wx.onMenuShareAppMessage({
        title: config.title, // 分享标题
        desc: config.desc, // 分享描述
        link: link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl: config.imgUrl, // 分享图标
        type: config.desc || 'link', // 分享类型,music、video或link，不填默认为link
        dataUrl: config.dataUrl || null, // 如果type是music或video，则要提供数据链接，默认为空
        success: function () {
          // 用户点击了分享后执行的回调函数
          if (success) {
            success()
          }
        },
      })

      // 获取“分享到QQ”按钮点击状态及自定义分享内容接口
      wx.onMenuShareQQ({
        title: config.title, // 分享标题
        desc: config.desc, // 分享描述
        link: link, // 分享链接
        imgUrl: config.imgUrl, // 分享图标
        success: function () {
          // 用户确认分享后执行的回调函数
          if (success) {
            success()
          }
        },
        cancel: function () {
          // 用户取消分享后执行的回调函数
        },
      })

      // “分享到 QQ 空间”按钮点击状态及自定义分享内容接口 （即将废弃）
      wx.onMenuShareQZone({
        title: config.title, // 分享标题
        desc: config.desc, // 分享描述
        link: link, // 分享链接
        imgUrl: config.imgUrl, // 分享图标
        success: function () {
          // 用户确认分享后执行的回调函数
        },
        cancel: function () {
          // 用户取消分享后执行的回调函数
        },
      })
    })
    .catch((error) => {
      console.warn(error.msg)
    })
}

export default setWxShare
