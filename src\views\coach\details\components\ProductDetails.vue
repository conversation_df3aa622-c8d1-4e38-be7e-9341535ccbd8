<template>
  <Panel class="product-details-panel" title="教学说明">
    <section v-if="personalProfile" class="section">
      <h2 class="section__title">个人介绍</h2>
      <div class="section__content">
        <p>{{ personalProfile }}</p>
      </div>
    </section>

    <section v-if="personalHonor" class="section">
      <h2 class="section__title">个人成绩</h2>
      <div class="section__content">
        <p>{{ personalHonor }}</p>
      </div>
    </section>

    <section v-if="courseMaterial" class="section">
      <h2 class="section__title">课程信息</h2>
      <div class="section__content">
        <p>{{ courseMaterial }}</p>
      </div>
    </section>

    <section v-if="teachingMaterial" class="section">
      <h2 class="section__title">教学内容</h2>
      <div class="section__content">
        <p>{{ teachingMaterial }}</p>
      </div>
    </section>
  </Panel>
</template>

<script setup>
  import { computed } from 'vue'
  import Panel from './Panel'

  const props = defineProps({
    dataSource: { type: Object, default: null },
  })

  const personalProfile = computed(() => {
    return props.dataSource?.personalProfile
  })

  const personalHonor = computed(() => {
    return props.dataSource?.personalHonor
  })

  const courseMaterial = computed(() => {
    return props.dataSource?.courseMaterial
  })

  const teachingMaterial = computed(() => {
    return props.dataSource?.teachingMaterial
  })
</script>

<style scoped lang="scss">
  .product-details-panel {
    margin-top: 0.1rem;
    color: #383838;
    background-image: linear-gradient(90deg, rgba(217, 234, 251, 0.25) 3%, transparent 0),
      linear-gradient(1turn, rgba(217, 234, 251, 0.25) 3%, transparent 0);
    background-size: 20px 20px;
    background-position: 50%;
  }

  .section {
    padding: 0.08rem 0.16rem;
  }

  .section__title {
    display: inline-block;
    border-left: 0.04rem solid rgb(255, 223, 156);
    background-color: rgb(95, 156, 239);
    color: #fff;
    font-size: 0.14rem;
    padding: 0 0.1rem;
    font-weight: 500;
  }

  .section__content {
    margin-top: 0.1rem;
    line-height: 1.8;
    white-space: pre-line;
  }
</style>
