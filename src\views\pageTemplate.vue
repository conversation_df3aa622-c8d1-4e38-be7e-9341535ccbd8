<template>
  <page
    :title="$route.meta?.title"
    :loading="pageLoading"
    enablePullDownRefresh
    @pullToRefresh="onPullToRefresh"
    @pageshow="onPageshow"
    @pageEnterForeground="onPageEnterForeground"
    @pageEnterBackground="onPageEnterBackground"
  >
    <template #navBarRight>
      <van-button type="primary" size="mini">分享</van-button>
    </template>
    <template #page>
      <p v-for="item in 40" :key="item" class="item">内容</p>
    </template>
  </page>
</template>

<script setup>
  import { ref } from 'vue'
  const pageLoading = ref(true)

  setTimeout(() => {
    pageLoading.value = false
  }, 100)

  const onPullToRefresh = ({ stopPullDownRefresh }) => {
    setTimeout(() => {
      console.log('下拉刷新')
      stopPullDownRefresh()
    }, 1000)
  }

  const onPageshow = () => {
    console.log('pageshow')
  }

  const onPageEnterForeground = () => {
    console.log('页面进入前台')
  }

  const onPageEnterBackground = () => {
    console.log('页面进入后台')
  }
</script>

<style lang="scss" scoped>
  .item {
    text-align: center;
    margin: 0.15rem;
    padding: 0.15rem;
    background: #ccc;
  }
</style>
