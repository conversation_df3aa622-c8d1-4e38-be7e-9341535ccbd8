<template>
  <div class="cascader">
    <div class="cascader-menu level1-menu">
      <ul class="cascader-menu__list">
        <li
          v-for="item in store"
          :key="item.value"
          class="cascader-menu-none"
          :class="selectedClass(0, item.value)"
          @click="select(0, item)"
        >
          <span class="cascader-menu-none__label">{{ item.label }}</span>
        </li>
      </ul>
    </div>

    <div class="cascader-menu">
      <ul class="cascader-menu__list">
        <li
          v-for="item in getList(0)"
          :key="item.value"
          class="cascader-menu-none"
          :class="selectedClass(1, item.value)"
          @click="select(1, item)"
        >
          <span class="cascader-menu-none__label">
            {{ item.label }}
          </span>
        </li>
      </ul>
    </div>

    <div class="cascader-menu level3-menu">
      <ul class="cascader-menu__list">
        <li
          v-for="item in getList(1)"
          :key="item.value"
          class="cascader-menu-none"
          :class="selectedClass(2, item.value)"
          @click="select(2, item)"
        >
          <span class="cascader-menu-none__label">
            {{ item.label }}
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { getCategoriesList } from '@/api/coach-server'
  import usePublicStore from '@/store/public'

  const publicStore = usePublicStore()

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
  })

  const store = ref([])
  const selectedItems = ref([])

  const emit = defineEmits(['update:modelValue', 'change', 'nodeClick'])

  const select = (level, item) => {
    if (selectedItems.value[level] && selectedItems.value[level].value === item.value) {
      selectedItems.value.length = level
    } else {
      selectedItems.value[level] = item
      selectedItems.value.length = level + 1
    }

    const selectedValue = selectedItems.value.map((item) => item.value)
    emit('update:modelValue', selectedValue)
    emit('change', { selected: JSON.parse(JSON.stringify(selectedItems.value)), level })
    emit('nodeClick', { level, selected: item })
  }

  const getList = (level) => {
    return selectedItems.value[level] && selectedItems.value[level].children
  }

  const selectedClass = (level, nodeValue) => {
    if (!selectedItems.value[level]) return {}
    return {
      'node-activity': selectedItems.value[level].value === nodeValue,
    }
  }

  const setDefaultVal = (isInit = false) => {
    let selected = []

    props.modelValue.forEach((value, index) => {
      if (index === 0) {
        selected[0] = store.value.find((item) => item.value === value)
      }
      if (index === 1 && selected[0]) {
        selected[1] = selected[0].children.find((item) => item.value === value)
      }
      if (index === 2 && selected[1]) {
        selected[2] = selected[1].children.find((item) => item.value === value)
      }
    })

    selectedItems.value = selected
    emit('change', {
      selected: JSON.parse(JSON.stringify(selectedItems.value)),
      level: props.modelValue.length - 1,
      isInit: isInit,
    })
  }

  // 处理体育分类数据组装
  const handleSportsClassifyAssemble = (data) => {
    const options = []
    const recursion = (ary, obj) => {
      obj.children = []
      ary.forEach((item, index) => {
        obj.children.push({ label: item.name, value: item.id })
        if (item.childCategoriesVos) {
          recursion(item.childCategoriesVos, obj.children[index])
        }
      })
    }

    data.forEach((level1, index) => {
      options.push({ label: level1.name, value: level1.id })
      if (level1.childCategoriesVos) {
        recursion(level1.childCategoriesVos, options[index])
      }
    })

    return options
  }

  const initialCascader = async () => {
    if (publicStore.sportsClassify.length > 0) {
      store.value = handleSportsClassifyAssemble(publicStore.sportsClassify)
    } else {
      const { data } = await getCategoriesList()
      publicStore.updateSportsClassify(data)
      store.value = handleSportsClassifyAssemble(data)
    }

    setDefaultVal(true)
  }

  initialCascader()
</script>

<style lang="scss" scoped>
  .cascader {
    display: flex;
  }

  .cascader-menu {
    width: 1rem;
    height: 3.85rem;
    overflow: auto;
  }

  .cascader-menu-none {
    width: 100%;
    height: 0.36rem;
    line-height: 0.36rem;
    font-size: 0.12rem;
    color: #1a1b1d;
    cursor: pointer;
  }

  .cascader-menu-none__label {
    display: block;
    padding: 0 0.14rem;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .level1-menu {
    background: #f4f4f4;

    .cascader-menu-none {
      background: #f4f4f4;
    }

    .node-activity {
      background: #ffffff;
      position: relative;
    }
  }

  .level3-menu {
    flex: 1;
  }

  .node-activity {
    color: #ff6445;
  }
</style>
