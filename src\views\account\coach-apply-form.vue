<template>
  <div class="wrapper">
    <steps v-if="!isStepForm" :active="stepsNum" class="mb05">
      <step>基本信息</step>
      <step>授课信息</step>
      <step>教练资质</step>
      <step>审核</step>
    </steps>
    <!-- 步骤四才会出现 -->
    <!-- 待审核 -->
    <verify-notify
      v-if="coachForm.checkStatus === 0 && stepsNum === 3"
      title="已提交申请，等待后台审核…"
      class="mt05"
    >
      <template #desc>
        <div class="again-edit" @click="againEdit">修改资料</div>
        <div class="gohome" @click="tohome">去首页</div>
        <div class="preview-wrap">
          <button class="preview-btn" @click="$router.push('/coach/details/preview')">
            <i class="icon-preview"></i>点击预览效果
          </button>
        </div>
      </template>
    </verify-notify>

    <register-status
      v-if="coachForm.checkStatus === 0 && stepsNum === 3"
      class="mt05"
      :isShowTop="false"
      :show-cound-down="true"
      :statusType="'coach'"
    />
    <!-- 已通过 -->
    <register-status
      v-if="coachForm.checkStatus === 1 && stepsNum === 3"
      class="mt05"
      title="恭喜！你的申请已通过"
      icon="success"
      :show-cound-down="true"
    >
      <template #desc v-if="!isShow">
        <div class="again-edit" @click="$router.push({ name: 'coachWorktableSettingsProfile' })">
          查看详情
        </div>
        <div class="gohome" @click="toWorktable">前往工作台</div>
      </template>
    </register-status>
    <!-- 未通过 -->
    <register-status
      v-if="coachForm.checkStatus === 2 && stepsNum === 3"
      class="mt05"
      title="很遗憾～你的申请未能通过"
      icon="error"
      button-text="重新申请"
      @button-click="reapply"
    >
      <template #desc>
        <p class="register-failed">未通过原因：{{ coachForm.checkRemark }}</p>
      </template>
    </register-status>
    <div v-if="coachForm.checkStatus === 3 || isStepForm" class="form-wrapper">
      <!-- 步骤二才会出现 -->
      <basic-form v-show="stepsNum === 0" ref="basicFormRef" />
      <teach-form v-show="stepsNum === 1" ref="teachFormRef" />
      <specialty-form v-show="stepsNum === 2" :coachTitle="coachTitle" ref="specialtyFormRef" />
    </div>

    <div v-if="isStepForm" class="fixed-button" @click="submit">
      <button class="button">保存</button>
    </div>
    <div v-if="!isStepForm && coachForm.checkStatus === 3" class="fixed-button">
      <button class="button" @click="nextStep">下一步（{{ stepsNum + 1 }}/4）</button>
    </div>
  </div>
</template>

<script>
  export default { name: 'coachApplyForm' }
</script>

<script setup>
  import { ref, onMounted, nextTick } from 'vue'
  import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
  import { Toast } from 'vant'
  import { Step, Steps } from '@/components/steps'
  import VerifyNotify from '@/views/login/components/VerifyNotify'
  import BasicForm from './components/coach-basic-info.vue'
  import TeachForm from './components/coach-teach-subject.vue'
  import SpecialtyForm from './components/coach-specialty.vue'
  import RegisterStatus from '@/views/common/components/register-status'
  import { getCoachNewestBatchCheckRecord, addCoachLastRecord } from '@/api/coach-server'
  import { ossURLJoin } from '@/common'
  import useKeepAliveStore from '@/store/keepAlive'

  const router = useRouter()
  const route = useRoute()
  const basicFormRef = ref(null)
  const teachFormRef = ref(null)
  const specialtyFormRef = ref(null)
  const stepsNum = ref(0)
  const coachForm = ref({
    checkStatus: null,
    checkRemark: null,
  })

  const checkId = ref(null) // 审核id
  const coachId = ref(null) // 教练id
  const coachResulInfo = ref({}) // 教练资料
  const isCoachIdentity = route.query.isCoachIdentity // 是否已经是教练身份
  const isShow = route.query.miniToken //是否是小程序跳转过来

  // 是否从个人中心跳转
  const isStepForm = ref(false)
  if (route.query.channel === 'coachUpdateList') {
    isStepForm.value = true
    stepsNum.value = +route.query.stepsNum
  }

  const tempRef = ref(null)

  // 入驻申请
  const coachTitle = ref('')
  const nextStep = () => {
    if (stepsNum.value === 0) {
      tempRef.value = basicFormRef.value
    } else if (stepsNum.value === 1) {
      tempRef.value = teachFormRef.value
    } else if (stepsNum.value === 2) {
      tempRef.value = specialtyFormRef.value
    }
    tempRef.value.formData.checkId = checkId.value

    // 拿回显的数据（市区+技能类型）覆盖到标题
    let aTitle = basicFormRef.value.formData.teachTitle || ''
    let bTitle = teachFormRef.value.formatData.skillType || ''
    coachTitle.value = aTitle + bTitle + '教练'

    tempRef.value.submit((valid) => {
      if (valid) {
        // 最后一步提交，进入待审核
        if (stepsNum.value === 2) {
          coachForm.value.checkStatus = 0
        }
        stepsNum.value++
      }
    })
  }

  const keepAliveStore = useKeepAliveStore()
  onBeforeRouteLeave((to) => {
    let pages = ['help']
    if (!pages.includes(to.name)) {
      // 卸载缓存
      keepAliveStore.removeKeepAlive('coachApplyForm')
    }
  })
  // 保存修改资料
  const submit = () => {
    if (stepsNum.value === 0) {
      tempRef.value = basicFormRef.value
    } else if (stepsNum.value === 1) {
      tempRef.value = teachFormRef.value
    } else if (stepsNum.value === 2) {
      tempRef.value = specialtyFormRef.value
    }

    if (isCoachIdentity) {
      tempRef.value.formData.coachId = coachId.value
    } else {
      tempRef.value.formData.checkId = checkId.value
    }

    tempRef.value.submit((valid) => {
      if (valid) {
        // 分步修改
        Toast('提交成功')
        setTimeout(() => {
          if (isCoachIdentity) {
            // router.push({ name: "coachWorktableSettingsProfile" });
            router.go(-1)
          } else {
            router.push({ name: 'coachUpdateList' })
          }
        }, 500)
        return
      }
    })
  }

  // 基本资料回显
  const setBaseInfoForm = (data) => {
    nextTick(() => {
      // 基本信息回显
      basicFormRef.value.formData.latitude = data.latitude
      basicFormRef.value.formData.longitude = data.longitude
      // #真实姓名
      basicFormRef.value.formData.realName = data.realName
      // #身份证
      basicFormRef.value.formData.idCard = data.idCard
      // #性别
      basicFormRef.value.formData.sex = data.sex
      // #出生日期
      if (typeof data.birthdate === 'string') {
        // 替换成 "/" 是为了兼容 ios 的日期格式
        // data.birthdate = data.birthdate.replaceAll("-", "/");
        basicFormRef.value.formData.birthdate = data.birthdate
      }

      // 省市区
      if (Array.isArray(data.areaList)) {
        let address = ''
        data.areaList.forEach((item) => {
          basicFormRef.value.formData.areaCodes.push(item.code)
          address += item.name + '/'
        })
        basicFormRef.value.formatData.address = address.substring(0, address.length - 1)
        basicFormRef.value.formatData.lastAreaCode = data.areaList?.[data.areaList.length - 1]?.code
      }
      // #详细地址
      basicFormRef.value.formData.location = data.location
      // #教练图片
      data.coachImages = JSON.parse(data.coachImages)
      if (Array.isArray(data.coachImages)) {
        basicFormRef.value.formData.coachImages = data.coachImages.map((url) => {
          return { url: ossURLJoin(url), path: url }
        })
      }
      // # 教练视频
      if (data.coachVideos) {
        basicFormRef.value.formData.coachVideos.push({
          url: ossURLJoin(data.coachVideos),
          path: data.coachVideos,
        })
      }
    })
  }
  // 授课信息回显
  const setTeachForm = (data) => {
    nextTick(() => {
      // 授课信息回显
      teachFormRef.value.formatData.isUpdateTeachTitle = false

      // #分类
      if (Array.isArray(data.levelList)) {
        teachFormRef.value.formData.levelIds = data.levelList.map((item) => item.id)
      }
      // # 教学成就
      teachFormRef.value.formData.teachAchievement = data.teachAchievement

      if (Array.isArray(data.coachFeesSetVOList)) {
        // # 课时收费
        teachFormRef.value.chargeList = data.coachFeesSetVOList
        // 课时类型集合
        teachFormRef.value.teachWayList = data.coachFeesSetVOList.map((item) => item.feesType)
      }

      // // # 授课科目
      if (Array.isArray(data.levelList)) {
        teachFormRef.value.formatData.skillType = data.levelList[2].name
        teachFormRef.value.formatData.selectedSkillType[data.levelList[2].id] = data.levelList
      }
      // # 教学工作日
      if (data.teachingWeek) {
        teachFormRef.value.formData.teachingWeek = data.teachingWeek.split(',')
      }
      // # 教学工作日时间范围
      if (Array.isArray(data.teachingTimeRange)) {
        teachFormRef.value.formData.teachingTimeRange = data.teachingTimeRange.filter((n) => n)
      }

      // # 服务金额
      teachFormRef.value.formData.money = data.money

      // #授课方式
      teachFormRef.value.formData.teachingWay = data.teachingWay
      // #授课地点
      if (typeof data.trainPlace === 'string' && data.trainPlace) {
        teachFormRef.value.formData.trainPlace = data.trainPlace.split('')
      }
      // #适用人群
      if (typeof data.forTheCrowd === 'string' && data.forTheCrowd) {
        teachFormRef.value.formData.forTheCrowd = data.forTheCrowd.split(',')
      }
    })
  }
  // 教练资质回显
  const setSpecialtyForm = (data) => {
    // 教练资质回显
    // # 教练身份
    specialtyFormRef.value.formData.coachIdentity = data.coachIdentity || ''
    //# 标题
    specialtyFormRef.value.formData.teachTitle = data.teachTitle

    // #从业年限
    specialtyFormRef.value.formData.teachYear = data.teachYear
    // #教练描述
    specialtyFormRef.value.formData.teachDescription = data.teachDescription
    // # 教学成就
    specialtyFormRef.value.formData.teachAchievement = data.teachAchievement
    // # 教学说明
    specialtyFormRef.value.formData.content = data.content
    // #荣耀证书
    data.certificateImages = JSON.parse(data.certificateImages)
    if (Array.isArray(data.certificateImages)) {
      data.certificateImages.forEach((file) => {
        specialtyFormRef.value.formData.certificateImages.push({
          images: [{ url: ossURLJoin(file.url), path: file.url }],
          name: file.name,
        })
      })
    }
  }

  // 从教练资料列表进入
  const initFormUserInfo = () => {
    getCoachNewestBatchCheckRecord(isCoachIdentity).then((res) => {
      let { data } = res
      coachResulInfo.value = res.data
      if (data) {
        if (isCoachIdentity) {
          coachId.value = data.coachId
        } else {
          checkId.value = data.checkId
        }
        if (route.query.channel === 'coachUpdateList') {
          if (stepsNum.value === 0) {
            // basicFormRef.value.formatData.isShowTitle = false;
            setBaseInfoForm(data.coachCheckRecordBaseInfoVO)
          } else if (stepsNum.value === 1) {
            // teachFormRef.value.formatData.isShowTitle = false;
            setTeachForm(data.coachCheckRecordTeachingInfoVO)
          } else if (stepsNum.value === 2) {
            // specialtyFormRef.value.formatData.isShowTitle = false;
            setSpecialtyForm(data.coachCheckRecordQualificationVO)
          }
        }
      }
    })
  }
  // 从注册/入驻教练进入
  const initFormSingUp = () => {
    getCoachNewestBatchCheckRecord().then((res) => {
      let { data } = res
      coachResulInfo.value = res.data
      if (data) {
        checkId.value = data.checkId

        //待审核
        if (data.checkStatus === 0) {
          stepsNum.value = 3
        }
        // data.checkStatus = 2;
        // 审核成功
        if (data.checkStatus === 1) {
          stepsNum.value = 3
        }

        if (data.checkStatus === 2) {
          stepsNum.value = 3
        }
        coachForm.value.checkStatus = data.checkStatus
        coachForm.value.checkRemark = data.checkRemark || ''

        // 入驻待审核
        if (data.checkStatus === 3 && !isStepForm.value) {
          stepsNum.value = 0
          nextTick(() => {
            setBaseInfoForm(data.coachCheckRecordBaseInfoVO)
            setTeachForm(data.coachCheckRecordTeachingInfoVO)
            setSpecialtyForm(data.coachCheckRecordQualificationVO)
          })
        }
      }
    })
  }

  // 重新申请
  const reapply = () => {
    stepsNum.value = 0
    coachForm.value.checkStatus = 3
    addCoachLastRecord().then((res) => {
      checkId.value = res.data.checkId
    })
    setTimeout(() => {
      setBaseInfoForm(coachResulInfo.value.coachCheckRecordBaseInfoVO)
      setTeachForm(coachResulInfo.value.coachCheckRecordTeachingInfoVO)
      setSpecialtyForm(coachResulInfo.value.coachCheckRecordQualificationVO)
    })
  }
  const tohome = () => {
    router.push({ name: 'home' })
  }
  const toWorktable = () => {
    router.push({ name: 'myWorktableCoach' })
  }
  const againEdit = () => {
    router.push({ name: 'coachUpdateList' })
  }

  onMounted(() => {
    if (route.query.channel === 'coachUpdateList') {
      initFormUserInfo()
      basicFormRef.value.formatData.isShowTitle = false
      teachFormRef.value.formatData.isShowTitle = false
      specialtyFormRef.value.formatData.isShowTitle = false
    } else {
      initFormSingUp()
    }
  })
</script>

<style lang="scss" scoped>
  @import '~@/styles/fixed-button.scss';
  @import '@/styles/mixins/mixins.scss';

  @include Icon('preview', 0.16rem, 0.16rem) {
    vertical-align: text-top;
    margin-right: 0.03rem;
  }

  .wrapper {
    padding-bottom: 0.8rem;
    .mt05 {
      margin-top: 0.05rem;
    }
    .mb05 {
      margin-bottom: 0.05rem;
    }

    .form-wrapper {
      background: #fff;

      .tip {
        text-align: center;
        padding-top: 0.18rem;
      }
    }

    .register-failed {
      margin-top: 0.06rem;
      font-size: 0.12rem;
      color: #616568;
    }

    :deep(.van-steps--horizontal) {
      padding: 0.2rem 0.36rem;
    }

    :deep(.van-step__circle-container) {
      top: 0.3rem;
    }
    :deep(.van-step--horizontal .van-step__line) {
      top: 0.3rem;
    }
  }
  .gohome,
  .again-edit {
    display: inline-block;
    margin: 0.1rem auto 0;
    width: 0.96rem;
    height: 0.32rem;
    line-height: 0.3rem;
    text-align: center;
    background: #ffffff;
    color: #ff9b26;
    font-size: 0.14rem;
    border-radius: 0.16rem;
    border: 0.01rem solid #f28d00;
  }
  .again-edit {
    margin-right: 0.1rem;
  }
  .gohome {
    background: #ff9b26;
    color: #ffffff;
    border: none;
  }

  .preview-wrap {
    margin-top: 0.24rem;

    .preview-btn {
      font-size: 0.14rem;
      color: #616568;
    }
  }
</style>
