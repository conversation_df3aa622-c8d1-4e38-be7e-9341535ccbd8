<template>
  <div>
    <div class="search-box flex">
      <!-- <div class="address">
        <span>广州</span>
        <van-icon class="address-arrow" name="arrow-down" />
      </div> -->

      <van-field
        class="search-ipt"
        v-model="formData.searchVal"
        :placeholder="placeholder"
        @keyup="searchClick2($event)"
      >
        <template #button>
          <i class="icon icon-search" />
        </template>
      </van-field>
      <div class="search-btn" @click="searchClick">搜索</div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, watch } from 'vue'
  // import { localProxyStorage } from "@/utils/storage";
  const props = defineProps({
    placeholder: {
      type: String,
      default: '搜索教练/场馆',
    },
    searchValue: {
      type: String,
      default: '',
    },
  })

  watch(
    () => props.searchValue,
    () => {
      formData.searchVal = props.searchValue
    },
  )

  onMounted(() => {
    if (props.searchValue) {
      formData.searchVal = props.searchValue
    }
  })
  const formData = reactive({
    searchVal: '',
    historyList: [],
  })

  const emit = defineEmits(['searchChange'])

  const searchClick = () => {
    emit('searchChange', formData.searchVal)
    // if (!formData.historyList.includes(formData.searchVal)) {
    //   formData.historyList.unshift(formData.searchVal);
    // }
    // localProxyStorage.historyInfo = formData.historyList;
  }
  const searchClick2 = (el) => {
    if (el.keyCode === 13) {
      searchClick()
    }
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';
  @include Icon('search', 0.15rem, 0.15rem);
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  :deep(.van-icon) {
    font-size: 0.18rem;
    color: #ccc;
  }
  .van-button--small {
    width: 0.48rem;
    height: 0.3rem;
    background: #ff9b26;
    border-radius: 0.17rem;
  }
  .search-box {
    position: relative;
    padding: 0.08rem 0.15rem;
    width: 3.75rem;
    height: 0.45rem;
    background: #ffffff;
    .search-btn {
      position: absolute;
      right: 0.18rem;
      top: 0.08rem;
      text-align: center;
      width: 0.48rem;
      height: 0.3rem;
      line-height: 0.3rem;
      background: #ff9b26;
      border-radius: 0.17rem;
      font-size: 0.12rem;
      font-weight: 600;
      color: #ffffff;
    }
    .address {
      width: 0.6rem;
      flex-shrink: 0;
      .address-arrow {
        margin-left: 0.02rem;
        color: #453838;
      }
    }
    .search-ipt {
      padding: 0 0.15rem;
      flex: auto;
      height: 0.34rem;
      line-height: 0.34rem;
      background: #f3f3f3;
      border-radius: 0.17rem;
    }
  }
  :deep(.van-field__button) {
    position: absolute;
    left: -0.1rem;
    display: flex;
    z-index: 999;
  }
  :deep(.van-field__body) {
    padding-left: 0.17rem;
  }
</style>
