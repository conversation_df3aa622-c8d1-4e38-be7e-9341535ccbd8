<template>
  <Teleport to="body">
    <div v-if="show" class="LoginPopup">
      <van-popup v-model:show="show" @open="onOpen" @close="onClose">
        <div class="popup-wrap">
          <div class="close" @click="updateShow(false)" />
          <div class="title">
            <i class="star" />
            <span>哎呀，你先登录啦~</span>
            <i class="star" />
          </div>
          <div class="welfare">
            <div class="gift" />
            <div class="welfare-text">
              <p>完成登录，帮朋友点赞</p>
              <p>获取神秘大礼包！</p>
            </div>
          </div>

          <div class="form">
            <FormItem v-model="formData.mobile" label="手机号" placeholder="请输入正确手机号" />
            <FormItem
              v-model="formData.imageCode"
              label="图形验证码"
              placeholder="请输入图形验证码"
            >
              <template #right>
                <ImageCaptcha class="image-Captcha" />
              </template>
            </FormItem>
            <FormItem
              v-model="formData.verifyCode"
              label="验证码"
              placeholder="请输入验证码"
              :max-length="6"
            >
              <template #right>
                <span v-if="isGetCaptcha" class="auth-code" @click="getVerifyCode">获取验证码</span>
                <CountDown
                  class="count-down"
                  v-else
                  :time="time"
                  format="ss 秒"
                  @finish="handleCountDownFinish"
                />
              </template>
            </FormItem>
          </div>
          <button class="login-btn" @click="onSubmit">登录/注册</button>
        </div>
      </van-popup>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import FormItem from './FormItem'
  import ImageCaptcha from '@/components/image-captcha'
  import Schema from 'async-validator'
  import { checkEmpty, checkPhone } from '@/utils/validate'
  import { verifyUserInfoByMobile } from '@/api/user-server'
  import { loginByVerifyCode, sendRegistration } from '@/api/auth-server'
  import { Toast, CountDown } from 'vant'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'
  import { useSendLoginRegisterSMSCode } from '@/use/useSendVerifyCode'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  watch(
    () => props.modelValue,
    (val) => (show.value = val),
  )

  // eslint-disable-next-line no-unused-vars
  let validator = new Schema({
    mobile: { message: '请输入手机号', validator: checkPhone },
    imageCode: { message: '请输入图形验证码', validator: checkEmpty },
    verifyCode: { message: '请输入短信验证码', validator: checkEmpty },
  })

  const time = ref(60 * 1000)
  const isGetCaptcha = ref(true)

  const show = ref(props.modelValue || false)

  const initFormData = () => {
    return {
      mobile: '',
      verifyCode: '',
      imageCode: '',
    }
  }

  const formData = ref(initFormData())

  // 倒计时结束后
  const handleCountDownFinish = () => {
    isGetCaptcha.value = true
  }

  const updateShow = (state) => emit('update:modelValue', state)

  const onOpen = () => {
    formData.value = initFormData()
  }

  const onClose = () => {
    updateShow(false)
  }

  // 获取验证码
  const getVerifyCode = () => {
    useSendLoginRegisterSMSCode(formData.value, () => {
      isGetCaptcha.value = false
      Toast('验证码发送成功')
    })
  }

  function afterLogin(data) {
    localProxyStorage.user = data
    onClose()
    Toast('登录成功')
    window.location.reload()
  }

  const register = () => {
    let params = {
      mobile: formData.value.mobile,
      verifyCode: formData.value.verifyCode,
      identity: 'member',
      registerSource: 'r4',
      unionId: sessionProxyStorage.weChatUId, // 用户微信 unionId
    }
    // 如果有分享人,则携带上注册
    if (sessionProxyStorage.shareId) {
      params.ijlShare = {
        shareId: sessionProxyStorage.shareId,
      }
    }

    sendRegistration(params).then((res) => {
      afterLogin(res.data)
    })
  }

  const login = () => {
    let loginLoading = Toast.loading({
      message: '登录中...',
      duration: 0,
    })

    let params = {
      mobile: formData.value.mobile,
      verifyCode: formData.value.verifyCode,
      expired: undefined,
      unionId: sessionProxyStorage.weChatUId, // 用户微信 unionId
    }

    loginByVerifyCode(params)
      .then((res) => {
        loginLoading.clear()
        afterLogin(res.data)
      })
      .catch(() => {})
  }

  const onSubmit = () => {
    verifyUserInfoByMobile({ mobile: formData.value.mobile }).then((res) => {
      const { data } = res
      data ? login() : register()
    })
  }
</script>

<style lang="scss" scoped>
  .popup-wrap {
    width: 3.3rem;
    background: #ffffff;
    border-radius: 0.1rem;
    border: 2px solid #376cfe;
    padding-bottom: 0.25rem;
    position: relative;
  }

  .close {
    position: absolute;
    right: 0.12rem;
    top: 0.12rem;
    width: 0.12rem;
    height: 0.12rem;
    background: url('../images/popup-close.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  .title {
    margin-top: 0.25rem;
    margin-bottom: 0.1rem;
    text-align: center;

    span {
      margin: 0 0.05rem;
      font-size: 0.16rem;
      color: #1a1b1d;
      font-weight: 600;
    }
  }

  .star {
    width: 0.15rem;
    height: 0.1rem;
    display: inline-block;
    background: url('../images/star.png') no-repeat;
    background-size: 100% 100%;
  }

  .welfare {
    width: 1.97rem;
    height: 0.44rem;
    position: relative;
    margin: 0 auto;

    .welfare-text {
      width: 1.84rem;
      height: 0.44rem;
      background: #ff81b7;
      border: 1px solid #7719be;
      line-height: 0.18rem;
      margin-left: 0.13rem;
      padding: 0.04rem 0 0.04rem 0.29rem;
      color: #ffffff;
      border-radius: 0.06rem 0.06rem 0.06rem 0;
    }

    .gift {
      width: 0.34rem;
      height: 0.34rem;
      position: absolute;
      left: 0;
      top: 0.05rem;
      background: url('../images/welfare-box.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .form {
    padding: 0 0.08rem;
    margin-top: 0.1rem;

    :deep(.form-item) {
      border: none;
      padding: 0.15rem 0.15rem 0.15rem 0;
      position: relative;

      .form-item-label {
        width: 0.66rem;
        padding-left: 0.07rem;
      }

      .form-item-input {
        border-bottom: 1px solid #eeeeee;
        line-height: 0.5rem;
        padding-left: 0.1rem;
      }

      .form-item-right-icon {
        position: absolute;
        right: 0.15rem;
        color: #e02020;
      }
    }
  }

  .login-btn {
    width: 2.85rem;
    height: 0.4rem;
    background: #ff9b26;
    border-radius: 0.2rem;
    margin: 0.2rem auto 0 auto;
    display: block;
    font-size: 0.16rem;
    color: #ffffff;
  }

  .img-code {
    width: 1rem;
    height: 0.42rem;
    display: inline-block;
    border: 1px solid #ebebeb;
    position: absolute;
    right: 0;
    top: 0;
  }

  :deep(.van-popup) {
    background: transparent;
  }
</style>
