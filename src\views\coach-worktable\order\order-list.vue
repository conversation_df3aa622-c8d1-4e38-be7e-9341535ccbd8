<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="container">
        <van-tabs
          class="tabs"
          v-model:active="tabIndex"
          @click-tab="handledTabsChange"
          sticky
          :offset-top="isMiniprogramEvn ? 0 : '0.44rem'"
        >
          <van-tab v-for="(tab, index) in tabs" :key="index" :title="tab.title" :name="tab.name">
            <van-list
              :ref="(el) => (listRefs[index] = el)"
              v-model:loading="tab.loading"
              :finished="tab.finished"
              :finished-text="tab.emptyShow ? null : '-没有更多了-'"
              @load="() => onLoad(index)"
            >
              <template v-if="tab.name !== 4">
                <order-item
                  v-for="order in tab.list"
                  :key="order.id"
                  :order="order"
                  @click="toOrderDetails(order)"
                />
              </template>
              <template v-else>
                <refund-order-item
                  v-for="order in tab.list"
                  :key="order.id"
                  :order="order"
                  @click="toRefundOrderDetail(order)"
                  @refundBtnClick="handledRefund(order)"
                />
              </template>
            </van-list>
            <empty class="empty" v-if="tab.emptyShow" :description="tab.emptyDesc" top="1.96rem" />
          </van-tab>
        </van-tabs>

        <audit-refund
          v-model="refundPopupShow"
          :data="curSelectOrder"
          @agree="refreshRefundList"
          @reject="refreshRefundList"
        />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { tabList } from '@/views/coach-worktable/order/tabs'
  import Empty from '@/components/empty'
  import OrderItem from './components/OrderItem'
  import RefundOrderItem from './components/RefundOrderItem'
  import AuditRefund from './components/AuditRefund'
  import { getCoachOrderList, getCoachAfterSaleOrder } from '@/api/coach-worktable'

  const router = useRouter()
  const tabIndex = ref(0)
  const listRefs = ref([])
  const tabs = reactive(tabList)
  const refundPopupShow = ref(false)
  const curSelectOrder = ref(null)
  const isMiniprogramEvn = localStorage.getItem('BROWSER_ENV') === 'miniprogram'

  // 获取订单列表
  const getOderList = (index) => {
    tabs[index].pageNum += 1

    let params = {
      pageNum: tabs[index].pageNum,
      pageSize: 7,
      type: tabs[index].name,
    }

    getCoachOrderList(params).then((res) => {
      const { data } = res

      tabs[index].list = tabs[index].list.concat(data)

      if (tabs[index].list.length === 0) {
        tabs[index].emptyShow = true
      }
      // 加载结束
      tabs[index].loading = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        tabs[index].finished = true
      }
    })
  }

  // 获取退费订单列表
  const getAfterSaleOrder = (index) => {
    tabs[index].pageNum += 1
    let params = {
      pageNum: tabs[index].pageNum,
      pageSize: 7,
      type: tabs[index].name,
    }
    getCoachAfterSaleOrder(params).then((res) => {
      const { data } = res

      tabs[index].list = tabs[index].list.concat(data)

      if (tabs[index].list.length === 0) {
        tabs[index].emptyShow = true
      }
      // 加载结束
      tabs[index].loading = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        tabs[index].finished = true
      }
    })
  }

  const onLoad = (index) => {
    if (tabs[index].name !== 4) {
      getOderList(index)
    } else {
      getAfterSaleOrder(index)
    }
  }

  const handledTabsChange = (index) => {
    listRefs.value[index] && listRefs.value[index].check()
  }

  const handledRefund = (order) => {
    curSelectOrder.value = {
      orderId: order.id,
      applyTime: order.applyTime,
      refundAmount: order.refundAmount,
      refundQuantity: order.refundQuantity,
      studentName: order.studentInfo.studentName,
    }
    refundPopupShow.value = true
  }

  // 刷新退款订单列表
  const refreshRefundList = () => {
    tabs[tabIndex.value].list = []
    nextTick(() => {
      tabs[tabIndex.value].pageNum = 0
      tabs[tabIndex.value].loading = false
      tabs[tabIndex.value].finished = false
      listRefs.value[tabIndex.value] && listRefs.value[tabIndex.value].check()
    })
  }

  const toOrderDetails = (order) => {
    router.push({
      name: 'myWorktableOrderDetails',
      query: {
        orderId: order.id,
      },
    })
  }

  // 查看退款订单详情
  const toRefundOrderDetail = (order) => {
    router.push({
      name: 'myWorktableOrderRefundDetails',
      query: {
        orderId: order.id,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .tabs {
    :deep(.van-tabs__wrap) {
      height: 0.45rem;
    }

    :deep(.van-tab) {
      font-size: 0.14rem;
      color: #616568;
      line-height: 0.2rem;
    }

    :deep(.van-tab--active) {
      font-size: 0.18rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    :deep(.van-tabs__line) {
      width: 0.18rem;
      height: 0.03rem;
      background: linear-gradient(90deg, #ff9b26 0%, #ff6445 100%);
      border-radius: 0.03rem;
      bottom: 0.2rem;
    }
    :deep(.van-tab__panel) {
      padding: 0 0.08rem;
    }
  }

  .empty {
    padding-top: 1.97rem;
  }
</style>
