<template>
  <van-popup class="popup" round position="bottom">
    <div class="toolbar">
      <button @click="$emit('update:show', false)">取消</button>
      <button class="confirm" @click="handleConfirm">确认</button>
    </div>
    <van-checkbox-group v-model="checkedDays" @change="handelChange">
      <ul class="days">
        <li
          v-for="(value, key, index) in workingDay"
          :key="index"
          class="day"
          :class="{ active: checkedDays.includes(key) }"
          @click="toggle(index)"
        >
          <div>{{ value }}</div>
          <div>
            <van-checkbox
              :name="key"
              checked-color="#FF6445"
              :ref="(el) => (checkboxRefs[index] = el)"
              shape="square"
              @click.stop
            />
          </div>
        </li>
      </ul>
    </van-checkbox-group>
  </van-popup>
</template>

<script setup>
  import { ref, watch } from 'vue'

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['confirm', 'update:show', 'change'])

  const checkedDays = ref([])

  const checkboxRefs = ref([])

  const toggle = (index) => {
    checkboxRefs.value[index].toggle()
  }

  const workingDay = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日',
  }

  const handelChange = (value) => {
    emit('change', {
      checked: value,
    })
  }

  const handleConfirm = () => {
    emit('confirm', checkedDays.value)
  }

  watch(
    () => props.modelValue,
    (newVal) => {
      checkedDays.value = newVal
      handleConfirm()
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped>
  .toolbar {
    display: flex;
    height: 0.6rem;
    padding: 0.2rem;
    justify-content: space-between;
    font-size: 0.15rem;
    color: #969799;

    .confirm {
      color: #ff6445;
    }
  }

  .days {
    .day {
      display: flex;
      justify-content: space-between;
      padding: 0.15rem 0.2rem;
      height: 0.54rem;
    }

    .active {
      background-color: #fff7f5;
      color: #ff6445;
    }
  }
</style>
