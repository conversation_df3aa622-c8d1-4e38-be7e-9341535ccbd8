import { ref, onMounted, onUnmounted } from 'vue'

export default function useVisualViewport() {
  let width = ref(window.innerWidth)
  let height = ref(window.innerHeight)

  const handler = () => {
    // ios 13以上
    if (window.visualViewport) {
      width.value = window.visualViewport.width
      height.value = window.visualViewport.height
    } else {
      width.value = window.innerWidth
      height.value = window.innerHeight
    }
  }

  onMounted(() => {
    window.visualViewport.addEventListener('resize', handler)
    window.visualViewport.addEventListener('scroll', handler)
  })

  onUnmounted(() => {
    window.visualViewport.removeEventListener('resize', handler)
    window.visualViewport.removeEventListener('scroll', handler)
  })

  return { width, height }
}
