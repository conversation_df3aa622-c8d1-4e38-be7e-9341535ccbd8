<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="page">
        <div v-if="courseDetail" class="header">
          <h3 class="label">
            {{ courseDetail.studyStatus.statusName }}：剩余
            {{ courseDetail.showRemainQuantity }} 个课时
          </h3>
          <div class="header-box flex">
            <p>
              <template v-if="courseDetail.studyStatus.status === 'FINISH'">
                课时已全部上完了～
              </template>
              <template v-else>赶紧联系教练预约上课吧～</template>
            </p>
            <div class="order flex">
              <span @click="toHistoryOrder">历史订单</span>
              <i class="icon icon-arrow-right" />
            </div>
          </div>
          <div class="coach-info">
            <h3 class="label">授课教练</h3>
            <div class="coach-box flex">
              <div class="coach-box-l flex">
                <img :src="getOssURL(courseDetail.coachInfo.avatarUrl)" alt="" v-error-img />
                <h3>{{ courseDetail.coachInfo.coachName }}</h3>
              </div>
              <div class="coach-box-r">
                <a
                  v-if="courseDetail.studyStatus.status === 'FINISH'"
                  class="class-btn"
                  @click.stop="continueLearn()"
                  >继续上课</a
                >
                <a v-else class="class-btn" :href="'tel:' + courseDetail.coachInfo.mobile"
                  >联系上课</a
                >
                <!-- <div class="class-btn" @click="callCoach">联系上课</div>
            <a href="" v-show="false" ref="tels"></a> -->
              </div>
            </div>
          </div>
        </div>

        <div class="list-block">
          <h3 v-if="!emptyShow" class="title">上课记录</h3>
          <van-list
            class="trainer"
            v-model:loading="loading"
            :finished="finished"
            @load="getRecordList"
          >
            <div v-for="(item, index) in classList" :key="index" class="collapse">
              <div class="collapse-title" @click="item.open = !item.open">
                <van-row justify="space-between" align="center">
                  <van-col class="hx">
                    <span>核销{{ item.consumeQuantity }}个课时</span>
                    <van-icon :name="item.open ? 'arrow-down' : 'arrow'" />
                  </van-col>
                  <van-col class="state">
                    <!-- 待处理状态下展示倒计时 -->
                    <!--                    <van-count-down-->
                    <!--                      v-if="item.consumeStatus.status === 'APPLIED'"-->
                    <!--                      class="count-down"-->
                    <!--                      :time="getCountDownTime(item)"-->
                    <!--                      format="还剩HH时mm分钟"-->
                    <!--                    />-->
                    <span>{{ item.consumeStatus.statusName }}</span>
                  </van-col>
                </van-row>
                <div class="time">{{ item.applyTime }}</div>
              </div>
              <div :class="['collapse-wrapper', { open: item.open }]">
                <div class="collapse-content">
                  <!-- 待处理 -->
                  <div v-if="item.consumeStatus.status === 'APPLIED'">
                    <div class="sub-title f14">
                      教练{{ item.coachName }}申请核销课时：{{ item.consumeQuantity }}个课时（{{
                        item.teachingWay.typeName
                      }})
                    </div>
                    <div class="operation flex">
                      <div class="refuse" @click="refuseFn(item)">拒绝</div>
                      <div class="agree" @click.stop="agreeFn(item)">同意</div>
                    </div>
                    <div class="operating-time">{{ item.applyTime }}</div>
                  </div>
                  <!-- 操作日志 -->
                  <div v-else v-for="(log, index) in item.logs" :key="index" class="sub-item">
                    <div class="sub-title">{{ log.title }}</div>
                    <div v-if="log.desc" class="operating-desc">
                      {{ log.desc }}
                    </div>
                    <div v-if="log.type === 'AGREED'" class="operation">
                      <div
                        v-if="item.evaluateId === '0'"
                        class="evaluate"
                        @click="showEvaluateFn(item)"
                      >
                        课后评价
                      </div>
                      <div v-else class="evaluate" @click="showEvaluateFn(item)">查看评价</div>
                    </div>
                    <div class="operating-time">{{ log.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
          <empty v-if="emptyShow" top="1rem" description="暂无上课记录<br >快去联系教练上课吧～" />
        </div>

        <refuse-pop v-model:show="showRefusePop" :refuseInfo="refuseInfo" @refresh="refreshList" />
        <!--  课后评价弹窗 -->
        <evaluate-pop
          v-model:show="showEvaluatePop"
          :row-data="curSelectRowData"
          @refresh="refreshList"
        />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import RefusePop from './refuse-pop.vue'
  import EvaluatePop from './evaluate-pop.vue'
  import { agreeConsume, getMyCoachDetail, getClassesConsumeList } from '@/api/trade-server'
  import { Dialog, Toast } from 'vant'
  import Empty from '@/components/empty'
  import { getOssURL } from '@/common'
  // import { getDateTime } from "@/utils/day";

  const router = useRouter()
  const route = useRoute()

  const coachUserId = route.query.coachUserId

  const courseDetail = ref(null)
  const loading = ref(false)
  const finished = ref(false)
  const classList = ref([])
  const emptyShow = ref(false)
  const refuseInfo = ref({})
  const showRefusePop = ref(false)
  const showEvaluatePop = ref(false)
  const isEvaluate = ref(null)
  const curSelectRowData = ref(null)
  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  // 获取课程详情
  const getDetail = () => {
    getMyCoachDetail({ coachUserId: coachUserId }).then((res) => {
      const { data } = res
      courseDetail.value = data
    })
  }

  const getRecordList = () => {
    pagination.pageNum += 1
    let params = {
      heId: coachUserId,
      ...pagination,
    }
    getClassesConsumeList(params)
      .then((res) => {
        let { data } = res
        data.forEach((item) => {
          item.open = item.consumeStatus.status === 'APPLIED'
          if (item.consumeStatus.status !== 'APPLIED') {
            item.logs = []
            // 教练申请记录
            item.logs.unshift({
              title: `教练${item.coachName}申请核销课时：${item.consumeQuantity}个课时（${item.teachingWay.typeName}）`,
              time: item.applyTime,
            })

            // 核销成功
            if (item.consumeStatus.status === 'AGREED') {
              item.logs.unshift({
                title: `学员已同意教练${item.coachName}的核销申请`,
                time: item.auditTime,
                type: 'AGREED',
              })
            }
            // 核销拒绝
            if (item.consumeStatus.status === 'REFUSED') {
              item.logs.unshift({
                title: `学员已拒绝教练${item.coachName}的核销申请`,
                time: item.auditTime,
                desc: '拒绝理由：' + item.refuseExplain,
              })
            }
          }
        })

        classList.value = classList.value.concat(data)
        emptyShow.value = classList.value.length === 0
        // 加载状态结束
        loading.value = false
        // 数据全部加载完成
        if (data.length === 0 || data.length < pagination.pageSize) {
          finished.value = true
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true
      })
  }

  // 获取倒计时毫秒数
  // const getCountDownTime = (rowData) => {
  //   const startTime = getDateTime();
  //   const endTime = getDateTime(rowData.auditDeadTime);
  //   return endTime - startTime;
  // };

  // 评价
  const showEvaluateFn = (rowData) => {
    isEvaluate.value = rowData
    curSelectRowData.value = rowData
    showEvaluatePop.value = true
  }

  // 继续上课
  const continueLearn = () => {
    router.push({
      path: `/coach/details/${courseDetail.value.coachInfo.coachId}`,
      query: {
        isShowSku: true,
      },
    })
  }

  const refreshList = () => {
    pagination.pageNum = 0
    finished.value = false
    loading.value = false
    classList.value = []
    nextTick(() => {
      getRecordList()
    })
  }
  // 同意核销
  const agreeFn = (item) => {
    Dialog.confirm({
      confirmButtonText: '确认核销',
      message() {
        return (
          <div>
            <div>核销课时</div>
            <div style="color: var(--i-primary);font-size: 0.12rem;margin-top: 0.08rem">
              <p>为了您的上课权益，请勿在未上课前同意核销</p>
              <p>否则，因核销引起的权益损失平台概不负责。</p>
            </div>
          </div>
        )
      },
    }).then(() => {
      const params = { id: item.id }
      agreeConsume(params).then(() => {
        Toast('已同意核销')
        getDetail()
        refreshList()
        // router.go(0);
      })
    })
  }

  const refuseFn = (item) => {
    refuseInfo.value = item
    refuseInfo.value['statusName'] = item.teachingWay.typeName
    showRefusePop.value = true
  }

  const toHistoryOrder = () => {
    router.push({
      name: 'studentHistoryOrder',
      query: {
        coachUserId: coachUserId,
      },
    })
  }

  const init = () => {
    getDetail()
  }

  init()
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';

  @include Icon('arrow-right', 0.08rem, 0.1rem);
  @include Icon('arrow-down', 0.16rem, 0.16rem);
  @include Icon('arrow-right3', 0.16rem, 0.16rem);
  // 公共样式
  .flex {
    display: flex;
    align-items: center;
  }

  .label {
    font-size: 0.16rem;
    font-weight: 600;
    color: #1a1b1d;
  }

  .list-block {
    background: #fff;
    padding: 0.12rem 0.15rem;
    // border-top: 0.08rem solid #f7f7f7;

    .title {
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }
  }

  .header {
    background-color: #fff;
    padding: 0.12rem 0.15rem;
    border-bottom: 0.08rem solid #f7f7f7;
    .header-box {
      margin-top: 0.06rem;
      padding-bottom: 0.1rem;
      border-bottom: 0.01rem solid #eee;
      justify-content: space-between;
      font-size: 0.14rem;
      color: #616568;
    }
    h3 {
      font-size: 0.18rem;
    }
    .order {
      align-items: center;
      span {
        margin-right: 0.02rem;
      }
    }
    .coach-info {
      padding-top: 0.1rem;
      h3 {
        font-size: 0.16rem;
      }
      .coach-box {
        margin-top: 0.08rem;
        align-items: center;
        justify-content: space-between;

        .coach-box-l {
          align-items: center;
          img {
            object-fit: cover;
            margin-right: 0.1rem;
            width: 0.36rem;
            height: 0.36rem;
            flex-shrink: 0;
            border-radius: 50%;
            border: 0.01rem solid #eee;
          }
          h3 {
            font-size: 0.14rem;
            color: #1f1f1f;
          }
        }
        .coach-box-r .class-btn {
          padding: 0.04rem 0.12rem;
          // width: 0.78rem;
          height: 0.28rem;
          line-height: 0.28rem;
          text-align: center;
          background: #ffffff;
          font-size: 0.14rem;
          color: #ff9b26;
          border-radius: 0.16rem;
          border: 0.01rem solid #ff9b26;
        }
      }
    }
  }

  .collapse {
    .count-down {
      display: inline-block;
      vertical-align: top;
      font-size: 0.12rem;
      color: #ff9b26;
      margin-right: 0.08rem;
    }

    .collapse-title {
      padding: 0.1rem 0;

      .hx {
        color: #333333;
      }

      .state {
        color: #ff9b26;
      }

      .time {
        margin-top: 0.04rem;
        font-size: 0.12rem;
        color: #b2b1b7;
      }
    }

    .collapse-wrapper {
      height: 0;
      display: none;
      transition: height 1s ease-in-out;
      will-change: height;
    }

    .open {
      height: auto;
      display: block;
    }

    .collapse-content {
      background: #f7f7f7;
      border-radius: 4px;
      padding: 0 0.12rem;
    }

    .sub-item {
      font-size: 0.13rem;
      color: #1a1b1d;

      &:not(:last-child) {
        border-bottom: 1px solid #eeeeee;
      }
    }

    .sub-title {
      padding-top: 0.1rem;
    }

    .operating-desc {
      margin-top: 0.05rem;
      font-size: 0.13rem;
      color: #616568;
      word-break: break-all;
    }

    .operating-time {
      padding-bottom: 0.1rem;
      margin-top: 0.08rem;
      font-size: 0.12rem;
      color: #b2b1b7;
    }

    .operation {
      margin-top: 0.1rem;

      .refuse,
      .evaluate,
      .agree {
        width: 0.84rem;
        height: 0.32rem;
        line-height: 0.3rem;
        text-align: center;
        background: #ffffff;
        border-radius: 0.16rem;
        border: 0.01rem solid #ff9b26;
        color: #ff9b26;
        font-size: 0.14rem;
      }

      .agree {
        margin-left: 0.12rem;
        background: #ff9b26;
        color: #ffffff;
      }
    }
  }
</style>
