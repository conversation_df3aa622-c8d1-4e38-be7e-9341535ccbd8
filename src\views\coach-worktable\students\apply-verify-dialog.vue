<template>
  <i-dialog
    title="课时核销"
    @open="onDialogOpen"
    @closed="onDialogClose"
    :before-close="beforeClose"
    showCancelButton
    confirmButtonText="提交"
  >
    <div v-if="render" class="form">
      <i-select
        v-if="isShowType"
        v-model="form.teachingWay"
        label="授课类型"
        placeholder="请选择"
        @change="onTypeChange"
        :options="options"
        teleport="body"
        is-link
      />

      <van-field
        :class="['input-num', { 'input-content-center': !isShowType }]"
        :label="isShowType ? '核销课时' : null"
      >
        <template #input>
          <van-stepper
            v-model="form.quantity"
            :default-value="''"
            :show-plus="false"
            :show-minus="false"
            integer
            :long-press="false"
            :max="curSelectType?.quantity"
            allow-empty
            :placeholder="placeholder"
            :min="1"
          />
        </template>
      </van-field>
    </div>
  </i-dialog>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import Schema from 'async-validator'
  import IDialog from '@/components/dialog'
  import ISelect from '@/components/form/i-select'
  import { reqClassConsumeApply, reqStudentStore } from '@/api/coach-worktable'
  import { Toast } from 'vant'

  const props = defineProps({
    data: {
      type: Object,
      default: () => {},
    },
  })

  const emit = defineEmits(['afterSubmit'])

  // 是否展示授课类型 当学员只买该教练的一个种授课方式，此时不需要选择授课方式
  const isShowType = ref(false)
  const options = ref([])
  const render = ref(false)
  const curSelectType = ref(null)

  const placeholder = computed(() => {
    if (!curSelectType.value) return '请输入'
    return `剩余${curSelectType.value.quantity}个课时`
  })

  const form = reactive({
    quantity: '',
    teachingWay: '',
  })

  //表单校检
  let formValidator = new Schema({
    teachingWay: { required: true, message: '请选择授课类型' },
    quantity: { required: true, message: '请输入申请核销的课时数' },
  })

  const resetForm = () => {
    options.value = []
    form.quantity = null
    form.teachingWay = ''
    curSelectType.value = null
  }

  const getStudentStore = () => {
    let params = { studentUserId: props.data.studentUserId }
    reqStudentStore(params).then((res) => {
      const { data } = res
      isShowType.value = data.length > 1

      if (!isShowType.value) {
        curSelectType.value = data[0]
        form.teachingWay = curSelectType.value.teachingWay.type
      }

      data.forEach((item) => {
        options.value.push({
          text: item.teachingWay.typeName,
          id: item.teachingWay.type,
          quantity: item.quantity,
        })
      })
    })
  }

  const onTypeChange = (id, value) => {
    curSelectType.value = value
  }

  const beforeClose = (action) => {
    if (action === 'cancel') return true
    if (action === 'confirm') {
      return onSubmit()
    }
  }

  const onSubmit = () => {
    return new Promise((resolve) => {
      const formData = JSON.parse(JSON.stringify(form))
      formValidator
        .validate(formData)
        .then(() => {
          let params = { ...formData, studentUserId: props.data.studentUserId }
          reqClassConsumeApply(params)
            .then(() => {
              Toast('提交成功')
              resolve(true)
              emit('afterSubmit')
            })
            .catch(() => {
              resolve(false)
            })
        })
        .catch(({ errors }) => {
          Toast(errors[0].message)
          resolve(false)
        })
    })
  }

  const onDialogOpen = () => {
    resetForm()
    getStudentStore()
    render.value = true
  }

  const onDialogClose = () => {
    render.value = false
  }
</script>

<style lang="scss" scoped>
  .form {
    user-select: none;
    padding: 0 0.2rem 0.3rem 0.2rem;

    :deep(.van-cell) {
      padding: 0.14rem 0;
      border-bottom: 1px solid #eeeeee;
      align-items: center;

      .van-cell__right-icon {
        color: #bab9b9;
      }
    }

    :deep(.van-field__label) {
      font-size: 0.14rem;
      color: #453938;
    }

    :deep(.van-field__control) {
      &::placeholder {
        font-size: 0.14rem;
        color: #b2b1b7;
      }
    }

    .input-num {
      :deep(.van-stepper__input) {
        width: 100%;
        font-size: 0.18rem;
        color: #1a1b1d;
        font-weight: bold;
        background: #fff;
        text-align: left;

        &::placeholder {
          font-size: 0.14rem;
          color: #b2b1b7;
          font-weight: initial;
        }
      }
    }

    .input-content-center {
      :deep(.van-stepper) {
        width: 100%;
      }

      :deep(.van-stepper__input) {
        width: 100%;
        background: #fff;
        text-align: center;

        &::placeholder {
          font-size: 0.14rem;
          color: #b2b1b7;
          font-weight: initial;
          text-align: center;
        }
      }
    }
  }
</style>
