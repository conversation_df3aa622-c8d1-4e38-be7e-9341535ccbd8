<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content page-bg-white">
        <div class="form">
          <div class="title">提现金额（元）</div>
          <div class="input-wrap">
            <div class="symbol">¥</div>
            <input
              v-model="form.amount"
              class="input"
              readonly
              @touchstart.stop="keyboardShow = true"
              :placeholder="placeholder"
            />
          </div>
          <div v-if="wallet?.freezeBalance > 0" class="extract-tip">
            注：有¥{{ wallet?.freezeBalance || 0 }}元收益暂不可提现，
            <van-popover class="popover" v-model:show="showPopover" teleport="body">
              <div class="popover-content">上课收益，在核销成功后24小时才能提现</div>
              <template #reference>
                <span class="blue">原因</span>
              </template>
            </van-popover>
          </div>
        </div>

        <div class="select-wrap">
          <div class="title">提现到</div>
          <div class="select">
            <div
              v-for="item in platform"
              :key="item.name"
              :class="['item', { selected: form.account.accountType === item.code }]"
              @click="handledPlatformSelect(item)"
            >
              <div class="platform-wrap">
                <div class="platform-logo">
                  <img :src="item.logo" alt="" />
                </div>
                <div class="platform-name">{{ item.name }}</div>
              </div>
              <div v-if="form.account.accountType === item.code" class="icon-selected"></div>
            </div>
          </div>
        </div>

        <div v-if="form.account.accountType" class="form-item">
          <div class="title">真实姓名</div>
          <div class="input-wrap">
            <input
              class="input"
              v-model="form.account.accountName"
              placeholder="请输入提现账户所绑定的真实姓名"
            />
          </div>
        </div>

        <div v-if="form.account.accountType === payPlatform.alipay" class="form-item">
          <div class="title">提现账户</div>
          <div class="input-wrap">
            <input
              class="input"
              v-model="form.account.accountNumber"
              placeholder="请输入支付宝账户，如（手机号）"
            />
          </div>
        </div>

        <div v-show="footerShow" class="fixed-button" @click="onSubmit">
          <button class="button">立即提现</button>
        </div>

        <number-keyboard
          v-model="form.amount"
          :show="keyboardShow"
          theme="custom"
          extra-key="."
          close-button-text="完成"
          @blur="keyboardShow = false"
        />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import Schema from 'async-validator'
  import { NumberKeyboard, Toast, Dialog } from 'vant'
  import { verificationAmount } from '@/utils'
  import { reqCoachWallet, reqCoachWithdrawal } from '@/api/coach-worktable'
  import { reqUserWeChatInfo } from '@/api/user-server'
  import { payPlatform } from '@/common/enum'

  const router = useRouter()
  const showPopover = ref(false)
  let keyboardShow = ref(false)
  const footerShow = ref(true)
  const wallet = ref({}) // 钱包信息
  const userWeChartInfo = ref({}) // 用户微信信息

  const platform = ref([
    // {
    //   logo: require("../../../assets/images/coach-worktable/icon-wx.png"),
    //   code: payPlatform.weChat,
    //   name: "微信钱包",
    // },
    {
      logo: require('../../../assets/images/coach-worktable/icon-alipay.png'),
      code: payPlatform.alipay,
      name: '支付宝账户',
    },
  ])

  const placeholder = computed(() => {
    if (!wallet.value) return ''
    return `最多可提现¥${wallet.value.availableBalance || 0}元`
  })

  const form = ref({
    account: {
      accountType: payPlatform.alipay, // 支付类型 先默认为支付宝
      accountName: '',
      accountNumber: '', //账户号码，微信传openId
    },
    amount: '',
  })

  watch(
    () => form.value.amount,
    (newVal, oldVal) => {
      if (newVal === oldVal) return
      form.value.amount = verificationAmount(newVal)
    },
  )

  const formValidator = new Schema({
    amount: {
      required: true,
      validator(rule, value, callback) {
        if (value === '' || value === null) {
          callback('请输入提现金额')
        }

        if (value > wallet.value.availableBalance) {
          callback('输入的金额超过可提现金额')
        }

        if (value < 0.1) {
          callback('单次提现金额需大于或等于0.1元')
        }

        if (value > 20000) {
          callback('单次提现金额不能大于20000元')
        }

        callback()
      },
    },
    account: {
      type: 'object',
      required: true,
      fields: {
        accountType: { required: true, message: '请选择提现方式' },
        accountName: { required: true, message: '请输入真实姓名' },
        accountNumber: { required: true, message: '请输入提现账户' },
      },
    },
  })

  // 获取教练钱包信息
  const getCoachWallet = () => {
    reqCoachWallet().then((res) => {
      const { data } = res
      wallet.value = data
    })
  }

  const getUserWeChatInfo = () => {
    reqUserWeChatInfo().then((res) => {
      userWeChartInfo.value = res.data
    })
  }

  const handledPlatformSelect = (rowData) => {
    if (rowData.code === payPlatform.weChat && !userWeChartInfo.value?.publicOpenId) {
      Dialog.alert({
        title: '未绑定微信',
        message: '你的账号未绑定微信，无法提现到微信钱包',
      })
      return
    }

    if (rowData.code === payPlatform.weChat) {
      form.value.account.accountNumber = userWeChartInfo.value?.publicOpenId
    } else {
      form.value.account.accountNumber = ''
    }

    form.value.account.accountType = rowData.code
  }

  // 发起提现
  const onSubmit = () => {
    // 1. 非微信环境浏览页面，用户有绑定微信，则有openId可提现，没绑定微信，则需要提示用户去微信环境绑定微信才能使用次功能
    let params = JSON.parse(JSON.stringify(form.value))
    formValidator
      .validate(params, { first: true })
      .then(() => {
        reqCoachWithdrawal(params).then(() => {
          Dialog.alert({
            title: '提示',
            message: '申请成功，款项将在72小时内到账',
          }).then(() => {
            router.push({
              name: 'myWorktableWallet',
            })
          })
        })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  const init = () => {
    getCoachWallet()
    getUserWeChatInfo()
  }

  init()
</script>

<style lang="scss" scoped>
  .page-content {
    padding-bottom: 0.8rem;
  }

  .form {
    padding: 0 0.2rem;

    .title {
      padding: 0.15rem 0 0.06rem 0;
      font-size: 0.14rem;
      color: #616568;
    }

    .input-wrap {
      width: 3.35rem;
      padding: 0.06rem 0;
      display: flex;
      font-size: 0.26rem;
      font-weight: 600;
      color: #1a1b1d;
      border-bottom: 1px solid #eeeeee;

      input {
        width: 100%;
      }
    }

    .symbol {
      font-size: 0.26rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .input {
      margin-left: 0.1rem;
      flex: 1;
      width: 100%;
      border: none;

      &::placeholder {
        font-size: 0.14rem;
        color: #b2b1b7;
        font-weight: 400;
      }
    }

    .blue {
      color: #0083fc;
    }

    .extract-tip {
      margin-top: 0.06rem;
      font-size: 0.12rem;
      color: #b2b1b7;
    }
  }

  .popover {
    .popover-content {
      padding: 0.08rem 0.1rem;
      font-size: 0.12rem;
      color: #616568;
    }
  }

  .select-wrap {
    margin-top: 0.2rem;
    padding: 0 0.15rem;

    .title {
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .select {
      margin-top: 0.1rem;

      .item {
        display: flex;
        align-items: center;
        padding: 0.08rem 0.15rem;
        background: #ffffff;
        border-radius: 0.04rem;
        border: 1px solid #eeeeee;
        margin-bottom: 0.15rem;
      }

      .platform-wrap {
        display: flex;
        flex: 1;
        align-items: center;
      }

      .selected {
        border: 1px solid #ff9b26;
      }

      .icon-selected {
        width: 0.15rem;
        height: 0.15rem;
        background: url('../../../assets/images/coach-worktable/icon-selected.png') no-repeat;
        background-size: 100% 100%;
      }

      .platform-name {
        font-size: 0.14rem;
        color: #1a1b1d;
        margin-left: 0.06rem;
      }

      .platform-logo {
        width: 0.24rem;
        height: 0.24rem;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .form-item {
    padding: 0 0.15rem;
    margin-bottom: 0.16rem;

    .title {
      padding-left: 0.05rem;
      margin-bottom: 0.1rem;
      font-size: 0.14rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .input-wrap {
      padding-left: 0.05rem;
      border-bottom: 1px solid #eeeeee;
      input {
        width: 100%;
      }
    }

    .input {
      border: none;
      font-size: 0.15rem;
      padding-bottom: 0.17rem;

      &::placeholder {
        color: #b2b1b7;
      }
    }
  }

  .fixed-button {
    position: fixed;
    bottom: 0;
    background-color: #fff;
    width: 3.75rem;
    height: 0.6rem;
    padding: 0.1rem 0.15rem;
    text-align: center;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .button {
      width: 3.45rem;
      height: 0.4rem;
      font-size: 0.17rem;
      color: #ffffff;
      background-color: var(--i-primary);
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.23rem;
      cursor: pointer;
    }
  }
</style>
