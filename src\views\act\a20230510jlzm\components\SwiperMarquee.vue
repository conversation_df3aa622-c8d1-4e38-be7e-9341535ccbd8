<template>
  <div class="SwiperMarquee">
    <div
      class="marquee-content clearfix"
      :style="{ width: containerWidth + 'px', transform: transform }"
    >
      <div class="item omit" v-for="(item, index) in list" :key="index">
        <div class="box">
          <img
            :src="getOssURL(item.avatar + '?x-oss-process=image/resize,m_fill,h_480,w_480')"
            class="img"
            alt=""
          />
          <span>{{ item.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { getOssURL } from '@/common'

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    time: {
      type: Number,
      default: 40,
    },
  })

  const timer = ref(null)
  const move = ref(375)
  const transform = ref('')

  const containerWidth = computed(() => {
    if (props.list.length > 0) {
      return props.list.length * 324
    }
    return 9999
  })

  const startScrollView = () => {
    timer.value = setInterval(() => {
      move.value -= 2
      transform.value = `translateX(${move.value}px)`
      if (Math.abs(move.value) >= containerWidth.value) {
        move.value = 375
        transform.value = `translateX(${move.value}px)`
      }
    }, props.time)
  }

  onMounted(() => {
    startScrollView()
  })

  onUnmounted(() => {
    clearInterval(timer.value)
    timer.value = null
  })
</script>

<style lang="scss">
  .SwiperMarquee {
    margin-bottom: 0.04rem;
    .marquee-content {
      width: 99999px;
      white-space: nowrap;

      .item {
        width: 324px;
        float: left;
      }

      .box {
        display: inline-block;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        border-radius: 0.2rem;
        height: 0.2rem;
        line-height: 0.2rem;
        font-size: 0.12rem;
        padding-right: 0.1rem;

        .img {
          width: 0.2rem;
          height: 0.2rem;
          border-radius: 50%;
          float: left;
          margin-right: 0.06rem;
        }
      }

      .name {
        max-width: 1.04rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
