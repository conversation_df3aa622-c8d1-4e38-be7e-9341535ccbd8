<template>
  <van-popup
    round
    v-model:show="show"
    :closeable="true"
    @close="onClose"
    position="bottom"
    :style="{ height: '3.03rem' }"
  >
    <div class="popup-wrap">
      <div class="title">
        <p>更多活动福利等你解锁</p>
        <p>请长按识别添加小爱企微</p>
      </div>
      <div class="qrcode">
        <img v-if="isParticipateAct" src="../images/jiaolian-kf.png" alt="" />
        <img v-else src="../images/xueyuan-kf.png" alt="" />
      </div>
      <div class="tip"><i class="icon-press"></i><span>长按识别二维码</span></div>
    </div>
  </van-popup>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import { useParent } from '@vant/use'
  const { parent } = useParent('321ACT')

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  watch(
    () => props.modelValue,
    (val) => (show.value = val),
  )

  // 是否报名参加活动
  const isParticipateAct = computed(() => {
    if (!parent.actInfo.value) return false
    return parent.actInfo.value.btnType === 2 || parent.actInfo.value.btnType === 3
  })

  const show = ref(props.modelValue || false)

  const updateShow = (state) => emit('update:modelValue', state)

  const onClose = () => {
    updateShow(false)
  }
</script>

<style lang="scss" scoped>
  .popup-wrap {
    .title {
      text-align: center;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1f1f1f;
      padding: 0.2rem 0 0.14rem 0;
    }

    .qrcode {
      width: 1.58rem;
      height: 1.58rem;
      background: #ffffff;
      border: 1px solid #d8d8d8;
      margin: 0 auto;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .tip {
      margin-top: 0.11rem;
      font-size: 0.12rem;
      color: #616568;
      text-align: center;
    }

    .icon-press {
      width: 0.19rem;
      height: 0.23rem;
      margin-right: 0.04rem;
      background: url('../images/icon-press.png') no-repeat;
      background-size: 100% 100%;
      display: inline-block;
      vertical-align: bottom;
    }
  }
</style>
