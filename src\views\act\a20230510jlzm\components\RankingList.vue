<template>
  <div class="ranking-list">
    <div class="tip" @click="openContactPopup">
      <img src="../images/welfare-bg.png" alt="" />
    </div>

    <div class="table">
      <div class="ranking-list-head">
        <div class="ranking-list-desc">
          <!-- <button class="gift-btn" @click="openContactPopup">
            <img src="../images/icon-kf.png" alt="" />领取礼包
          </button> -->
          <div class="num">{{ count }}</div>
          <div class="num-text">名教练上榜</div>
          <img class="refresh-btn" @click="onRefresh" src="../images/refresh-btn.png" alt="" />
        </div>
      </div>
      <div class="rank-head">
        <div class="rank-coach-box">
          <div class="coach-box second" v-if="list.length > 1">
            <img
              v-default-avatar
              class="coach-avatar"
              :src="
                getOssURL(list[1].coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
              "
              @click="onItemClick(list[1])"
              alt=""
            />
            <div class="rank-coach-info">
              <div>
                <p class="coach-name omit">
                  {{
                    list[1].coachName.length > 5
                      ? list[1].coachName.slice(0, 5) + '..'
                      : list[1].coachName
                  }}
                  <img
                    @click="openSharePopup(list[1])"
                    class="share-icon"
                    src="../images/icon-share-yellow.png"
                    alt=""
                  />
                </p>
                <p class="coach-tag omit">{{ list[1].coachTitle }}</p>
              </div>
              <div class="praise-num">
                <div class="likes" :class="{ purple: list[1].likeCount > 0 }">
                  {{ list[1].likeCount }}<span>赞</span>
                </div>
              </div>
              <div v-if="isGiveLikeBtn" class="action-icon" @click="onGiveLike(list[1])">
                <div class="icon-praise" :class="{ active: list[1].isGiveLike }" />
              </div>
              <p class="number-count">No.2</p>
            </div>
          </div>
          <div class="coach-box first" v-if="list.length > 0">
            <img
              v-default-avatar
              class="coach-avatar"
              :src="
                getOssURL(list[0].coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
              "
              @click="onItemClick(list[0])"
              alt=""
            />
            <div class="rank-coach-info">
              <div>
                <p class="coach-name omit">
                  {{
                    list[0].coachName.length > 5
                      ? list[0].coachName.slice(0, 5) + '..'
                      : list[0].coachName
                  }}
                  <img
                    @click="openSharePopup(list[0])"
                    class="share-icon"
                    src="../images/icon-share-yellow.png"
                    alt=""
                  />
                </p>
                <p class="coach-tag omit">{{ list[0].coachTitle }}</p>
              </div>
              <div class="praise-num">
                <div class="likes" :class="{ purple: list[0].likeCount > 0 }">
                  {{ list[0].likeCount }}<span>赞</span>
                </div>
              </div>
              <div v-if="isGiveLikeBtn" class="action-icon" @click="onGiveLike(list[0])">
                <div class="icon-praise" :class="{ active: list[0].isGiveLike }" />
              </div>
              <p class="number-count">No.1</p>
            </div>
          </div>
          <div class="coach-box thirdly" v-if="list.length > 2">
            <img
              v-default-avatar
              class="coach-avatar"
              :src="
                getOssURL(list[2].coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
              "
              @click="onItemClick(list[2])"
              alt=""
            />
            <div class="rank-coach-info">
              <div>
                <p class="coach-name omit">
                  {{
                    list[2].coachName.length > 5
                      ? list[2].coachName.slice(0, 5) + '..'
                      : list[2].coachName
                  }}
                  <img
                    @click="openSharePopup(list[2])"
                    class="share-icon"
                    src="../images/icon-share-yellow.png"
                    alt=""
                  />
                </p>
                <p class="coach-tag omit">{{ list[2].coachTitle }}</p>
              </div>
              <div class="praise-num">
                <div class="likes" :class="{ purple: list[2].likeCount > 0 }">
                  {{ list[2].likeCount }}<span>赞</span>
                </div>
              </div>
              <div v-if="isGiveLikeBtn" class="action-icon" @click="onGiveLike(list[2])">
                <div class="icon-praise" :class="{ active: list[2].isGiveLike }" />
              </div>
              <p class="number-count">No.3</p>
            </div>
          </div>
        </div>
      </div>
      <div class="table-thead">
        <div class="col-1-width th">排名</div>
        <div class="col-2-width th">教练</div>
        <div class="col-3-width th" style="text-align: center; padding-left: 0">拉赞/点赞</div>
      </div>
      <div class="table-body">
        <div v-for="(item, index) in list" :key="index" class="row">
          <template v-if="index > 2">
            <div class="col-1-width col-1">
              <span>{{ index + 1 }}</span>
            </div>
            <div class="col-2-width col-2">
              <div class="coach">
                <img
                  v-if="item.coachId"
                  v-default-avatar
                  class="coach-avatar"
                  :src="
                    getOssURL(item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
                  "
                  @click="onItemClick(item)"
                  alt=""
                />
                <van-popover
                  v-else
                  v-model:show="item.showPopover"
                  :show-arrow="false"
                  placement="top-start"
                  :offset="[-25, -60]"
                >
                  <div class="popover">
                    <img
                      v-default-avatar
                      class="avatar"
                      :src="
                        getOssURL(
                          item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480',
                        )
                      "
                    />
                    <div class="user-info">
                      <p class="username omit">
                        {{ item.coachName }}
                      </p>
                      <p class="desc omit">{{ item.coachTitle }}</p>
                      <div class="tags">
                        <span v-if="item.teachYear">{{ item.teachYear }}年经验</span>
                        <span v-if="item.teachArea">{{ item.teachArea }}</span>
                      </div>
                    </div>
                  </div>
                  <template #reference>
                    <img
                      v-default-avatar
                      class="coach-avatar"
                      :src="
                        getOssURL(
                          item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480',
                        )
                      "
                      alt=""
                    />
                  </template>
                </van-popover>
                <div class="coach-info">
                  <div class="flex" style="align-items: center">
                    <span class="coach-name omit">{{ item.coachName }}</span>
                    <span class="coach-tag omit">{{ item.coachTitle }}</span>
                  </div>
                  <div class="praise-num">
                    <div class="likes" :class="{ purple: item.likeCount > 0 }">
                      {{ item.likeCount }}<span>赞</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-4-width col-3">
              <div class="action">
                <div v-if="isGiveLikeBtn" class="action-icon" @click="onGiveLike(item)">
                  <div class="icon-praise" :class="{ active: item.isGiveLike }" />
                  <span>点赞</span>
                </div>
                <div class="action-icon" @click="openSharePopup(item)">
                  <div class="icon-share" />
                  <span>分享</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="fetch-more">
        <div v-if="loading" class="list-loading-text">
          <van-loading size="0.14rem">加载中...</van-loading>
        </div>
        <div v-if="finished" class="list-finished-text">- 没有更多了 -</div>
        <button v-else-if="!finished && !loading" class="more-btn" @click="onLoadMore">
          查看更多教练
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { reqActRankingList, reqUserLike } from '../api'
  import { getOssURL, isLogin } from '@/common'
  import { useParent } from '@vant/use'
  import { Toast } from 'vant'

  const router = useRouter()
  const { parent } = useParent('321ACT')

  let group = []

  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const count = ref(0)

  const isGiveLikeBtn = computed(() => {
    return !parent.shareUserId || parent.shareUserId === parent.actInfo.value.loginUserId
  })

  const slicer = (data, len) => {
    if (!Array.isArray(data)) return []

    const arr = []
    for (let i = 0; i < data.length; i += len) {
      arr.push(data.slice(i, i + len))
    }
    return arr
  }

  const getRankingList = () => {
    loading.value = true
    reqActRankingList().then((res) => {
      const { data } = res
      count.value = data.count

      data.rankingList.forEach((item) => {
        item.showPopover = false
        item.isGiveLike = parent.actInfo.value.currentLikeCoachUserId === item.coachUserId
      })

      list.value = data.rankingList.splice(0, 20)
      loading.value = false
      finished.value = count.value <= 20

      group = slicer(data.rankingList, 10)
    })
  }

  const onLoadMore = () => {
    loading.value = true
    setTimeout(() => {
      list.value = list.value.concat(...group.splice(0, 1))
      loading.value = false
      finished.value = list.value.length === count.value
    }, 50)
  }

  const onRefresh = () => {
    group.length = 0
    finished.value = false
    list.value.length = 0
    getRankingList()
  }

  const openSharePopup = (data) => {
    parent.openSharePopup(data)
  }

  const onGiveLike = (item) => {
    if (isLogin()) {
      if (parent.actInfo.value.currentLikeCoachUserId) {
        if (!item.isGiveLike) {
          Toast('今日点赞次数用完啦，可以帮ta拉赞喔！')
        }
        return
      }

      let params = { coachUserId: item.coachUserId }
      reqUserLike(params).then(() => {
        // 开始点赞动画
        parent.playLikeAnim()
        parent.actInfo.value.currentLikeCoachUserId = item.coachUserId
        onRefresh()

        setTimeout(() => {
          if (
            parent.actInfo.value.loginUserId === item.coachUserId &&
            parent.actInfo.value.btnType === 2
          ) {
            parent.openSharePopup()
          } else {
            parent.openGiftPopup()
          }
        }, 3000)
      })
    } else {
      parent.openLoginPopup()
    }
  }

  const onItemClick = (item) => {
    if (!item.coachId) return
    router.push('/coach/details/' + item.coachId)
  }

  const openContactPopup = () => {
    parent.showContactPopup()
  }

  getRankingList()
</script>

<style lang="scss" scoped>
  .tip {
    text-align: center;

    img {
      width: 3rem;
      height: 0.53rem;
    }
  }

  .ranking-list-head {
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 0.55rem;
    .ranking-list-desc {
      margin-top: 0.04rem;
      line-height: 0.22rem;
      font-size: 0.12rem;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .gift-btn {
        width: 0.8rem;
        height: 0.22rem;
        line-height: 0.22rem;
        background: rgba(243, 51, 8, 0.72);
        border-radius: 0.1rem;
        color: #fff;
        font-size: 0.12rem;

        img {
          width: 0.13rem;
          height: 0.15rem;
          margin-right: 0.03rem;
          vertical-align: sub;
        }
      }

      .refresh-btn {
        width: 0.81rem;
        height: 0.27rem;
        margin-right: 0.27rem;
      }

      .num {
        font-size: 0.18rem;
        color: #f4370b;
        margin-right: 0.02rem;
        font-weight: bold;
      }
      .num-text {
        margin-right: 0.15rem;
      }

      span {
        display: inline-block;
      }
    }
  }

  .table {
    width: 3.75rem;
    // margin: 0 0.12rem;
    border-radius: 0.1rem;
    // box-shadow: 0 0 0.06rem 0 #fec202;
    // background: #fff;
    min-height: 2rem;
    overflow: hidden;
    position: relative;

    .col-1-width {
      width: 0.46rem;
    }

    .col-2-width {
      width: 2.1rem;
    }

    .col-3-width {
      width: 0.95rem;
    }
    .col-4-width {
      width: 0.69rem;
      margin-left: auto;
    }
    .table-thead {
      background: #fffbee;
      display: flex;
      width: 3.48rem;
      margin: 0 auto;
      border-radius: 0.07rem 0.07rem 0 0;
      margin-top: -0.07rem;
      border-left: 0.01rem solid #7719be;
      border-right: 0.01rem solid #7719be;
      .th {
        font-size: 0.12rem;
        color: #979797;
        padding: 0.07rem 0 0.09rem 0.1rem;
      }
    }
    .table-body {
      border-left: 0.01rem solid #7719be;
      border-right: 0.01rem solid #7719be;
      width: 3.48rem;
      margin: 0 auto;
      background: #fffbee;
    }

    .row {
      display: flex;
      align-items: center;
    }

    .col-1 {
      font-weight: 600;
      color: #979797;
      text-align: center;
    }
  }

  .fetch-more {
    // padding: 0.15rem 0;
    background: #5a7cff;
    width: 3.48rem;
    text-align: center;
    margin: 0 auto;
    border-radius: 0 0 0.07rem 0.07rem;

    .list-loading-text,
    .list-finished-text {
      color: #fff;
      font-size: 0.12rem;
      line-height: 0.32rem;
      text-align: center;
    }

    .more-btn {
      width: 2.24rem;
      height: 0.32rem;
      background: #5a7cff;
      border-radius: 0.16rem;
      font-size: 0.12rem;
      color: #fff;
    }
  }

  .coach {
    display: flex;
    padding: 0.08rem 0;

    .coach-avatar {
      width: 0.44rem;
      height: 0.44rem;
      object-fit: cover;
      border-radius: 50%;
      margin-right: 0.1rem;
      border: 1px solid #7719be;
    }

    .coach-info {
      .coach-name {
        max-width: 0.6rem;
        font-size: 0.15rem;
        color: #1f1f1f;
        font-weight: bold;
      }

      .coach-tag {
        max-width: 0.96rem;
        font-size: 0.12rem;
        color: #616568;
        margin-left: 0.06rem;
      }

      .praise-num {
        font-size: 0.12rem;
        font-family: PingFangSC-Medium, PingFang SC;
        color: #b2b1b7;
        .likes {
          font-weight: bold;
        }
      }

      .purple {
        font-size: 0.16rem;
        color: #376bfe;

        span {
          font-size: 0.14rem;
          font-weight: 400;
          margin-left: 0.02rem;
        }
      }
    }
  }

  .action {
    display: flex;

    .action-icon {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .icon-praise {
        width: 0.18rem;
        height: 0.18rem;
        background: url('../images/icon-praise.png') no-repeat;
        background-size: 100% 100%;
      }

      .active {
        background: url('../images/icon-praise-active.png') no-repeat;
        background-size: 100% 100%;
      }

      .icon-share {
        width: 0.18rem;
        height: 0.18rem;
        background: url('../images/icon-share.png') no-repeat;
        background-size: 100% 100%;
      }

      span {
        margin-top: 0.03rem;
        font-size: 0.12rem;
        color: #979797;
      }
    }
  }

  .medal-1 {
    width: 0.23rem;
    height: 0.26rem;
    display: inline-block;
    background: url('../images/medal-1.png') no-repeat;
    background-size: 100% 100%;
  }

  .medal-2 {
    width: 0.23rem;
    height: 0.26rem;
    display: inline-block;
    background: url('../images/medal-2.png') no-repeat;
    background-size: 100% 100%;
  }

  .medal-3 {
    width: 0.23rem;
    height: 0.26rem;
    display: inline-block;
    background: url('../images/medal-3.png') no-repeat;
    background-size: 100% 100%;
  }

  .popover {
    width: 3.17rem;
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
    border-radius: 0.1rem;
    display: flex;
    padding: 0.12rem 0 0.12rem 0.12rem;

    .avatar {
      width: 0.7rem;
      height: 0.7rem;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 0.12rem;
    }

    .user-info {
      flex: 1;
      width: 2rem;
      padding-right: 0.1rem;
    }

    .username {
      font-size: 0.2rem;
      font-weight: 600;
      color: #1f1f1f;
      line-height: 0.28rem;
    }

    .desc {
      color: #1f1f1f;
      line-height: 0.2rem;
    }

    .tags {
      margin-top: 0.05rem;
      span {
        font-size: 0.13rem;
        padding: 0.02rem 0.06rem;
        background: #fff3e5;
        color: #ff6445;
        border-radius: 0.02rem;
        margin-right: 0.04rem;
      }
    }
  }

  .rank-head {
    width: 3.75rem;
    height: 3.14rem;
    background: url('../images/rank-head.png') no-repeat;
    background-size: 100% 100%;

    .rank-coach-box {
      width: 3.5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      .coach-box {
        width: 33%;
        text-align: center;
        .coach-avatar {
          width: 0.6rem;
          height: 0.6rem;
          border: 1px solid #7719be;
          border-radius: 50%;
        }
        .rank-coach-info {
          .coach-name {
            color: #000;
            font-weight: bold;
            font-size: 0.14rem;
            .share-icon {
              width: 0.22rem;
              height: 0.22rem;
              vertical-align: bottom;
            }
          }
          .coach-tag {
            color: #000;
            font-size: 0.12rem;
            margin-top: 0.01rem;
          }
          .praise-num {
            color: rgba(0, 0, 0, 0.4);
            font-size: 0.12rem;
            font-weight: bold;
            margin-top: 1px;
          }
          .action-icon {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            margin-top: 0.12rem;
            .icon-praise {
              width: 0.21rem;
              height: 0.21rem;
              background: url('../images/icon-praise2.png') no-repeat;
              background-size: 100% 100%;
            }

            .active {
              background: url('../images/icon-praise-active2.png') no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }
      .first {
        position: relative;
        &:before {
          content: '';
          width: 0.21rem;
          height: 0.21rem;
          position: absolute;
          left: 0.73rem;
          top: -0.12rem;
          background: url('../images/rank-1.png') no-repeat;
          background-size: 100% 100%;
          z-index: 2;
        }
        .number-count {
          color: rgba(59, 68, 102, 0.4);
          font-size: 0.17rem;
          font-weight: 800;
          text-align: center;
        }
        .action-icon {
          padding-top: 0.18rem;
        }
      }
      .second {
        position: relative;
        &:before {
          content: '';
          width: 0.21rem;
          height: 0.21rem;
          position: absolute;
          left: 0.73rem;
          top: -0.12rem;
          background: url('../images/rank-2.png') no-repeat;
          background-size: 100% 100%;
          z-index: 2;
        }
        .number-count {
          color: rgba(59, 68, 102, 0.4);
          font-size: 0.13rem;
          font-weight: 800;
          text-align: center;
        }
      }
      .thirdly {
        position: relative;
        &:before {
          content: '';
          width: 0.21rem;
          height: 0.21rem;
          position: absolute;
          left: 0.73rem;
          top: -0.12rem;
          background: url('../images/rank-3.png') no-repeat;
          background-size: 100% 100%;
          z-index: 2;
        }
        .number-count {
          color: rgba(59, 68, 102, 0.4);
          font-size: 0.11rem;
          font-weight: 800;
          text-align: center;
        }
      }
      .first {
        margin-top: 0.89rem;
      }
      .second {
        margin-top: 1.07rem;
      }
      .thirdly {
        margin-top: 1.3rem;
      }
    }
  }
</style>
