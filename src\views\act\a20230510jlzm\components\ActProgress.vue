<template>
  <!--  活动进行中的排行榜 -->
  <div class="ActProgress">
    <Coach v-if="actInfo.helpCoachInfo" />
    <RankingList />
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useParent } from '@vant/use'
  import RankingList from './RankingList'
  import Coach from './Coach'

  const { parent } = useParent('321ACT')
  const actInfo = ref(parent.actInfo.value)
</script>

<style lang="scss" scoped></style>
