import ImageUploader from 'quill-image-uploader'
import { uploadFileOss } from '@/api/generic-server'
import { ossURLJoin } from '@/common'

export default {
  name: 'imageUploader',
  module: ImageUploader,
  options: {
    upload: (file) => {
      return new Promise((resolve, reject) => {
        let formData = new FormData()
        formData.append('file', file)

        uploadFileOss(formData).then((res) => {
          const { data, code } = res
          if (code === 0) {
            resolve(ossURLJoin(data))
          } else {
            reject()
          }
        })
      })
    },
  },
}
