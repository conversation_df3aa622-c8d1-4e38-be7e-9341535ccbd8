<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="attent-class-page">
        <empty v-if="emptyShow" top="2.41rem" description="你还没有专属教练<br >快去挑选教练吧～" />
        <van-list
          class="trainer"
          v-model:loading="loading"
          :finished="finished"
          :finished-text="classList.length > 0 ? '-没有更多了-' : null"
          @load="onLoad"
        >
          <div
            v-for="(item, index) in classList"
            :key="index"
            class="class-item"
            @click="toClassDetail(item)"
          >
            <div class="header flex">
              <p>
                剩余课时：<span>{{ item.showRemainQuantity }}个课时</span>
              </p>
              <div class="state">{{ item.studyStatus.statusName }}</div>
            </div>
            <div class="content flex">
              <div class="content-l flex">
                <img :src="getOssURL(item.coachInfo.avatarUrl)" alt="" v-error-img />
                <div class="coach-box flex">
                  <h3>{{ item.coachInfo.coachName }}</h3>
                  <div v-if="item.coachInfo.coachDescription" class="position">
                    {{ ellipsis(item.coachInfo.coachDescription) }}
                  </div>
                  <p>
                    <template v-if="item.studyStatus.status === 'WAITING_CLASS'">
                      下单时间：{{ sliceStr(item.orderTime, 0, 10) }}
                    </template>
                    <template v-else>
                      最近上课：{{ sliceStr(item.lastConsumeTime, 0, 10) }}</template
                    >
                  </p>
                </div>
              </div>
              <div class="content-r">
                <!-- 课程已结束 -->
                <div
                  v-if="item.studyStatus.status === 'FINISH'"
                  class="class-btn"
                  @click.stop="continueLearn(item)"
                >
                  继续上课
                </div>
                <a @click.stop v-else :href="'tel:' + item.coachInfo.mobile">
                  <div class="class-btn">联系上课</div>
                </a>
              </div>
            </div>
            <div v-if="item.consumeInfo && item.consumeInfo.id" class="class-details">
              <div class="details-box">
                <div class="time flex">
                  <p>待处理</p>
                  <!--                  <van-count-down class="time-stamp" :time="countDown(item)">-->
                  <!--                    <template #default="timeData">-->
                  <!--                      还剩-->
                  <!--                      <span v-if="timeData.hours > 0" class="block">{{ timeData.hours }}时</span>-->
                  <!--                      <span class="block">{{ timeData.minutes }}分钟</span>-->
                  <!--                    </template>-->
                  <!--                  </van-count-down>-->
                </div>
                <div class="details-txt">
                  教练{{ item.coachInfo.coachName }}申请核销课时：{{
                    item.consumeInfo.consumeQuantity
                  }}个课时 （{{ item.consumeInfo.teachingWay.typeName }}）
                </div>
                <div class="operation flex">
                  <div class="refuse" @click.stop="refuseFn(item)">拒绝</div>
                  <div class="agree" @click.stop="agreeFn(item)">同意</div>
                </div>
                <p class="oper-time">
                  {{ dateFormat(item.consumeInfo.applyTime, 'YYYY/MM/DD HH:mm') }}
                </p>
              </div>
            </div>
          </div>
        </van-list>
        <RefusePop v-model:show="showRefusePop" :refuseInfo="refuseInfo" @refresh="refreshList" />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { agreeConsume, getMyCoachList } from '@/api/trade-server'
  import { Dialog, Toast } from 'vant'
  import RefusePop from './refuse-pop.vue'
  import { dateFormat } from '@/utils/day'
  import Empty from '@/components/empty'
  import { getOssURL } from '@/common'
  import { sliceStr } from '@/utils'
  import { useRouter } from 'vue-router'
  // import { getDateTime } from "@/utils/day";

  const router = useRouter()

  const loading = ref(false)
  const finished = ref(false)
  const classList = ref([])
  const emptyShow = ref(false)
  // 上课请求参数
  const pagination = reactive({
    pageNum: 0,
    pageSize: 12,
  })

  const ellipsis = (item) => {
    if (!item) return ''
    if (item.split('').length > 7) {
      return item.substring(0, 7) + '...'
    } else {
      return item
    }
  }

  const onLoad = () => {
    pagination.pageNum += 1
    getMyCoachList(pagination)
      .then((res) => {
        let { data } = res
        classList.value = classList.value.concat(data)
        emptyShow.value = classList.value.length === 0
        // 加载结束
        loading.value = false
        // 数据全部加载完成
        if (data.length === 0 || data.length < pagination.pageSize) {
          finished.value = true
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true
      })
  }

  const agreeFn = (item) => {
    Dialog.confirm({
      confirmButtonText: '确认核销',
      message() {
        return (
          <div>
            <div>核销课时</div>
            <div style="color: var(--i-primary);font-size: 0.12rem;margin-top: 0.08rem">
              <p>为了您的上课权益，请勿在未上课前同意核销</p>
              <p>否则，因核销引起的权益损失平台概不负责。</p>
            </div>
          </div>
        )
      },
    })
      .then(() => {
        const params = {
          id: item.consumeInfo.id,
        }
        agreeConsume(params).then(() => {
          Toast('已同意核销')
          refreshList()
        })
      })
      .catch(() => {})
  }

  // 继续上课
  const continueLearn = (item) => {
    router.push({
      path: `/coach/details/${item.coachInfo.coachId}`,
      query: {
        isShowSku: true,
      },
    })
  }

  const toClassDetail = (item) => {
    router.push({
      name: 'studentClassDetails',
      query: {
        coachUserId: item.coachInfo.coachUserId,
      },
    })
  }
  const showRefusePop = ref(false)
  const refuseInfo = ref({})
  const refuseFn = (item) => {
    showRefusePop.value = true
    refuseInfo.value = item.consumeInfo
    refuseInfo.value['statusName'] = item.consumeInfo.teachingWay.typeName
  }

  // const countDown = computed(() => {
  //   return (rowData) => {
  //     const startTime = getDateTime();
  //     const endTime = getDateTime(rowData.consumeInfo.auditDeadTime);
  //     return endTime - startTime;
  //   };
  // });

  const refreshList = () => {
    pagination.pageNum = 0
    finished.value = false
    loading.value = false
    classList.value = []
    onLoad()
  }
</script>

<style scoped lang="scss">
  .flex {
    display: flex;
  }
  .attent-class-page {
    padding: 0.08rem;
  }
  .class-item {
    border-radius: 0.06rem;
    margin-bottom: 0.08rem;
    background-color: #fff;
    .header {
      font-size: 0.14rem;
      padding: 0.1rem 0.15rem;
      justify-content: space-between;
      border-bottom: 0.01rem solid #eee;
      p {
        color: #979797;
        span {
          font-weight: 600;
          color: #1f1f1f;
        }
      }
      .state {
        color: #ff9b26;
      }
    }
    .content {
      align-items: center;
      padding: 0.12rem 0.15rem;
      .content-l {
        align-items: center;
        img {
          object-fit: cover;
          margin-right: 0.1rem;
          width: 0.5rem;
          height: 0.5rem;
          flex-shrink: 0;
          border-radius: 50%;
          border: 0.01rem solid #eee;
        }
        .coach-box {
          flex-wrap: wrap;
          align-items: center;
          font-size: 0.12rem;
          h3 {
            margin-right: 0.06rem;
            color: #1a1b1d;
            font-weight: 600;
            font-size: 0.14rem;
          }
          .position {
            // width: 1.5rem;
            padding: 0.02rem 0.1rem;
            border-radius: 0.11rem;
            border: 0.01rem solid #eeeeee;
          }
          p {
            margin-top: 0.05rem;
            flex-shrink: 0;
            width: 1.5rem;
          }
        }
      }
      .content-r .class-btn {
        width: 0.92rem;
        height: 0.32rem;
        line-height: 0.32rem;
        text-align: center;
        background: #ffffff;
        font-size: 0.14rem;
        color: #ff9b26;
        border-radius: 0.16rem;
        border: 0.01rem solid #ff9b26;
      }
    }
    .class-details {
      padding: 0.1rem 0.15rem;
      .details-box {
        background: #f7f7f7;
        padding: 0.1rem 0.12rem;
        border-radius: 0.06rem;

        .time {
          color: #ff9b26;
          font-weight: 600;
          font-size: 0.18rem;
          .time-stamp {
            color: #ff9b26;
            margin-left: 0.08rem;
            font-size: 0.14rem;
            margin-top: 0.05rem;
          }
        }
        .details-txt {
          width: 3.2rem;
          margin: 0.06rem 0 0.15rem;
          font-size: 0.14rem;
          color: #1a1b1d;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .operation {
          .refuse,
          .agree {
            width: 0.84rem;
            height: 0.32rem;
            line-height: 0.32rem;
            text-align: center;
            background: #ffffff;
            border-radius: 0.16rem;
            border: 0.01rem solid #ff9b26;
            color: #ff9b26;
            font-size: 0.14rem;
          }
          .agree {
            margin-left: 0.12rem;
            background: #ff9b26;
            color: #ffffff;
          }
        }
        .oper-time {
          margin-top: 0.1rem;
          font-size: 0.12rem;
          color: #b2b1b7;
        }
      }
    }
  }

  .warning {
    color: var(--i-primary);
    font-size: 0.14rem;
    margin-top: 0.08rem;
  }
</style>
