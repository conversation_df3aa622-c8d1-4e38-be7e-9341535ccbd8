import axios from 'axios'
import { Toast } from 'vant'
// import { toLogin } from '@/common'
import { localProxyStorage } from '@/utils/storage'
import { aiApiURL } from '@/config'
// import { clearStorage } from '@/utils'
import router from '@/router'

const contentType = {
  form: 'application/x-www-form-urlencoded',
  json: 'application/json',
  file: 'multipart/form-data',
}

const http = axios.create({
  baseURL: process.env.VUE_APP_RUN_ENV !== 'development' ? aiApiURL : '',
  timeout: 3000000,
  headers: {
    'Content-Type': contentType.json,
  },
})

// 添加请求拦截器
http.interceptors.request.use(
  function (config) {
    if (localProxyStorage?.user?.authToken) {
      config.headers['auth-token'] = localProxyStorage.user.authToken
    }
    config.headers['Content-Type'] = contentType[config.contentType || 'json']
    // config.headers["x-version"] = "25";
    // config.headers["x-first-ip"] = "*********";
    // 在发送请求之前做些什么
    return config
  },
  function (error) {
    // 对请求错误做些什么
    return Promise.reject(error)
  },
)

// 添加响应拦截器
http.interceptors.response.use(
  function (response) {
    // 对响应数据做点什么
    const { status, data } = response
    if (status === 200) {
      if (data.code === 0) {
        return data
      } else if (data.code === 401) {
        localStorage.clear()
        sessionStorage.clear()

        Toast('登录状态已过期，请重新进入页面')
        if (process.env.VUE_APP_RUN_ENV !== 'development') {
          router.go(0)
        }
        return Promise.reject(data)
      } else {
        Toast(data.msg || '接口异常')
        return Promise.reject(data)
      }
    } else {
      console.log('网络请求异常：', status)
      Toast('网络请求异常')
    }

    return response
  },
  function (error) {
    if (error?.response?.data?.code === 401) {
      localStorage.clear()
      sessionStorage.clear()

      Toast('登录状态已过期，请重新进入页面')
      return Promise.reject(error)
    }

    // 对响应错误做点什么
    Toast('服务器异常，请稍后再试')
    return Promise.reject(error)
  },
)

export default http
