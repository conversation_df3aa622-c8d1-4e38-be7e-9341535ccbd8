export function isElemVisibleRange(el) {
  // viewPortHeight 兼容所有浏览器写法
  const viewPortHeight =
    window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight

  const offsetTop = el.offsetTop
  // scrollTop 兼容性问题
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  const top = offsetTop - scrollTop
  // 这里 +100 是为了提前加载
  return top <= viewPortHeight + 100
}

// 元素是否出现在屏幕内
export function isInViewPort(element) {
  const viewHeight = window.innerHeight || document.documentElement.clientHeight
  const { top, bottom } = element.getBoundingClientRect()
  return top <= viewHeight && bottom - 44 >= 0
}
