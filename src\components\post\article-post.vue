<template>
  <div class="article-post feedback">
    <div class="article-time">
      <span class="f10">{{ sliceStr(post.releaseTime, 0, 16) || '-' }}</span>
    </div>
    <div class="content">
      <div class="article-info">
        <div class="article-title" :class="{ 'no-cover-title': !isCover }">
          {{ post.title }}
        </div>
        <div class="article-other">
          <div class="article-tag">
            <span class="f10">文章</span>
          </div>
          <div class="article-author">
            <span class="author-max-w f10 omit">{{ post.realName }}</span>
          </div>
          <div class="article-pageviews">
            <span class="f10">{{ getPagesview(post) }} 浏览</span>
          </div>
        </div>
      </div>
      <div class="article-cover" v-if="isCover">
        <img :src="getCover()" alt="" />
      </div>
    </div>
    <slot name="state" />
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { ossURLJoin } from '@/common'
  import { sliceStr } from '@/utils'

  let props = defineProps({
    post: Object,
  })

  const isCover = computed(() => {
    if (Array.isArray(props.post.coverImage)) {
      return props.post.coverImage.length > 0
    }
    return false
  })

  // 浏览量
  const getPagesview = (post) => {
    let count = post.readCount
    if (count > 999) {
      return '999+'
    }
    return count
  }

  const getCover = () => {
    if (Array.isArray(props.post.coverImage)) {
      if (isCover.value) {
        return ossURLJoin(props.post.coverImage[0])
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  .article-post {
    display: flex;
    flex-direction: column;
    padding: 0.1rem 0.12rem 0.1rem 0.15rem;
    border-bottom: 1px solid #f2f2f2;

    .article-info {
      flex: 1;
      padding-right: 0.1rem;
    }

    .content {
      display: flex;
    }

    .article-title {
      min-height: 0.44rem;
      font-size: 0.15rem;
      line-height: 0.24rem;
      color: #414141;
      margin-bottom: 0.12rem;
      @include TextEllipsis(2);
    }

    .no-cover-title {
      min-height: initial;
      margin-bottom: 0.1rem;
    }

    .article-other {
      //margin-top: 0.12rem;
      color: #979797;

      span {
        display: inline-block;
      }
    }

    .article-tag {
      display: inline;
      border-radius: 0.03rem;
      background: rgba(#ccc, 0.34);
      padding: 0.01rem 0.04rem;
      color: #616568;
    }

    .article-author {
      display: inline;
      margin-left: 0.04rem;

      span {
        vertical-align: middle;
      }
    }

    .author-max-w {
      max-width: 0.6rem;
    }

    .article-pageviews {
      display: inline;
      margin-left: 0.06rem;
    }

    .article-time {
      margin-bottom: 0.04rem;
      span {
        color: #b2b1b7;
        font-size: 0.12rem;
        display: inline-block;
        transform: scale(0.92);
        transform-origin: left;
      }
    }

    .article-cover {
      width: var(--i-article-cover-w);
      height: var(--i-article-cover-h);
      object-fit: cover;
      border-radius: 0.04rem;
      border: 1px solid #ebebeb;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
</style>
