<template>
  <Teleport to="body">
    <div class="321ActivePopup">
      <van-popup v-model:show="show">
        <div class="popup-content">
          <img class="activity-theme" src="../images/active-popup-img.png" />
          <div class="join-btn" @click="toActiveHome" />
          <div class="close" @click="onClose" />
        </div>
      </van-popup>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { localProxyStorage } from '@/utils/storage'

  const router = useRouter()
  const show = ref(false)

  const updateShow = (state) => (show.value = state)

  const toActiveHome = () => {
    updateShow(false)
    router.push({ name: 'a20230321jlzm' })
  }

  const onClose = () => {
    updateShow(false)
  }

  const initialize = () => {
    // 每天只弹一次，活动期间做多三次
    let actEndTime = 1679241600000
    let nowTime = new Date().getTime()

    // 活动结束不弹窗
    if (nowTime > actEndTime) return

    const dayEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime().toString()
    const ACT_POPUP_321 = localProxyStorage.ACT_POPUP_321

    if (ACT_POPUP_321 && typeof ACT_POPUP_321 === 'string') {
      const data = localProxyStorage.ACT_POPUP_321.split('|')
      if (nowTime > data[data.length - 1] && data.length < 7) {
        updateShow(true)
        data.push(dayEndTime)
        localProxyStorage.ACT_POPUP_321 = data.join('|')
      }
    } else {
      localProxyStorage.ACT_POPUP_321 = dayEndTime
      updateShow(true)
    }
  }

  initialize()
</script>

<style lang="scss" scoped>
  .popup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .activity-theme {
    width: 3.54rem;
    height: 2.57rem;
    //object-fit: cover;
  }

  .join-btn {
    width: 2.72rem;
    height: 0.5rem;
    margin-top: 0.1rem;
    background: url('../images/active-popup-btn.png') no-repeat;
    background-size: 100% 100%;
  }

  .close {
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.3rem;
    background: url('../images/active-popup-close.png') no-repeat;
    background-size: 100% 100%;
  }

  :deep(.van-popup) {
    background: transparent;
  }
</style>
