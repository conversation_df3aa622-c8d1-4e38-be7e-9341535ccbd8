<template>
  <Panel class="product-news-panel" title="教练动态">
    <Skeleton class="skeleton" :loading="loading" animated>
      <template #skeleton>
        <SkeletonRow width="80%" margin-top="0" />
        <SkeletonImage height="2rem" margin-top="0.1rem" />
        <SkeletonRow width="40%" />
      </template>
      <template #content>
        <div v-for="item in list" :key="item.teachingId" class="media-box" @click="onNewsClick">
          <strong class="media-box__title">{{ item.title }}</strong>
          <div class="media-box__image">
            <div v-if="item.type === 2" class="icon-video">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="0.6rem"
                height="0.6rem"
                viewBox="0 0 70 70"
              >
                <path
                  transform="translate(15,15) scale(0.04,0.04)"
                  d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"
                ></path>
              </svg>
            </div>
            <Image :src="item.surfaceImage" height="100%" fit="cover" block />
          </div>
          <ul class="media-box__info">
            <li class="media-box__info__meta">{{ item.realName }}</li>
            <li class="media-box__info__meta">{{ item.hits }} 浏览</li>
            <li class="media-box__info__meta media-box__info__meta_extra">
              {{ item.type === 1 ? '文章' : '视频' }}
            </li>
          </ul>
        </div>

        <Empty v-if="list.length === 0" description="暂无动态" />
      </template>
    </Skeleton>
    <template #bottom>
      <div v-if="showMore" class="product-news__more" @click="onNewsClick"> 查看更多 </div>
    </template>
  </Panel>
</template>

<script setup>
  import { computed } from 'vue'
  import { Skeleton, SkeletonImage, SkeletonRow } from '@/components/skeleton'
  import Panel from './Panel'
  import Image from '@/components/image'
  import Empty from '@/components/empty'
  import { Toast } from 'vant'

  const props = defineProps({
    news: Array,
    loading: Boolean,
  })

  const list = computed(() => {
    return props.news.slice(0, 2)
  })

  const showMore = computed(() => {
    return props.news?.length > 2
  })

  function onNewsClick() {
    Toast('请前往爱教练小程序上浏览')
  }
</script>

<style scoped lang="scss">
  .product-news-panel {
    margin-top: 0.1rem;
  }

  .media-box {
    padding: 0.16rem;
    position: relative;

    &:active {
      background-color: #ececec;
    }

    &:before {
      content: ' ';
      position: absolute;
      left: 0.16rem;
      top: 0;
      right: 0.16rem;
      height: 1px;
      border-top: 1px solid rgba(0, 0, 0, 0.1);
      color: rgba(0, 0, 0, 0.1);
      transform-origin: 0 0;
      transform: scaleY(0.5);
    }
  }

  .media-box__title {
    display: block;
    font-weight: 400;
    font-size: 0.17rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.9);
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
  }

  .media-box__image {
    height: 2rem;
    padding-top: 0.04rem;
    border-radius: 0.04rem;
    overflow: hidden;
    position: relative;
  }

  .media-box__desc {
    color: rgba(0, 0, 0, 0.3);
    font-size: 0.14rem;
    line-height: 1.4;
    padding-top: 0.04rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-wrap: break-word;
    hyphens: auto;
  }

  .media-box__info {
    display: block;
    margin-top: 0.16rem;
    padding-bottom: 4px;
    font-size: 0.13rem;
    color: rgba(0, 0, 0, 0.3);
    line-height: 1em;
    list-style: none;
    overflow: hidden;
  }

  .media-box__info__meta {
    float: left;
    padding-right: 1em;
  }

  .media-box__info__meta_extra {
    padding-left: 1em;
    border-left: 1px solid rgba(0, 0, 0, 0.3);
  }

  .product-news__more {
    padding: 0.16rem;
    position: relative;
    text-align: center;
    justify-content: space-between;
    line-height: 1.5;
    font-size: 0.16rem;
    color: #576b95;
  }

  .icon-video {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 1;
    border-radius: 50%;
    display: inline-block;
    width: 0.6rem;
    height: 0.6rem;
    background: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    text-align: center;
    line-height: 0.6rem;
    vertical-align: middle;
    margin: -0.3rem auto auto -0.3rem;
    cursor: pointer;

    &:active {
      opacity: 0.85;
    }

    svg {
      fill: hsla(0, 0%, 100%, 0.7);
    }
  }

  :deep(.skeleton) {
    .ijl-skeleton {
      padding: 0.15rem;
    }
  }
</style>
