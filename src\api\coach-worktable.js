// # 教练工作台接口

import http from '@/utils/axios'

// 获取教练订单列表
export const getCoachOrderList = (params) => {
  return http.get('/trade-server/api/coach/order/list', { params })
}

// 获取该学员的购买记录
export const reqStudentBuyHistory = (params) => {
  return http.get('/trade-server/api/coach/study/buy-history', { params })
}

// 获取教练售后订单列表（退款订单）
export const getCoachAfterSaleOrder = (params) => {
  return http.get('/trade-server/api/coach/after-sale/list', { params })
}

// 获取教练学员列表
export const getCoachStudentList = (params) => {
  return http.get('/trade-server/api/coach/study/my-student-list', { params })
}

// 教练查看学员上课详情接口
export const getStudentClassSituation = (params) => {
  return http.get('/trade-server/api/coach/study/my-student', { params })
}

// 获取学员核销记录列表
export const getStudentVerificationList = (params) => {
  return http.get('/trade-server/api/coach/classes-consume/list', { params })
}

// 获取订单明细
export const reqOrderDetail = (params) => {
  return http.get('/trade-server/api/coach/order/detail', { params })
}

// 获取退款订单明细
export const reqRefundOrderDetail = (params) => {
  return http.get('/trade-server/api/coach/after-sale/detail', { params })
}

// 同意订单退款
export const reqAgreeRefund = (params) => {
  return http.post('/trade-server/api/coach/after-sale/agree', params)
}

// 拒绝订单退款
export const reqRejectRefund = (params) => {
  return http.post('/trade-server/api/coach/after-sale/refuse', params)
}

// 获取学员的购买课程库存
export const reqStudentStore = (params) => {
  return http.get('/trade-server/api/coach/classes-consume/teachingWayRemain', { params })
}

// 发起核销申请
export const reqClassConsumeApply = (params) => {
  return http.post('/trade-server/api/coach/classes-consume/apply', params)
}

// 获取教练钱包信息
export const reqCoachWallet = (params) => {
  return http.get('/trade-server/api/wallet/v1/getWallet', { params })
}

// 获取教练最近收入
export const reqRecentEarnings = (params) => {
  return http.get('/trade-server/api/wallet/v1/getWalletIncome', { params })
}

// 获取教练全部月分收益
export const reqAllMonthlyRevenue = (params) => {
  return http.get('/trade-server/api/wallet/v1/getWalletTransactionStatistic', { params })
}

// 获取教练某月的收益账单流水
export const reqMonthlyBill = (params) => {
  return http.get('/trade-server/api/wallet/v1/getWalletBillByMonth', { params })
}

// 教练发起金额提现
export const reqCoachWithdrawal = (params) => {
  return http.post('/trade-server/api/wallet/v1/withdrawal', params)
}

// 获取教练工作台消息列表
export const reqCoachMessageList = (params) => {
  return http.post('/user-server/api/userMessage/v1/getWorkTableList', params)
}

// 标记消息已读
export const reqMarkMessageRead = (params) => {
  return http.post('/user-server/api/userMessage/v1/userHasBeenRead', params)
}

// 标记所有消息已读
export const reqMarkALLMessageRead = () => {
  return http.post('/user-server/api/userMessage/v1/workTableOneKeyHasBeenRead')
}

// 获取教练未读消息总数
export const reqUnreadMessageTotal = () => {
  return http.post('/user-server/api/userMessage/v1/getWorkTableMessageUnreadCount')
}
