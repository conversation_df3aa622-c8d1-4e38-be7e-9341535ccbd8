<template>
  <div class="page">
    <div class="title">请使用微信扫码，提交订单</div>
    <div class="qrcode-wrap">
      <div class="tip">- 长按下载二维码或截图 -</div>
      <img class="qrcode" :src="qrcode" alt="二维码" />
    </div>
    <div class="go-back">
      <button class="back-btn feedback" @click="$router.go(-1)">返回</button>
    </div>
    <div class="logo">
      <img src="../../assets/images/logo-2.png" alt="logo" />
    </div>
  </div>
</template>

<script setup>
  import { useRoute } from 'vue-router'
  import { genQrCode } from '@/utils/qrcode'

  const route = useRoute()
  const link = decodeURIComponent(route.query.link || '')

  const qrcode = genQrCode({
    width: 175,
    height: 175,
    link: link,
  })
</script>

<style lang="scss" scoped>
  .title {
    font-size: 0.18rem;
    color: #1f1f1f;
    padding-top: 1.13rem;
    text-align: center;
  }

  .qrcode-wrap {
    margin-top: 0.24rem;
    font-size: 0.15rem;
    font-weight: 400;
    color: #1a1b1d;
    display: flex;
    flex-direction: column;
    align-items: center;

    .tip {
      font-size: 0.15rem;
      color: #1a1b1d;
    }

    .qrcode {
      width: 1.75rem;
      height: 1.75rem;
      margin-top: 0.15rem;
    }
  }

  .go-back {
    margin-top: 0.9rem;
    text-align: center;

    .back-btn {
      background: #ffffff;
      border-radius: 0.18rem;
      border: 1px solid #dddddd;
      padding: 0.08rem 0.2rem;
      color: #616568;
    }
  }

  .logo {
    margin-top: 0.8rem;
    text-align: center;

    img {
      width: 1.52rem;
      height: 0.47rem;
    }
  }
</style>
