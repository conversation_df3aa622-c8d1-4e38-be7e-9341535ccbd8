<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content page-bg-white posts-wrap">
        <div class="main" v-if="mainShow">
          <!--发布帖子 -->
          <div class="write-box">
            <div class="write-article feedback" @click="toPublishPage">
              <img
                class="icon"
                src="../../assets/images/coach-worktable/icon-publish-article.png"
                alt="icon"
              />
              发布培训信息
            </div>
          </div>
          <!-- 帖子列表 -->
          <div class="posts-list">
            <van-list
              v-model:loading="loading"
              :finished="finished"
              :immediate-check="false"
              @load="onPostLoad"
            >
              <div
                v-for="item in posts"
                :key="item"
                class="post feedback"
                :class="{ 'post-disable': !item.publish }"
                @click="toPostPage(item)"
              >
                <div class="post-time">
                  <span>{{ sliceStr(item.releaseTime, 0, 16) || '-' }}</span>
                </div>
                <div class="post-title omit">{{ item.title }}</div>
                <div class="post-desc">
                  {{ item.description }}
                </div>
                <div class="post-other">
                  <div class="tags">
                    <div class="tag">
                      <span class="f10">
                        {{ item.thirdlyCategoriesName }}
                      </span>
                    </div>
                    <div class="tag">
                      <span class="f10">
                        {{ getArea(item) }}
                      </span>
                    </div>
                  </div>
                  <div class="time">
                    <span class="f10"> 培训时间：{{ formatTiem(item.trainTime) }} </span>
                    <span class="f10">{{ getPagesview(item) }} 浏览</span>
                  </div>
                </div>
                <post-state v-if="!item.publish" />
              </div>
            </van-list>
          </div>
        </div>
        <div class="empty" v-if="emptyShow">
          <img class="empty-icon" src="../../assets/images/empty.png" alt="你还未发布过内容哦～" />
          <p class="empty-desc">你还未发布过内容哦～</p>
          <button class="i-button empty-button" @click="toPublishPage">发布培训</button>
        </div>
      </div>
    </template>
  </page>
</template>

<script>
  // KeepAlive 缓存组件必须要有 name 和路由 name 对应
  export default { name: 'coachWorktableRecruitPosts' }
</script>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter, onBeforeRouteLeave } from 'vue-router'
  import PostState from '@/components/post/post-state'
  import { getUserTrainsList } from '@/api/generic-server'
  import useKeepAliveStore from '@/store/keepAlive'
  import { sliceStr } from '@/utils'

  const router = useRouter()
  const emptyShow = ref(false)
  const mainShow = ref(false)
  const posts = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  const onPostLoad = (callback) => {
    pagination.pageNum += 1
    getUserTrainsList(pagination).then((res) => {
      let { data } = res
      posts.value = posts.value.concat(data.records)
      // 加载状态结束
      loading.value = false

      // 数据全部加载完成
      if (data.records.length === 0 || data.records.length < pagination.pageSize) {
        finished.value = true
      }
      callback && callback()
    })
  }

  onPostLoad(() => {
    mainShow.value = posts.value.length > 0
    emptyShow.value = !mainShow.value
  })

  const toPublishPage = () => {
    router.push({ name: 'coachWorktablePublishRecruit' })
  }

  // 浏览量
  const getPagesview = (item) => {
    let count = item.readCount
    if (count > 999) {
      return '999+'
    }
    return count
  }

  const formatTiem = (time) => {
    if (typeof time === 'string') {
      return time.slice(0, 10)
    } else {
      return '长期有效'
    }
  }

  // 获取地区名称
  const getArea = (item) => {
    let str = ''
    if (item.cityName) {
      str += item.cityName
    }

    if (item.countyName) {
      str += item.countyName
    }
    return str
  }

  const toPostPage = (item) => {
    router.push({
      name: 'trainDetails',
      params: {
        trainId: item.trainsId,
      },
    })
  }

  const keepAliveStore = useKeepAliveStore()
  onBeforeRouteLeave((to) => {
    let pages = ['trainDetails']
    if (!pages.includes(to.name)) {
      // 卸载缓存
      keepAliveStore.removeKeepAlive('coachWorktableRecruitPosts')
    }
  })
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  .posts-wrap {
    .write-box {
      width: 100%;
      height: 1.1rem;
      background: #fff;
      padding: 0.15rem 0.18rem;
      user-select: none;
    }

    .write-article {
      width: 3.4rem;
      height: 0.8rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f8f8;
      border-radius: 0.08rem;
      border: 1px dashed #ececec;

      .icon {
        width: 0.26rem;
        height: 0.26rem;
        margin-right: 0.06rem;
      }
    }

    .posts-list {
      background-color: #fff;
    }

    .post-disable {
      background: #f7f7f7;
    }

    .post {
      padding: 0.08rem 0.12rem;
      border-bottom: 1px solid #f2f2f2;

      .post-time {
        span {
          color: #b2b1b7;
          font-size: 0.12rem;
          display: inline-block;
          transform: scale(0.92);
          transform-origin: left;
        }
      }

      .post-title {
        font-size: 0.15rem;
        color: #1f1f1f;
        margin-top: 0.03rem;
      }

      .post-desc {
        font-size: 0.14rem;
        color: #616568;
        line-height: 0.2rem;
        margin-top: 0.02rem;
        @include TextEllipsis(2);
      }

      .post-other {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.08rem;
        color: #979797;

        .tags {
          .tag {
            border: 1px solid rgba(#979797, 0.8);
            // padding: 0.01rem 0.03rem;
            // padding: 0 0.02rem;
            border-radius: 0.03rem;
            margin-right: 0.06rem;
            display: inline;
          }
          span {
            display: inline-block;
            transform-origin: top;
          }
        }

        .time {
          color: #979797;
          span {
            display: inline-block;
            margin-left: 0.1rem;
          }
        }
      }

      .post-state {
        display: flex;
        align-items: center;
        margin-top: 0.08rem;

        .van-icon {
          margin-right: 0.03rem;
        }

        span {
          display: inline-block;
          transform-origin: left;
        }

        a {
          color: #ff9b26;
        }
      }
    }

    .empty {
      background-color: #fff;
      min-height: 100vh;
      text-align: center;

      .empty-icon {
        width: 0.98rem;
        height: 1rem;
        margin-top: 1.77rem;
      }

      .empty-desc {
        font-size: 0.12rem;
        color: #b2b1b7;
        margin-top: 0.1rem;
        user-select: none;
      }

      .empty-button {
        width: 1.8rem;
        height: 0.4rem;
        margin-top: 0.7rem;
        font-size: 0.16rem;
        color: #fff;
        font-weight: 600;
        background: #ff9b26;
        box-shadow: 0 2px 4px 1px rgba(245, 176, 76, 0.1);
        border-radius: 0.24rem;
      }
    }
  }
</style>
