<template>
  <div class="filter-menu-warp">
    <van-dropdown-menu class="dropdown-menu" active-color="#ff6445" duration="0">
      <!-- 综合 -->
      <van-dropdown-item
        v-model="formData.comprehensive"
        :title-class="sortTitleClass"
        :options="SORT"
        @change="triggerSearch"
      />
      <!-- 分类 -->
      <van-dropdown-item
        ref="classifyRef"
        :lazy-render="false"
        :title-class="classifyTitleClass"
        :title="classifyMenuTitle"
        @open="handleClassifyMenuOpen"
        @close="handleClassifyMenuClose"
      >
        <classify-cascader
          v-if="classifyCascaderShow"
          v-model="classifyValue"
          @change="onClassifyChange"
        />
      </van-dropdown-item>
      <!-- 区域 -->
      <van-dropdown-item
        v-model="formData.county"
        :title-class="areaTitleClass"
        :options="area"
        @change="triggerSearch"
      />
      <!-- 筛选 -->
      <van-dropdown-item class="dropdown-item" title="筛选" ref="otherFilterRef">
        <div class="dropdown-item-content">
          <!-- 授课方式 -->
          <div class="filter-item">
            <div class="filter-item-label">授课方式</div>
            <van-radio-group class="tags-radio" v-model="formData.feesType" direction="horizontal">
              <van-radio v-for="item in TEACHING_MODE" :key="item.name" :name="item.value">
                <template #icon="props">
                  <div class="tag-check-item">
                    <span :class="{ 'tag-check-checked': props.checked }">{{ item.name }}</span>
                  </div>
                </template>
              </van-radio>
            </van-radio-group>
          </div>
          <!-- 适用人群 -->
          <div class="filter-item">
            <div class="filter-item-label">适用人群</div>
            <van-radio-group
              class="tags-radio"
              v-model="formData.forTheCrowd"
              direction="horizontal"
            >
              <van-radio v-for="item in SCOPE" :key="item.name" :name="item.value">
                <template #icon="props">
                  <div class="tag-check-item">
                    <span :class="{ 'tag-check-checked': props.checked }">{{ item.name }}</span>
                  </div>
                </template>
              </van-radio>
            </van-radio-group>
          </div>
          <!-- 价格区间 -->
          <div class="filter-item">
            <div class="filter-item-label">价格区间</div>
            <div class="price-area">
              <input v-model="formData.leastMoney" placeholder="最低价" />
              <div class="price-area-hyphen"></div>
              <input v-model="formData.highestMoney" placeholder="最高价" />
              <div class="price-area-unit">元/课时</div>
            </div>
          </div>
          <!-- 从业年限 -->
          <div class="filter-item">
            <div class="filter-item-label">从业年限</div>
            <van-checkbox-group
              class="tags-checkbox"
              v-model="formData.teachYearList"
              direction="horizontal"
            >
              <van-checkbox v-for="item in WORKING_AGE" :key="item.name" :name="item.value">
                <template #icon="props">
                  <div class="tag-check-item">
                    <span :class="{ 'tag-check-checked': props.checked }">{{ item.name }}</span>
                  </div>
                </template>
              </van-checkbox>
            </van-checkbox-group>
          </div>
          <!-- 年龄 -->
          <div class="filter-item">
            <div class="filter-item-label">年龄</div>
            <van-checkbox-group
              class="tags-checkbox"
              v-model="formData.ageList"
              direction="horizontal"
            >
              <van-checkbox v-for="item in AGE_RANGE" :key="item.name" :name="item.value">
                <template #icon="props">
                  <div class="tag-check-item">
                    <span :class="{ 'tag-check-checked': props.checked }">{{ item.name }}</span>
                  </div>
                </template>
              </van-checkbox>
            </van-checkbox-group>
          </div>
          <!-- 类型 -->
          <div class="filter-item">
            <div class="filter-item-label">类型</div>
            <van-checkbox-group
              class="tags-checkbox"
              v-model="formData.coachIdentityList"
              direction="horizontal"
            >
              <van-checkbox v-for="item in IDENTITY_TYPE" :key="item.name" :name="item.value">
                <template #icon="props">
                  <div class="tag-check-item">
                    <span :class="{ 'tag-check-checked': props.checked }">{{ item.name }}</span>
                  </div>
                </template>
              </van-checkbox>
            </van-checkbox-group>
          </div>
          <!-- 性别 -->
          <div class="filter-item">
            <div class="filter-item-label">性别</div>
            <van-checkbox-group
              class="tags-checkbox"
              v-model="formData.sexList"
              direction="horizontal"
            >
              <van-checkbox v-for="item in SEX" :key="item.name" :name="item.value">
                <template #icon="props">
                  <div class="tag-check-item">
                    <span :class="{ 'tag-check-checked': props.checked }">{{ item.name }}</span>
                  </div>
                </template>
              </van-checkbox>
            </van-checkbox-group>
          </div>
        </div>
        <div class="bottom-buttons">
          <div class="i-button button" @click="resetFilterFields">重置</div>
          <div class="i-button button confirm-button" @click="handleOtherFilterConfirm">确认</div>
        </div>
      </van-dropdown-item>
    </van-dropdown-menu>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { reqAreaOptions } from '@/api/common'
  import { localProxyStorage } from '@/utils/storage'
  import ClassifyCascader from './ClassifyCascader'
  import {
    SORT,
    TEACHING_MODE,
    SCOPE,
    WORKING_AGE,
    AGE_RANGE,
    IDENTITY_TYPE,
    SEX,
  } from './filterMenuEnum'

  const emit = defineEmits(['change'])

  const allCity = ref([])
  const area = ref([{ text: '不限地区', value: '' }])
  const classifyRef = ref(null)
  const otherFilterRef = ref(null)
  const classifyCascaderShow = ref(false)
  const classifyMenuTitle = ref('分类')
  const selectedCity = localProxyStorage.selectedGeolocation || {}
  const classifyValue = ref([])
  const selectedClassifyItems = ref([])
  let beforeClassifyValue = []

  const sortTitleClass = computed(() => {
    return formData.value.comprehensive !== '1' ? 'dropdown-title-active' : ''
  })

  const classifyTitleClass = computed(() => {
    return classifyValue.value.length !== 0 ? 'dropdown-title-active' : ''
  })

  const areaTitleClass = computed(() => {
    return formData.value.county !== '' ? 'dropdown-title-active' : ''
  })

  const initFilterFields = () => {
    return {
      feesType: '', // 授课方式
      forTheCrowd: '', // 适用人群
      teachYearList: [], // 从业年限
      leastMoney: '', // 价格区间-最低价
      highestMoney: '', // 价格区间-最高价
      ageList: [], // 年龄范围
      coachIdentityList: [], // 身份类型
      sexList: [], // 性别
    }
  }

  const formData = ref({
    comprehensive: '1', // 综合筛选条件字段
    city: selectedCity.adcode, // 城市编码
    county: '', // 区域/县编码
    countyList: [], // 区域 list
    firstCategoriesId: '', // 一级分类
    secondCategoriesId: '', // 二级分类
    thirdlyCategoriesId: '', // 三级分类
    ...initFilterFields(),
  })

  const triggerSearch = () => {
    let cloneFormData = JSON.parse(JSON.stringify(formData.value))
    cloneFormData.firstCategoriesId = classifyValue.value[0] || ''
    cloneFormData.secondCategoriesId = classifyValue.value[1] || ''
    cloneFormData.thirdlyCategoriesId = classifyValue.value[2] || ''

    // 选择了全国
    if (cloneFormData.city === '000000') {
      cloneFormData.city = cloneFormData.county
      cloneFormData.county = null
    }

    if (cloneFormData.county) {
      cloneFormData.countyList.length = 0
      cloneFormData.countyList.push(cloneFormData.county)
    }

    emit('change', cloneFormData)
  }

  const handleOtherFilterConfirm = () => {
    otherFilterRef.value?.toggle()
    triggerSearch()
  }

  const resetFilterFields = () => {
    formData.value = Object.assign(formData.value, initFilterFields())
  }

  const getAreaOptions = () => {
    reqAreaOptions().then((res) => {
      const { data } = res
      data.forEach((item) => {
        allCity.value = allCity.value.concat(item.children)
      })

      if (selectedCity.adcode === '000000') {
        let allCityOptions = []
        allCity.value.forEach((city) => {
          allCityOptions.push({ text: city.adName, value: city.adCode })
        })

        area.value = area.value.concat(allCityOptions)
      } else {
        area.value = area.value.concat(getCityChildren(selectedCity.adcode))
      }
    })
  }
  getAreaOptions()

  const getCityChildren = (adcode) => {
    let arr = []
    let options = allCity.value.find((city) => city.adCode === adcode)?.children || []

    options.forEach((item) => {
      arr.push({ text: item.adName, value: item.adCode })
    })

    return arr
  }

  const handleClassifyMenuOpen = () => {
    beforeClassifyValue = classifyValue.value
  }

  const updateClassifyMenuTitle = () => {
    if (selectedClassifyItems.value.length === 0) {
      classifyMenuTitle.value = '分类'
    } else {
      let lastItem = selectedClassifyItems.value[selectedClassifyItems.value.length - 1]
      classifyMenuTitle.value = lastItem?.label || '分类'
    }
  }

  const handleClassifyMenuClose = () => {
    if (beforeClassifyValue.toString() === classifyValue.value.toString()) return
    updateClassifyMenuTitle()
    triggerSearch()
  }

  const onClassifyChange = (value) => {
    selectedClassifyItems.value = value.selected

    if (value.isInit) {
      updateClassifyMenuTitle()
    }

    if (value.selected.length === 3) {
      classifyRef.value?.toggle(false)
    }
  }

  // 设置分类值
  const setClassifyValue = (value) => {
    classifyValue.value = value
    classifyCascaderShow.value = true
  }

  const setSortValue = (value) => {
    formData.value.comprehensive = value || '1'
  }

  defineExpose({
    formData,
    setClassifyValue,
    setSortValue,
  })
</script>

<style lang="scss" scoped>
  .dropdown-menu {
    :deep(.van-dropdown-menu__bar) {
      height: 0.34rem;
      box-shadow: none;
      border-bottom: 1px solid #eeeeee;
    }

    :deep(.van-dropdown-menu__title) {
      font-size: 0.13rem;
    }

    :deep(.van-dropdown-item__content) {
      width: initial;
      left: var(--window-left);
      right: var(--window-right);
      //overflow: hidden;
      //max-height: none;
    }

    .dropdown-item {
      :deep(.van-dropdown-item__content) {
        overflow: hidden;
        max-height: none;
      }
    }
  }

  .radio-group {
    :deep(.van-cell-group--inset) {
      margin: 0;
      border-radius: 0;
    }

    :deep(.van-cell) {
      padding: 0.1rem 0.16rem;
      font-size: 0.14rem;
    }

    .radio-activity {
      background-color: #fff7f5;
      color: #ff6445;
    }
  }

  .filter-item {
    padding: 0 0.15rem;
    margin-top: 0.08rem;

    &:first-child {
      margin-top: 0.06rem;
    }

    .filter-item-label {
      padding: 0.08rem 0;
      font-size: 0.12rem;
      font-weight: 600;
      color: #1a1b1d;
    }
  }

  .tags-checkbox {
    margin-bottom: 0.08rem;

    :deep(.van-checkbox) {
      overflow: initial;
    }

    :deep(.van-checkbox__icon) {
      height: auto;
      line-height: initial;
    }

    :deep(.van-checkbox--horizontal) {
      margin-right: 0.06rem;
    }

    .tag-check-item {
      span {
        padding: 0.05rem 0.08rem;
        font-size: 0.14rem;
        color: #616568;
        background: #f3f3f3;
        border-width: 1px;
        border-style: solid;
        border-color: #f3f3f3;
        border-radius: 0.02rem;
        display: block;
      }

      .tag-check-checked {
        color: #ff6445;
        background-color: #fff3f1;
        border-color: #ff6445;
      }
    }
  }

  .tags-radio {
    margin-bottom: 0.08rem;

    :deep(.van-radio) {
      overflow: initial;
    }

    :deep(.van-radio--horizontal) {
      margin-right: 0.06rem;
    }

    :deep(.van-radio__icon) {
      height: auto;
      line-height: initial;
    }

    .tag-check-item {
      span {
        padding: 0.05rem 0.08rem;
        font-size: 0.14rem;
        color: #616568;
        background: #f3f3f3;
        border-width: 1px;
        border-style: solid;
        border-color: #f3f3f3;
        border-radius: 0.02rem;
        display: block;
      }

      .tag-check-checked {
        color: #ff6445;
        background-color: #fff3f1;
        border-color: #ff6445;
      }
    }
  }

  .dropdown-item-content {
    position: relative;
    max-height: 4.2rem;
    overflow: auto;
    padding-bottom: 0.4rem;
  }

  .bottom-buttons {
    width: 100%;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    display: flex;
    box-shadow: 0 -1px 0.03rem #f1f1f1;

    .button {
      height: 0.4rem;
      line-height: 0.4rem;
      text-align: center;
      flex: 1;
      font-size: 0.14rem;
      color: #1a1b1d;
      //border-top: 1px solid #eeeeee;
    }

    .confirm-button {
      color: #ffffff;
      background: #ff9b26;
      //border-top: 1px solid #ff9b26;
    }
  }

  .price-area {
    display: flex;
    align-items: center;

    input {
      width: 1rem;
      text-align: center;
      padding: 0.05rem 0.08rem;
      font-size: 0.14rem;
      color: #616568;
      background: #f3f3f3;
      outline: none;
      border: none;

      &::placeholder {
        color: #cccccc;
      }
    }

    .price-area-hyphen {
      position: relative;
      width: 0.2rem;
      height: 0.3rem;

      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -0.05rem;
        width: 0.1rem;
        height: 1px;
        background-color: #616568;
      }
    }

    .price-area-unit {
      margin-left: 0.1rem;
      color: #1a1b1d;
    }
  }
</style>

<style lang="scss">
  .dropdown-menu {
    .has {
      color: #ff6445;
    }
  }
</style>
