/* 微信支付
 * 使用微信浏览器内置对象唤醒支付
 * 注意：WeixinJSBridge内置对象在其他浏览器中无效。
 *
 * 使用例子：
 * weChatPay(data, (res) => {
 *   let { err_msg, err_desc } = res;
 *   // 支付成功
 *   if (err_msg === 'get_brand_wcpay_request:ok') {}
 *
 *   // 支付过程中用户取消
 *   if (err_msg === 'get_brand_wcpay_request:cancel') {}
 *
 *   // 支付过程中失败
 *   if (err_msg === 'get_brand_wcpay_request:fail') {}
 * })
 */

/* eslint-disable */
function onBridgeReady(params, callback) {
  WeixinJSBridge.invoke(
    "getBrandWCPayRequest",
    {
      appId: params.appId, //公众号ID，由商户传入
      timeStamp: params.timeStamp, //时间戳，自1970年以来的秒数
      nonceStr: params.nonceStr, //随机串
      package: params.package, // 订单详情扩展字符串
      signType: params.signType, //微信签名方式：
      paySign: params.paySign, //微信签名
    },
    function (res) {
      callback && callback(res);
    }
  );
}

// 唤醒微信支付
function weChatPay(params, callback) {
  if (typeof WeixinJSBridge === "undefined") {
    if (document.addEventListener) {
      document.addEventListener("WeixinJSBridgeReady", onBridgeReady(params, callback), false);
    } else if (document.attachEvent) {
      document.attachEvent("WeixinJSBridgeReady", onBridgeReady(params, callback));
      document.attachEvent("onWeixinJSBridgeReady", onBridgeReady(params, callback));
    }
  } else {
    onBridgeReady(params, callback);
  }
}

export default weChatPay;










