<template>
  <div>
    <Chat :content="typingBuffer" />
  </div>
  <input type="text" v-model="message" />
  <van-button type="primary" @click="fetchChatAPIOnce">发送</van-button>
</template>

<script setup>
  import Chat from '../components/Chat/index.vue'
  import { ref } from 'vue'
  import { localProxyStorage } from '@/utils/storage'
  import { aiApiURL } from '@/config'
  const typingBuffer = ref('')
  // const controller = new AbortController()
  // const signal = controller.signal

  const params = {
    conversationId: '5f9cf351-86d7-47f7-a6a4-d5890a175945',
    inputs: '',
    botId: 'default',
    attachments: [],
  }
  const message = ref('')

  // 修复signal传递
  const fetchChatAPIOnce = async () => {
    typingBuffer.value = ''
    const baseUrl = process.env.VUE_APP_RUN_ENV !== 'development' ? aiApiURL : ''
    const authToken = localProxyStorage.user.authToken || ''
    const controller = new AbortController()
    const signal = controller.signal
    params.inputs = message.value

    try {
      const resp = await fetch(`${baseUrl}/api/ai/stream/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
        body: JSON.stringify(params),
        signal,
      })

      if (!resp.ok) throw new Error(`HTTP error! status: ${resp.status}`)

      const reader = resp.body.getReader()
      const decoder = new TextDecoder()
      let shouldContinue = true
      const aiMessage = {
        // conversationId: conversationId.value,
      }
      const handleEvent = (event, data) => {
        const { content } = data || {}
        if (!aiMessage?.role) {
          Object.assign(aiMessage, data)
          Object.assign(aiMessage, { content: '' })
        }
        if (event === 'message' && content) {
          typingBuffer.value += content
          aiMessage.content += content
        }
      }
      while (shouldContinue) {
        const { done, value } = await reader.read()
        if (done) {
          console.log('流式响应结束')
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        // const lines = buffer?.split('\n')?.filter((line) => line.trim() !== '') || []
        const lines = chunk.split('\n')

        // buffer = lines.pop()
        console.log('lines', lines)
        let eventLine = ''

        // 处理完整的事件流数据
        lines.forEach((line) => {
          console.log('line', line)

          if (line.startsWith('event:')) {
            eventLine = line.slice(6).trim()
            console.log('eventLine', eventLine)
          } else if (eventLine && line.startsWith('data:')) {
            const data = line.slice(5)
            if (data && data.trim() !== '') {
              const parsed = JSON.parse(data)
              console.log('parsed', parsed)

              handleEvent(eventLine, parsed)
            }
            eventLine = '' // Reset event line after processing
          }
          // if (line.startsWith('data:')) {
          //   const content = line.slice(5)
          //   typingBuffer.value += content
          //   // 触发UI更新
          //   updateAIResponse(content)
          // }
        })
      }
    } catch (error) {
      console.error('流式响应失败:', error)
      // Toast.fail('获取流式响应失败')
    }
  }
</script>
