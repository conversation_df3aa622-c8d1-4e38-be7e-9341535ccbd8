<template>
  <div class="sign-up-success min-height-100">
    <div class="tip">
      <img class="icon" src="../../assets/images/icon/icon-success.png" alt="" />
      <div class="text">您已注册成功！</div>
      <button class="back-home-btn" @click="$router.push('/')">
        返回首页
        <!-- <span>（{{ seconds }}s）</span> -->
      </button>
    </div>
    <div class="scan-code">
      <p class="text">扫码关注 <strong>「爱教练」</strong> 微信公众号</p>
      <p class="text">随时了解爱教练</p>
      <div class="qrcode">
        <img src="../../assets/images/wx.jpg" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted } from 'vue'

  // const router = useRouter();
  // const seconds = ref(10);
  onMounted(() => {
    // const timer = setInterval(() => {
    //   seconds.value--;
    //   if (seconds.value === 0) {
    //     router.push("/");
    //     clearInterval(timer);
    //   }
    // }, 1000);
  })
</script>

<style lang="scss" scoped>
  .tip {
    text-align: center;
    height: 2.66rem;
    padding: 0.5rem 0;
    background-color: #fff;

    .icon {
      display: inline-block;
      width: 0.48rem;
      height: 0.48rem;
    }

    .text {
      margin-top: 0.15rem;
      font-size: 0.16rem;
      font-weight: 500;
      color: #1a1b1d;
    }

    .back-home-btn {
      width: 2rem;
      height: 40px;
      margin-top: 0.36rem;
      background: #ff9b26;
      color: #fff;
      font-size: 0.17rem;
      font-weight: 600;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.2rem;
    }
  }

  .scan-code {
    min-height: calc(100vh - 2.67rem);
    margin-top: 0.05rem;
    padding-top: 0.4rem;
    background-color: #fff;
    text-align: center;

    .text {
      line-height: 0.2rem;
      font-size: 0.14rem;
      color: #1f1f1f;
    }

    .qrcode {
      width: 1.33rem;
      height: 1.33rem;
      display: inline-block;
      margin-top: 0.23rem;
      background: #ffffff;
      border: 1px solid #ececec;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
