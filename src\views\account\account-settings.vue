<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="page account-settings">
        <ul class="setting-list">
          <!--          <li class="item feedback" @click="userNamePopup = true">-->
          <!--            <div class="title">用户名</div>-->
          <!--            <div class="content omit">{{ userInfo.userName }}</div>-->
          <!--            <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />-->
          <!--          </li>-->
          <li class="item feedback" @click="toBindingPhonePage">
            <div class="title">绑定手机</div>
            <div class="content">{{ userInfo.mobile || '-' }}</div>
            <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
          </li>
          <li class="item feedback">
            <div class="title">绑定微信</div>
            <div class="content omit">{{ userInfo.nickname || '-' }}</div>
          </li>
          <li class="item feedback" @click="$router.push('/account/modify-password')">
            <div class="title">登录密码</div>
            <div class="content">修改密码</div>
            <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
          </li>
        </ul>

        <div class="fixed-page-footer">
          <button class="i-button logout-button" @click="onLogout">退出账号</button>
        </div>

        <!-- 修改用户名 -->
        <van-popup
          class="popup"
          v-model:show="userNamePopup"
          :close-on-click-overlay="false"
          :duration="0"
        >
          <div class="popup-close" @click="closePopup">
            <van-icon name="cross" color="#BAB5B5" size="0.16rem" />
          </div>
          <div class="popup-title">修改用户名</div>
          <div class="popup-content">
            <div class="input-wrap">
              <input
                v-model.trim="form.userName"
                @blur="onCheckUserName"
                maxlength="8"
                class="input"
                placeholder="输入用户名"
              />
              <span class="input-count">{{ form.userName.length }}/8</span>
            </div>
            <div v-show="checkUserNameExisted" class="input-error-msg">
              <i class="icon-error-tip"></i>
              <span>用户名已存在</span>
            </div>
          </div>
          <div class="popup-footer">
            <button class="i-button cancel-button" @click="closePopup">取消</button>
            <button class="i-button confirm-button" @click="onConfirmEditUserName">确认</button>
          </div>
        </van-popup>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { localProxyStorage } from '@/utils/storage'
  import { Toast, Dialog } from 'vant'
  import { signOut } from '@/common'
  import { isWeChat } from '@/utils'
  import {
    getUserInfo,
    updateUserName,
    verifyUserInfoByMobile,
    offAutoWeChatAuth,
  } from '@/api/user-server'

  const router = useRouter()
  const userNamePopup = ref(false)
  const checkUserNameExisted = ref(false)
  const userInfo = ref({})
  const form = reactive({
    userName: '',
  })

  getUserInfo().then((res) => {
    const { data } = res
    userInfo.value = data
    form.userName = data.userName
  })

  // 要修改的用户名是否和现在的一样
  const isUserNameSame = () => {
    return form.userName === userInfo.value.userName
  }

  const onCheckUserName = () => {
    if (isUserNameSame()) return
    verifyUserInfoByMobile({ userName: form.userName }).then((res) => {
      checkUserNameExisted.value = res.data
    })
  }

  const onConfirmEditUserName = () => {
    if (isUserNameSame()) {
      userNamePopup.value = false
      return
    }

    if (checkUserNameExisted.value) return

    if (form.userName) {
      updateUserName(form).then(() => {
        userInfo.value.userName = form.userName

        // 更新本地存储信息
        const localUserInfo = localProxyStorage.user
        localUserInfo.userName = form.userName
        localProxyStorage.user = localUserInfo

        userNamePopup.value = false
        Toast.success('修改成功')
      })
    } else {
      Toast('请输入用户名')
    }
  }

  const closePopup = () => {
    userNamePopup.value = false
    form.userName = userInfo.value.userName
    checkUserNameExisted.value = false
  }

  const onLogout = () => {
    Dialog.confirm({
      message: '您确认要退出当前账号吗？',
    })
      .then(() => {
        // 微信环境内，用户手动退出，关闭自动静默登录
        if (isWeChat()) {
          offAutoWeChatAuth().then(() => {
            signOut()
            Toast('退出成功')
          })
        } else {
          signOut()
        }

        Toast('退出成功')
      })
      .catch(() => {})
  }

  const toBindingPhonePage = () => {
    router.push({
      name: 'bindingPhone',
      query: {
        type: userInfo.value.mobile ? 'modify' : 'binding',
      },
    })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('error-tip', 0.13rem, 0.13rem);

  .account-settings {
    padding-bottom: 0.65rem;

    .setting-list {
      padding: 0 0.1rem;

      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.18rem 0.02rem 0.18rem 0.1rem;
        border-bottom: 1px solid #eeeeee;
        max-height: 0.55rem;

        .title {
          flex: 1;
          color: #453938;
        }

        .content {
          flex: 1;
          text-align: right;
          color: #b2b1b7;
        }

        .icon {
          margin-left: 0.02rem;
        }
      }
    }

    .fixed-page-footer {
      background-color: #fff;

      .logout-button {
        width: 3.45rem;
        height: 0.4rem;
        border-radius: 0.22rem;
        border: 1px solid #ff6445;
        margin: 0.1rem 0.15rem;
        color: #ff6445;
      }
    }

    :deep(.popup) {
      width: 2.81rem;
      border-radius: 0.05rem;
      background-color: #fff;
      overflow: hidden;

      .popup-close {
        width: 0.32rem;
        height: 0.32rem;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 0;
        right: 0;
      }

      .popup-title {
        font-size: 0.18rem;
        font-weight: 600;
        color: #1f1f1f;
        padding-top: 0.36rem;
        text-align: center;
      }

      .input-error-msg {
        margin-top: 0.04rem;
        font-size: 0.12rem;
        color: #ff6445;
        display: flex;
        align-items: center;

        span {
          margin-left: 0.04rem;
        }
      }

      .popup-content {
        height: 1.18rem;
        padding: 0.32rem 0.16rem 0 0.16rem;

        .input-wrap {
          position: relative;
        }

        .input {
          width: 100%;
          padding: 0.04rem 0.47rem 0.04rem 0.1rem;
          outline: none;
          border: none;
          font-size: 0.16rem;
          font-weight: 600;
          color: #1f1f1f;
          border-bottom: 1px solid #eeeeee;
        }

        .input-count {
          position: absolute;
          right: 0;
          display: inline-block;
          padding: 0.04rem 0.1rem;
          color: #909399;
          font-size: 0.14rem;
        }
      }

      .popup-footer {
        border-top: 1px solid #eeeeee;

        .cancel-button,
        .confirm-button {
          width: 50%;
          font-size: 0.14rem;
          height: 0.4rem;
          line-height: 0.4rem;
        }

        .confirm-button {
          color: #ffffff;
          background: var(--i-primary);
        }
      }
    }
  }
</style>
