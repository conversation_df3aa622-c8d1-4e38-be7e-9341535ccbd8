<template>
  <page :title="pageTitle">
    <template #page>
      <div class="page-content page-bg-white">
        <div v-if="pageShow" class="box">
          <div v-if="isUserPost && !article.publish" class="fixed-top">
            <post-state />
          </div>
          <div class="wrapper">
            <div class="title">{{ article.title }}</div>
            <div class="author-info">
              <div class="info-l">
                <span class="author" @click="toAuthorHome(article.mappingId, article.identityType)">
                  {{ article.realName }}
                </span>
                <span> {{ sliceStr(article.releaseTime, 0, 10) }}</span>
              </div>
              <div class="info-r">
                <!-- <i class="icon-eye2"></i> -->
                <span v-if="article.hits < 999">{{ article.hits }}浏览</span>
                <span v-else class="num">999+浏览</span>
              </div>
            </div>
            <image-preview-wrapper>
              <div class="content" v-html="article.description" />
            </image-preview-wrapper>
            <iframe
              v-if="isThirdPartyVideoLink"
              class="iframe"
              width="100%"
              height="200"
              :src="parserVideoUrl(article.url)"
              allowfullscreen
            />
            <xg-player
              v-if="article.videosUrl"
              class="player"
              :url="ossURLJoin(article.videosUrl)"
              :poster="article.poster"
              height="2rem"
            />
            <post-manage v-if="isUserPost" @select="onSelect" />
          </div>
        </div>
        <details-empty v-if="emptyShow" />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import { delUserVideos, getVideoDetails } from '@/api/generic-server'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { getIsUserPost, parserVideoUrl } from '@/common'
  import PostManage from '@/components/post/post-manage'
  import PostState from '@/components/post/post-state'
  import XgPlayer from '@/components/xg-player'
  import { ossURLJoin, toAuthorHome } from '@/common'
  import { Dialog, Toast } from 'vant'
  import useKeepAliveStore from '@/store/keepAlive'
  import DetailsEmpty from '@/views/common/components/details-empty'
  import { sliceStr } from '@/utils'

  const route = useRoute()
  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const article = ref(null)
  const articleId = route.params.id
  const ossVideoSnapshot = '?x-oss-process=video/snapshot,t_1,f_jpg,m_fast,ar_auto'
  const pageTitle = ref('')

  // 是否第三方平台链接
  const isThirdPartyVideoLink = ref(false)

  const initialize = async () => {
    let params = {
      id: articleId,
    }
    let { data } = await getVideoDetails(params)
    pageTitle.value = data.title
    document.title = data.title + ',爱教练私教网'
    document
      .querySelector('meta[name="keywords"]')
      .setAttribute('content', `${data.title},爱教练私教网`)
    document
      .querySelector('meta[name="description"]')
      .setAttribute('content', `${data.description},爱教练私教网`)
    // 优先展示 videosUrl
    if (!data.videosUrl) {
      if (data.url) {
        isThirdPartyVideoLink.value = true
      }
    }

    // 取第一张视频封面图
    if (Array.isArray(data.imageList)) {
      if (data.imageList.length > 0) {
        data.poster = ossURLJoin(data.imageList[0])
      }
    } else {
      // 这里使用 啊里云服务 OSS 视频截帧服务，取第一帧当封面
      data.poster = ossURLJoin(data.videosUrl) + ossVideoSnapshot
    }

    article.value = data

    setWxShare({
      title: '【爱教练】' + data.title,
      desc: data.description,
      link: baseURL + '/video/details/' + articleId,
      imgUrl: data.poster ? data.poster : ossURL + '/h5-assets/logo.png',
    })
  }

  initialize()

  const onEdit = () => {
    router.replace({
      name: 'coachWorktablePublishVideo',
      query: {
        videoId: articleId,
      },
    })
  }

  const onRemove = () => {
    Dialog.confirm({
      message: '你确定要删除这条培训信息吗？',
    })
      .then(() => {
        delUserVideos(articleId).then(() => {
          keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
          Toast.success('删除成功')
          router.replace({
            name: 'coachWorktableLearningPosts',
            query: {
              tabIndex: '2',
            },
          })
          router.go(-1)
        })
      })
      .catch(() => {
        // on cancel
      })
  }

  const onSelect = (item) => {
    if (item.code === 'edit') {
      onEdit()
    }

    if (item.code === 'delete') {
      onRemove()
    }
  }

  const isUserPost = computed(() => {
    if (!article.value) return false
    return getIsUserPost(article.value.userId) && article.value.identityType === 'coach'
  })

  const pageShow = computed(() => {
    if (!article.value) return false
    return !!(article.value.publish || isUserPost.value)
  })

  const emptyShow = computed(() => {
    if (!article.value) return false
    return !article.value.publish && !isUserPost.value
  })
</script>
<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: text-top;
    margin-right: 0.06rem;
  }

  .wrapper {
    padding: 0.15rem;
    background-color: #fff;
  }

  .title {
    font-size: 0.17rem;
    font-weight: 600;
    margin-bottom: 0.1rem;
    @include TextEllipsis(2);
  }

  .author-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.12rem;
    color: #b2b1b7;
    line-height: 0.17rem;
    padding-bottom: 0.1rem;
    margin-bottom: 0.1rem;
    border-bottom: 0.01rem solid #eeeeee;
    .author {
      margin-right: 0.1rem;
      color: var(--i-primary);
    }
  }

  :deep(.content) {
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }

  .iframe {
    margin-top: 0.1rem;
    border: 1px solid #eeeeee;
    background-color: #eeeeee;
  }

  .player {
    margin-top: 0.1rem;
    border: 1px solid #eeeeee;
    border-radius: 0.04rem;
    overflow: hidden;
  }

  .fixed-top {
    .post-state {
      margin: 0;
      line-height: 0.3rem;
      padding-left: 0.15rem;
      background: #f7f7f7;
    }
  }
</style>
