import { localProxyStorage } from '@/utils/storage'
import { isQyWeChat } from '@/utils/index'
import { getAuthToken } from '@/api/ai-server'
import { baseURL } from '@/config/index'
// 安全state生成（16位随机字符串）
const generateSecureState = () => {
  return crypto.getRandomValues(new Uint32Array(4)).join('')
}

const CORP_ID = 'ww8f126d814ac0287e' // 企业ID
const agentid = process.env.VUE_APP_AGENT_ID // 应用ID
const REDIRECT_URI = encodeURIComponent(baseURL + '/ai/sale-assistant')

// 生成带state的授权链接
export const generateSilentAuthUrl = () => {
  const state = generateSecureState()
  // localStorage.setItem('wecom_auth_state', state) // https://open.weixin.qq.com/connect/oauth2/authorize?appid=CORPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid=AGENTID#wechat_redirect

  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${CORP_ID}&redirect_uri=${REDIRECT_URI}&response_type=code&scope=snsapi_base&state=${state}&agentid=${agentid}#wechat_redirect`
}

export const beforeEnter = async (to, from, next) => {
  const query = to.query
  // localProxyStorage.user = {
  //   authToken:
  //     'eyJhbGciOiJIUzI1NiJ9.eyJleHRlcm5hbFVzZXJJZCI6IjEiLCJlbXBVc2VySWQiOjEsImVtcFVzZXJOYW1lIjoiYWRtaW4iLCJzdWIiOiIxIiwiaWF0IjoxNzQ5NjI4Mjg5LCJleHAiOjE3NTA0OTIyODl9.WVyq5cD7O-kJELieQgsPQkUgKc0n0Q3oLqb6KnZU-uw',
  // }
  if (isQyWeChat()) {
    if (localProxyStorage?.user?.authToken) {
      next()
    } else {
      if (query.code) {
        try {
          console.log('query:', query)
          const res = await getAuthToken({ code: query.code })
          console.log('getAuthToken:', res)
          localProxyStorage.user = {
            authToken: res.data.accessToken,
          }
          next()
        } catch (e) {
          next('/error')
        }
      } else {
        window.location.href = generateSilentAuthUrl()
      }
    }
  } else {
    next()
  }
}

// 初始化登录组件-扫码登录
export const wwLogin = () => {
  const ww = window.ww || {}
  console.log('wwLogin', ww)
  if (ww.createWWLoginPanel) {
    return ww.createWWLoginPanel({
      el: '#app',
      params: {
        login_type: 'CorpApp',
        appid: CORP_ID,
        agentid: agentid,
        redirect_uri: REDIRECT_URI,
        state: generateSecureState(),
        redirect_type: 'callback',
      },
      onCheckWeComLogin({ isWeComLogin }) {
        console.log(isWeComLogin)
      },
      onLoginSuccess({ code }) {
        console.log('code', code)
      },
      onLoginFail(err) {
        console.log(err)
      },
    })
  }
}
