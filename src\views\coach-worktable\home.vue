<template>
  <div class="page worktable-wrap">
    <!-- 用户信息 -->
    <div class="user-info-block">
      <van-image
        width="0.54rem"
        height="0.54rem"
        class="avatar"
        round
        fit="cover"
        :src="userAvatar"
      />
      <div class="info-box">
        <h1 class="real-name">
          {{ coachInfo.realName }}<span class="tag">{{ coachJobTag }}</span>
          <badge
            class="message"
            :show-zero="false"
            :content="unreadTotal"
            max="99"
            color="#FF6445"
            :offset="[1, 0]"
            @click="$router.push({ name: 'myWorktableMessage' })"
          >
            <div class="icon-bell" />
          </badge>
        </h1>
        <div class="detail">
          <div class="username">
            <span class="f10 omit">用户编号：{{ userInfo.userCode }}</span>
          </div>
          <van-row justify="space-between" align="center">
            <van-col span="12">
              <div class="user-mobile">
                <span class="f10">手机号：{{ userInfo.mobile || '-' }}</span>
              </div>
            </van-col>
            <van-col span="12">
              <div class="account" @click="$router.push({ name: 'accountSettings' })">
                <button class="more-btn">我的账号<i class="icon-arrow-right"></i></button>
              </div>
            </van-col>
          </van-row>
        </div>
      </div>
    </div>
    <div class="worktable-banner">
      <van-image
        width="3.45rem"
        height="0.9rem"
        fit="cover"
        :src="bannerUrl"
        @click="toEntryActivity"
      />
    </div>
    <!-- 操作板块 -->
    <div class="operation-panel">
      <div
        v-for="item in panels"
        :key="item.name"
        @click="onPanelClick(item)"
        class="panel feedback"
      >
        <img class="panel-icon" :src="item.icon" alt="icon" />
        <div class="panel-name">{{ item.name }}</div>
      </div>
    </div>

    <div class="page-footer">
      <div>
        <button class="i-button share-button" @click="posterShow = true">分享名片</button>
      </div>
      <div class="links">
        <a class="link" @click="toMyHome">我的主页</a>
        <a class="link" @click="$router.push('/')">用户端官网</a>
      </div>
    </div>

    <!-- 名片组件 -->
    <poster-popup v-model:show="posterShow" :coach-id="coachInfo.id" />
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { ossURLJoin } from '@/common'
  import { useRouter } from 'vue-router'
  import setWxShare from '@/utils/weChat/share'
  import { getUserInfo } from '@/api/user-server'
  import PosterPopup from '../coach/poster-popup'
  import { getCoachBaseInfo } from '@/api/coach-server'
  import { reqUnreadMessageTotal } from '@/api/coach-worktable'
  import { baseURL, ossURL } from '@/config'
  import { Badge } from 'vant'
  import wx from 'weixin-js-sdk'

  const router = useRouter()
  const isMiniprogramEnv = ref(false)
  const userInfo = ref({})
  const posterShow = ref(false)
  const unreadTotal = ref(0)
  const coachInfo = ref({
    coachCategoryList: [], // 教练职业分类
    coachImageList: [], // 教练图片
    id: '', // 教练id
    realName: '', // 教练真实姓名
    userId: '',
  })

  wx.miniProgram.getEnv((res) => {
    isMiniprogramEnv.value = res.miniprogram
  })

  const bannerUrl = ossURL + '/h5-assets/coach-worktable-banner.webp'

  setWxShare({
    title: '爱教练——全国体育私教预约平台',
    desc: '体教招生、科学排课、线上约课、订场馆',
    link: baseURL + '/my-worktable/coach',
    imgUrl: ossURL + '/h5-assets/logo.png',
  })

  getUserInfo().then((res) => {
    const { data } = res
    userInfo.value = data
  })

  getCoachBaseInfo().then((res) => {
    coachInfo.value = res.data
  })

  // 获取未读消息数
  reqUnreadMessageTotal().then((res) => {
    unreadTotal.value = res.data
  })

  // 教练职业标签
  const coachJobTag = computed(() => {
    if (Array.isArray(coachInfo.value.coachCategoryList)) {
      if (coachInfo.value.coachCategoryList.length > 0) {
        return coachInfo.value.coachCategoryList[0].thirdlyCategoriesName + '教练'
      }
    }
    return '教练'
  })

  // 从教练信息获取教练主图当头像
  const userAvatar = computed(() => {
    if (Array.isArray(coachInfo.value.coachImageList)) {
      if (coachInfo.value.coachImageList.length >= 1) {
        return (
          ossURLJoin(coachInfo.value.coachImageList[0]) +
          '?x-oss-process=image/interlace,1/format,jpg/quality,q_30'
        )
      }
    }
    return ''
  })

  const panels = ref([
    {
      name: '我的钱包',
      icon: require('../../assets/images/coach-worktable/icon-wallet.png'),
      pathName: 'myWorktableWallet',
      miniprogramPath: '/pages/coachWorkbench/wallet/wallet',
    },
    {
      name: '我的学员',
      icon: require('../../assets/images/coach-worktable/icon-students.png'),
      pathName: 'myWorktableStudents',
    },
    {
      name: '全部订单',
      icon: require('../../assets/images/coach-worktable/icon-order.png'),
      pathName: 'myWorktableOrder',
    },
    {
      name: '教练资料',
      icon: require('../../assets/images/coach-worktable/icon-settings.png'),
      pathName: 'myWorktableToWeapp',
      miniprogramPath: '/pages/joiningPolicy/joiningPolicy',
    },
    {
      name: '培训信息',
      icon: require('../../assets/images/coach-worktable/icon-recruit.png'),
      pathName: 'coachWorktableRecruitPosts',
    },
    {
      name: '教学资源',
      icon: require('../../assets/images/coach-worktable/icon-learning.png'),
      pathName: 'coachWorktableLearningPosts',
    },
    {
      name: '推荐关系',
      icon: require('../../assets/images/coach-worktable/icon-invite.png'),
      pathName: 'myWorktableInviteList',
    },
    {
      name: '配置上课时间',
      icon: require('../../assets/images/coach-worktable/icon-schedule.png'),
      pathName: '',
      miniprogramPath: '/pages/coachWorkbench/courseScheduling/courseScheduling',
    },
  ])

  const onPanelClick = (item) => {
    if (isMiniprogramEnv.value && item.miniprogramPath) {
      wx.miniProgram.navigateTo({
        url: item.miniprogramPath,
      })
    } else if (item.pathName) {
      router.push({
        name: item.pathName,
      })
    } else {
      router.push({
        path: '/my-worktable/to-weapp',
        query: {
          path: item.miniprogramPath,
        },
      })
    }
  }

  const toMyHome = () => {
    const id = coachInfo.value.id
    if (isMiniprogramEnv.value) {
      wx.miniProgram.navigateTo({
        url: '/pages/coach/details/details?id=' + id,
      })
    } else {
      router.push({ name: 'coachDetails', params: { id } })
    }
  }

  const toEntryActivity = () => {
    if (isMiniprogramEnv.value) {
      wx.miniProgram.navigateTo({
        url: '/pages/activity/buyTenGiveOne/coachEntry/index',
      })
    }
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('arrow-right', 0.06rem, 0.09rem) {
    margin-left: 0.07rem;
  }

  .worktable-wrap {
    .user-info-block {
      display: flex;
      padding-top: 0.2rem;

      .avatar {
        width: 0.54rem;
        height: 0.54rem;
        border-radius: 50%;
        border: 1px solid #f8f8f8;
        margin-left: 0.24rem;
      }

      .info-box {
        display: flex;
        flex: 1;
        flex-direction: column;
        margin-left: 0.15rem;
        padding-right: 0.15rem;

        .real-name {
          font-size: 0.2rem;
          font-weight: 600;
          color: #1f1f1f;
          line-height: 0.22rem;
          margin: 0;
          padding: 0;
        }

        .message {
          float: right;

          .icon-bell {
            width: 0.18rem;
            height: 0.2rem;
            display: inline-block;
            background: url('../../assets/images/coach-worktable/icon-bell.png') no-repeat;
            background-size: 100% 100%;
          }
        }

        .tag {
          font-size: 0.12rem;
          margin-left: 0.06rem;
          padding: 0.02rem 0.1rem;
          color: #ff6445;
          border-radius: 0.1rem;
          font-weight: 400;
          border: 1px solid #ff6445;
          vertical-align: bottom;
        }

        .detail {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          line-height: 0.14rem;

          .username,
          .user-mobile {
            font-size: 0.12rem;
            color: #616568;
            margin: 0.02rem 0.15rem 0 0;
            width: 1.5rem;

            span {
              display: inline-block;
              transform-origin: left;
            }
          }

          .username {
            width: 2.2rem;

            span {
              width: 100%;
              vertical-align: middle;
            }
          }

          .account {
            text-align: right;
          }

          .more-btn {
            font-size: 0.12rem;
            transform: scale(0.84);
            background: transparent;
          }
        }
      }
    }

    .worktable-banner {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 0.3rem;
    }

    .operation-panel {
      display: flex;
      flex-flow: wrap;
      margin: 0.3rem 0.15rem 0 0.15rem;
      gap: 0.12rem;

      .panel {
        width: 1.07rem;
        height: 1.04rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        box-shadow: 0 0 0.16rem 0 rgba(0, 0, 0, 0.1);
        border-radius: 0.08rem;
      }

      .panel-icon {
        width: 0.56rem;
        height: 0.56rem;
      }

      .panel-name {
        margin-top: 0.02rem;
        font-size: 0.14rem;
        color: #616568;
      }
    }

    .page-footer {
      width: 3.75rem;
      position: fixed;
      bottom: 0.15rem;
      text-align: center;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);

      .share-button {
        width: 1.68rem;
        height: 0.45rem;
        font-size: 0.16rem;
        color: #616568;
        border-radius: 0.22rem;
        border: 1px solid #b2b1b7;
      }

      .links {
        margin-top: 0.3rem;

        .link {
          font-size: 0.12rem;
          color: #616568;
          display: inline-block;
          padding: 0 0.1rem;

          &:not(:last-child) {
            border-right: 1px solid #ebebeb;
          }
        }
      }
    }
  }
</style>
