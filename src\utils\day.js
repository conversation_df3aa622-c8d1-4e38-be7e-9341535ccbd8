import dayjs from 'dayjs'

export const dateFormat = (date, format = 'YYYY/MM/DD HH:mm:ss') => {
  return dayjs(date).format(format)
}

// 获取某个日期的毫秒
export const getDateTime = (date) => {
  return dayjs(date).valueOf()
}

export const transformWeek = (number) => {
  let week = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日',
  }

  return week[number]
}

export const getDateSecond = (date) => {
  return dayjs(date).unix()
}
