<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="recruit-list">
        <van-dropdown-menu ref="trainMenu" :duration="0">
          <van-dropdown-item :title="formData.thirdValNav" :ref="(el) => (itemNavRefs[0] = el)">
            <div class="category-box">
              <ThreeTreeSelects
                class="tree-selects"
                :dataList="formData.threeSelectList"
                :clear="formData.isClearThreeTree"
                @selectChange="getSelectRes"
              />
            </div>
            <div class="footer">
              <div class="reset" @click="threeSelectclear">重置</div>
              <div class="submit" @click="searchSub(0)">确定</div>
            </div>
          </van-dropdown-item>
          <van-dropdown-item :title="formData.areaTabNav" :ref="(el) => (itemNavRefs[1] = el)">
            <div class="category-box">
              <div class="area category-box">
                <div class="area-box">
                  <van-cell
                    center
                    :title="item.adName"
                    :class="{ areaSelect: formData.areaJlIndex === index }"
                    v-for="(item, index) in formData.areaList"
                    :key="item.adCode"
                    @click="areaClick(item, index)"
                  >
                    <template #right-icon>
                      <i v-if="formData.areaJlIndex === index" class="icon icon-selected" />
                    </template>
                  </van-cell>
                </div>
                <div class="footer">
                  <div class="reset" @click="secendSelectclear">重置</div>
                  <div class="submit" @click="searchSub(1)">确定</div>
                </div>
              </div>
              <!-- <SecendTreeSelects
            class="tree-selects"
            :dataList="formData.areaList"
            :clear="formData.isClearSecendTree"
            @selectChange="getAreaRes"
          /> -->
              <!-- <div class="footer">
          <div class="reset" @click="secendSelectclear">重置</div>
          <div class="submit" @click="searchSub(1)">确定</div>
        </div> -->
            </div>
          </van-dropdown-item>
          <div v-if="formData.trainerList.length < 1" class="null-data">
            <img src="../../assets/images/empty2.png" alt="" />
            <p>{{ formData.nullDataTxt }}</p>
          </div>

          <div v-if="formData.trainerList" class="train-box">
            <van-list
              class="recruit-box flex"
              v-model:loading="loading"
              :finished="finished"
              :finished-text="formData.trainerList.length < 1 ? '' : '-没有更多了-'"
              @load="onLoad"
            >
              <div
                class="recruit-item"
                v-for="item in formData.trainerList"
                :key="item"
                @click="recruitDetail(item)"
              >
                <h3>{{ item.title }}</h3>
                <div class="intro">{{ item.description }}</div>
                <div class="recruit-foot flex">
                  <div class="foot-l">
                    <span>{{ item.thirdlyCategoriesName }}</span>
                    <span v-if="item.cityName">{{ item.cityName }}{{ item.countyName }}</span>
                  </div>
                  <div class="foot-r">
                    <span v-if="item.endTime">截止时间：{{ item.endTime.split(' ')[0] }}</span>
                    <span v-else>截止时间：长期有效</span>
                    <span>{{ item.readCount }}浏览</span>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </van-dropdown-menu>
      </div>
    </template>
  </page>
</template>

<script setup>
  import ThreeTreeSelects from '@/components/tree-selects/three-tree-selects'
  // import SecendTreeSelects from "@/components/tree-selects/secend-tree-selects";
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { getJobsList } from '@/api/generic-server'
  import { useGetDistricts } from '@/use/useAreaOptions'
  // import { ossURLJoin } from "@/common";
  import { localProxyStorage } from '@/utils/storage'
  import { getCategoriesLInfo } from '@/utils/skill'
  import { useRoute, useRouter } from 'vue-router'

  const route = useRoute()
  const router = useRouter()

  const itemNavRefs = ref([])

  const loading = ref(false)
  const finished = ref(false)

  const formData = reactive({
    thirdValNav: '科目分类',
    areaTabNav: '区域',
    areaJlIndex: 0,
    threeSelectList: [],
    areaList: [], //区域
    trainerList: [], //教练列表
    isClearThreeTree: false,
    isClearSecendTree: false,
    currentSelectCity: '',
  })

  // 教练请求参数
  const formParams = reactive({
    pageNum: 0,
    pageSize: 12,
    city: '',

    firstCategoriesId: '',
    secondCategoriesId: '',
    thirdlyCategoriesId: '',

    townsList: [],

    countyList: [],
    // county: "",
  })

  formParams.city = route.query.cityLabel || ''
  formData.currentSelectCity = localProxyStorage.currentSelectCity || '广州市'

  onMounted(() => {
    cityChangeList()

    if (!formData.threeSelectList) {
      getCategoriesLInfo().then((res) => {
        formData.threeSelectList = res
      })
    }
  })

  const cityChangeList = () => {
    useGetDistricts().then((res) => {
      // 展示所在城市列表
      res.map((item) => {
        item.children.map((childItem) => {
          if (formData.currentSelectCity == childItem.adName) {
            formData.areaList = childItem.children
          }
        })
      })
      const obj = {
        id: '0',
        text: '全部',
        adName: '全部',
        adCode: '',
        children: [],
      }
      formData.areaList.unshift(obj)
      // formData.areaList.map((areaItem) => {
      //   const childObj = {
      //     id: "0",
      //     text: "全部",
      //     name: "全部",
      //     children: [],
      //   };
      //   // console.log(areaItem, "areaItem");
      //   areaItem["text"] = areaItem.name;
      //   if (areaItem?.children) {
      //     if (areaItem.children.length !== 0) {
      //       areaItem.children.unshift(childObj);
      //       areaItem["isAllSelect"] = false;
      //     }
      //     areaItem.children.map((areaChild) => {
      //       areaChild["text"] = areaChild.name;
      //     });
      //   }
      // });
    })
  }

  const onLoad = async () => {
    // eslint-disable-next-line no-unused-vars
    // const thirdRes = await getCategories();
    const thirdRes = await getCategoriesLInfo().then((res) => {
      formData.threeSelectList = res
      console.log(res, 'getCategoriesLInfo')
    })
    console.log(thirdRes)
    formParams.pageNum += 1

    await getJobsList(formParams)
      .then((res) => {
        let { data } = res
        if (res.code === 0) {
          formData.trainerList = formData.trainerList.concat(data.records)
          // 加载状态结束
          loading.value = false

          // 数据全部加载完成
          if (data.records.length === 0 || data.records.length < formParams.pageSize) {
            finished.value = true
          }
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true
      })
  }
  const recruitDetail = (item) => {
    router.push({
      path: `/recruit-details/${item.jobsId}`,
    })
  }
  // 选中分类id
  const getSelectRes = (dataObj) => {
    if (!dataObj) {
      formParams.thirdlyCategoriesId = ''
    } else {
      for (let key in dataObj) {
        formParams[key] = dataObj[key]
      }
    }
  }

  // 教练查询
  const searchSub = (index) => {
    if (formParams.thirdlyCategoriesId === '0') {
      formParams.thirdlyCategoriesId = ''
    }
    trainCateTitle()
    trainAreaTitle()

    formParams.pageNum = 1
    formParams.pageSize = 12
    if (index || index === 0) {
      itemNavRefs.value[index].toggle()
    }
    return getJobsList(formParams).then((res) => {
      if (res.code === 0) {
        formData.trainerList = res.data.records
        if (res.data.records.length < 1) {
          formData.nullDataTxt = '暂无内容，换个关键词试试吧～'
          // if (formData.isExitCityTrainData) {
          //   formData.nullDataTxt = "暂无内容，换个关键词试试吧～";
          // } else {
          //   formData.nullDataTxt = `哎呀，您当前定位城市"${
          //     formData.currentSelectCity || ""
          //   }"的招聘信息正在收录中~`;
          // }
        }
        return res.data.records
      }
    })
  }

  // 获取选中区域
  // const getAreaRes = (allReturnObj) => {
  //   formData.areaNameList = [];
  //   // townsList - idList
  //   const AreaList = allReturnObj.townsList.map((item) => item);

  //   if (AreaList && AreaList.includes("0")) {
  //     //全选 筛选全部编码
  //     formParams.townsList = allReturnObj.county.children.map((item) => item.label);
  //     formParams.townsList.splice(formParams.townsList.indexOf(undefined), 1);
  //     return;
  //   } else if (!AreaList.includes("0")) {
  //     formParams.townsList = [];
  //     allReturnObj.townsList.map((item) => {
  //       allReturnObj.county.children.map((resItme) => {
  //         if (item === resItme.id && !formParams.townsList.includes(resItme.label)) {
  //           formParams.townsList.push(resItme.label);
  //           formData.areaNameList.push(resItme.name);
  //         }
  //       });
  //     });
  //   }
  // };

  // 教练tab分类标题
  const trainCateTitle = () => {
    if (formParams.thirdlyCategoriesId === '0') {
      formParams.thirdlyCategoriesId = ''
      formData.thirdValNav = '多选'
      document.querySelectorAll('.van-ellipsis')[0].style.color = '#FF6445'
    } else if (
      !formParams.thirdlyCategoriesId &&
      (formParams.secondCategoriesId || formParams.firstCategoriesId)
    ) {
      formData.thirdValNav = '多选'
      document.querySelectorAll('.van-ellipsis')[0].style.color = '#FF6445'
    } else if (
      formParams.thirdlyCategoriesId &&
      formParams.secondCategoriesId &&
      formParams.firstCategoriesId
    ) {
      console.log(formParams.indexObj, 'formParams.indexObj')
      if (formParams.indexObj) {
        const firstNav = formData.threeSelectList[formParams.indexObj.fisrtIndex]
        const secendNav = firstNav?.childCategoriesVos[formParams.indexObj.secendIndex]
        const thirdNav = secendNav?.childCategoriesVos[formParams.indexObj.thirdIndex]
        console.log(thirdNav, 'thirdNav.name')
        if (thirdNav?.id === formParams.thirdlyCategoriesId) {
          formData.thirdValNav = thirdNav.name
        }
        document.querySelectorAll('.van-ellipsis')[0].style.color = '#FF6445'
      }
    } else {
      formData.thirdValNav = '分类'
      document.querySelectorAll('.van-ellipsis')[0].style.color = '#323233'
    }
  }
  // 区域选择
  const areaClick = (item, index) => {
    console.log(item, index)
    formData.areaTabNav = item.adName
    formParams.countyList = []
    formParams.countyList[0] = item.adCode
    formData.areaJlIndex = index
  }
  // 教练tab区域标题
  const trainAreaTitle = () => {
    if (formParams.countyList.length > 0) {
      document.querySelectorAll('.van-ellipsis')[1].style.color = '#FF6445'
    } else {
      formData.areaJlTabNav = '区域'
      document.querySelectorAll('.van-ellipsis')[1].style.color = '#323233'
    }
    // console.log(formParams.townsList);
    // if (formParams.townsList.length > 1) {
    //   formData.areaTabNav = "多选";
    //   document.querySelectorAll(".van-ellipsis")[1].style.color = "#FF6445";
    // } else if (formParams.townsList.length === 1 && formData.areaNameList.length === 1) {
    //   formData.areaTabNav = formData.areaNameList[0];
    //   // console.log(formData.areaNameList);
    //   document.querySelectorAll(".van-ellipsis")[1].style.color = "#FF6445";
    // } else {
    //   formData.areaTabNav = "区域";
    //   if (document.querySelectorAll(".van-ellipsis").length > 0) {
    //     document.querySelectorAll(".van-ellipsis")[1].style.color = "#323233";
    //   }重置
    // }
  }

  // 三级分类重置
  const threeSelectclear = () => {
    formParams.firstCategoriesId = ''
    formParams.secondCategoriesId = ''
    formParams.thirdlyCategoriesId = ''
    formParams.thirdlyCategoriesName = ''
    formData.isClearThreeTree = !formData.isClearThreeTree
    formData.thirdValNav = '分类'
    itemNavRefs.value[0].toggle()
    nextTick(() => {
      searchSub()
      document.querySelectorAll('.jl-tab .van-ellipsis')[0].style.color = '#323233'
    })
  }
  // 二级分类重置
  const secendSelectclear = () => {
    formParams.countyList = []
    formData.areaJlIndex = 0
    formData.areaTabNav = '区域'
    formData.isClearSecendTree = !formData.isClearSecendTree
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';

  @include Icon('selected', 0.15rem, 0.15rem);
  // 公共样式
  $color1: #1f1f1fff;
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .recruit-list {
    min-height: 100vh;
  }
  .category-box {
    height: 4.3rem;
    background-color: #fff;
    .footer {
      z-index: 99;
    }
  }
  .footer {
    position: absolute;
    width: 100%;
    bottom: 0;
    display: flex;
    border-top: 0.01rem solid #eeeeee;
    .reset,
    .submit {
      flex: 1;
      height: 0.4rem;
      line-height: 0.4rem;

      text-align: center;
      font-size: 0.14rem;
      color: #1a1b1d;
    }
    .submit {
      background: #ff9b26;
      color: #fff;
    }
    .reset {
      background-color: #fff;
    }
  }
  .area {
    overflow: hidden;
    height: 4.3rem;
  }
  .area-box {
    position: relative;
    overflow-y: auto;
    height: 4.3rem;
    z-index: 9;
    width: 100vw;
    top: 0;
  }
  :deep(.van-dropdown-item__content) {
    position: relative;
    min-height: 4.3rem;
  }
  .recruit-box {
    flex-wrap: wrap;
  }
  .recruit-item {
    padding: 0.15rem 0.12rem;
    width: 3.75rem;
    flex-shrink: 0;
    background-color: #fff;
    border-bottom: 0.01rem solid #f7f7f7;
    h3 {
      font-size: 0.15rem;
      color: $color1;
    }
    .intro {
      padding-top: 0.04rem;
      margin-bottom: 0.12rem;
      max-height: 0.37rem;
      font-size: 0.13rem;
      color: #616568;
      line-height: 0.17rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .recruit-foot {
      .foot-l span {
        padding: 0.01rem 0.02rem;
        border: 0.01rem solid #979797;
        border-radius: 0.02rem;
        font-size: 0.1rem;
        border-color: rgba($color: #979797, $alpha: 0.8);
        margin-right: 0.04rem;
        color: #979797;
      }
      .foot-r span {
        font-size: 0.1rem;
        color: #979797;
        &:first-child {
          margin-right: 0.1rem;
        }
      }
    }
  }
  :deep(.van-list__loading) {
    margin: 0 auto;
  }
  :deep(.van-dropdown-menu__item) {
    flex: initial;
    padding: 0 0.15rem;
    margin-right: 0.1rem;
  }
  :deep(.van-dropdown-menu__bar) {
    box-shadow: none;
    height: 0.5rem;
  }
  // .van-dropdown-menu__title:before {
  //   position: absolute;
  //   top: 5%;
  //   right: -4px;
  //   border: 50px solid;
  //   border-color: red transparent transparent transparent;
  // }
  :deep(.van-dropdown-menu__title:after) {
    border: 0.035rem solid;
    opacity: 1;
    border-color: transparent transparent #ccc #ccc;
  }
  .null-data {
    margin: 30% auto 0;
    width: 2rem;
    height: 2rem;
    text-align: center;
    img {
      margin: 0 auto;
      display: block;
      width: 1.4rem;
      height: 1.4rem;
    }
    p {
      margin-top: 0.24rem;
      font-size: 0.14rem;
      color: #666666;
      line-height: 0.2rem;
    }
  }
  :deep(.van-list__finished-text) {
    margin: 0 auto;
  }
  .areaSelect {
    background-color: #fff7f5;
    color: #ff6445;
  }
</style>
