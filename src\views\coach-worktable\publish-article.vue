<template>
  <div class="form-wrap" :style="{ height: height + 'px' }">
    <!-- 表单 -->
    <div class="form" :style="{ height: height - 60 + 'px' }">
      <div class="form-item not-border">
        <label class="form-item-label">封面图（选填）</label>
        <div class="form-item-uploader">
          <Uploader
            v-model="formData.coverImage"
            class="uploader"
            preview-size="0.72rem"
            file-type="jpg|png|gif|jpeg"
            :max-size="10240 * 1024"
            :max-count="1"
          />
        </div>
      </div>
      <div class="form-item">
        <label class="form-item-label">标题</label>
        <input class="form-item-input" v-model="formData.title" placeholder="请输入文章标题" />
      </div>
      <div class="form-item">
        <label class="form-item-label">正文</label>
        <quill-editor
          v-model="formData.content"
          class="editor"
          @ready="editorReady"
          @focus="editorFocus"
          @blur="editorBlur"
          placeholder="请输入正文内容"
        />
      </div>
    </div>

    <!--    富文本工具栏 -->
    <quill-toolbar v-show="!footerShow" />

    <div class="fixed-bottom" v-show="footerShow">
      <button v-preventReClick class="i-button publish-btn" @click="onPublish">发布</button>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import Uploader from '@/components/upload-file'
  import { addUserArticle, getArticleDetails, updateUserArticle } from '@/api/generic-server'
  import Schema from 'async-validator'
  import { Toast } from 'vant'
  import { ossURLJoin } from '@/common'
  import useKeepAliveStore from '@/store/keepAlive'
  import useVisualViewport from '@/use/useVisualViewport'
  import QuillEditor from '@/components/quill-editor/editorV2'
  import QuillToolbar from '@/components/quill-editor/toolbarV2'
  const { height } = useVisualViewport()
  import { useEventListener } from '@vant/use'

  const route = useRoute()
  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const articleId = route.query.articleId
  const isEditState = !!articleId
  let quill = ref(null)

  const footerShow = ref(true)
  const formData = reactive({
    title: '',
    content: '',
    coverImage: [],
  })

  //表单校检
  let formValidator = new Schema({
    title: { required: true, message: '请输入标题' },
    content: { required: true, message: '请输入正文内容' },
  })

  const editorReady = (editor) => {
    quill.value = editor
  }

  const editorFocus = () => {
    footerShow.value = false
  }

  const editorBlur = () => {
    footerShow.value = true
  }

  const toPostPage = (articleId) => {
    router.replace({
      name: 'newsDetails',
      params: {
        articleId: articleId,
      },
    })
  }

  const getPostInfo = () => {
    getArticleDetails({ id: articleId, ignoreHits: true }).then((res) => {
      let { data } = res
      formData.title = data.title
      formData.content = data.content
      if (Array.isArray(data.imageList)) {
        if (data.imageList.length > 0) {
          formData.coverImage.push({
            url: ossURLJoin(data.imageList[0]),
            path: data.imageList[0],
          })
        }
      }
    })
  }

  if (isEditState) {
    getPostInfo()
  }

  const onPublish = () => {
    const cloneFormData = JSON.parse(JSON.stringify(formData))
    cloneFormData.coverImage = cloneFormData.coverImage.map((file) => file.path).filter(Boolean)

    formValidator
      .validate(cloneFormData)
      .then(() => {
        let loading = Toast.loading({
          duration: 0,
          forbidClick: true,
          loadingType: 'spinner',
        })

        if (isEditState) {
          cloneFormData.id = articleId
          updateUserArticle(cloneFormData)
            .then(() => {
              keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
              loading.clear()
              Toast.success('修改成功~')
              toPostPage(articleId)
            })
            .catch(() => {
              loading.clear()
            })
        } else {
          addUserArticle(cloneFormData)
            .then((res) => {
              keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
              let { data } = res
              loading.clear()
              Toast.success('发布成功~')
              toPostPage(data)
            })
            .catch(() => {
              loading.clear()
            })
        }
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  useEventListener('scroll', () => {
    document.documentElement.scrollTop = 0
  })
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';
  @include Icon('uploader', 0.2rem, 0.18rem);

  .form-wrap {
    position: relative;
    overflow: hidden;
    background-color: #fff;
    padding-bottom: 0.8rem;

    .form {
      overflow: auto;
      padding: 0 0.1rem;

      .form-item {
        &:not(:last-child) {
          border-bottom: 1px solid #eeeeee;
        }
      }

      .not-border {
        border: none !important;
      }

      .form-item-label {
        font-size: 0.14rem;
        color: #1a1b1d;
        padding-top: 0.17rem;
        display: block;
      }

      .form-item-input {
        width: 100%;
        padding-top: 0.1rem;
        margin-bottom: 0.16rem;
        font-size: 0.15rem;
        outline: none;
        border: none;

        &::-webkit-input-placeholder {
          color: #b2b1b7;
        }
      }

      .form-item-uploader {
        margin-top: 0.06rem;
      }

      .textarea {
        padding: 0;
      }

      .editor {
        margin-top: 0.07rem;
      }
    }

    .uploader {
      :deep(.van-uploader__upload) {
        border-radius: 0.04rem;
        border: 1px dashed #e8e8e8;
      }
      :deep(.van-uploader__preview-image) {
        border-radius: 0.04rem;
      }
    }

    .fixed-bottom {
      width: 3.75rem;
      height: 0.6rem;
      line-height: 0.6rem;
      position: fixed;
      bottom: 0;
      text-align: center;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);
      background-color: #fff;

      .publish-btn {
        width: 3.45rem;
        height: 0.4rem;
        background: #ff9b26;
        box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
        border-radius: 0.2rem;
        font-size: 0.16rem;
        color: #fff;
      }
    }
  }
</style>
