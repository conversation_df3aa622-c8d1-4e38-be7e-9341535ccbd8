<template>
  <Popup v-model:show="_show" class="sku" round @close="onClose" closeable position="bottom">
    <div class="popup__title">立即购买</div>
    <div class="safeguard">
      <div v-for="(item, index) in safeguard" :key="index" class="safeguard__item">
        <div class="safeguard__item-icon">
          <Image :src="item.icon" fit="cover" class="safeguard-item-icon" block />
        </div>
        <span class="safeguard__item-text">{{ item.title }}</span>
      </div>
    </div>
    <div class="sku__header">
      <div class="sku__header__img-wrap">
        <Image :src="productImage" fit="cover" class="sku__header-img" block />
      </div>
      <div class="sku__header__goods-info">
        <div class="sku__goods-price">
          <span class="sku__price-symbol">¥</span>
          <span class="sku__price-num">{{ price }}</span>
        </div>
        <div class="sku__header-item">{{ skuTitle }}</div>
      </div>
    </div>
    <div class="sku__body">
      <div class="sku__group-container">
        <div v-for="(specs, specsIndex) in skuConfig.specs" :key="specsIndex" class="sku__row">
          <div class="sku__row-title">{{ specs.k }}</div>
          <div class="sku__row-specs">
            <div
              v-for="(item, index) in specs.list"
              :key="index"
              class="sku__row-specs-item"
              :class="[
                selected[specsIndex] === item.value ? 'active' : '',
                item.disabled ? 'disabled' : '',
              ]"
              @click="handleActionClick(item, specsIndex)"
            >
              <div class="sku__row-item-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku-actions">
      <button class="sku-actions-button" @click="onSubmit">立即购买</button>
    </div>
  </Popup>
</template>

<script setup>
  import { computed, ref, unref, watch, nextTick } from 'vue'
  import { Popup } from 'vant'
  import { getOssURL, isLogin, toLogin } from '@/common'
  import { isWeChat } from '@/utils'
  import { baseURL } from '@/config'
  import { Toast } from 'vant'
  import router from '@/router'
  import Image from '@/components/image'

  const props = defineProps({
    show: Boolean,
    sku: {
      type: Object,
      default() {
        return {}
      },
    },
    coachId: String,
    productImage: String,
  })

  watch(
    () => props.sku,
    (newVal) => {
      if (newVal !== undefined) {
        nextTick(() => {
          initSKU(newVal)
        })
      }
    },
    { immediate: true },
  )

  const safeguard = ref([])
  const selected = ref([])
  const skuConfig = ref({})

  safeguard.value = [
    {
      title: '平台十年经营保障',
      icon: getOssURL('/miniprogram/safeguard-1.png'),
    },
    {
      title: '24小时服务',
      icon: getOssURL('/miniprogram/safeguard-2.png'),
    },
    {
      title: '购课退款无忧',
      icon: getOssURL('/miniprogram/safeguard-3.png'),
    },
  ]

  const emit = defineEmits(['update:show', 'change', 'submit'])

  const _show = computed(() => props.show)
  const selectedSku = computed(() => {
    if (!props.sku) return 0
    const skuList = props.sku.list
    let sku = null

    if (selected.value.every((value) => value !== '')) {
      let key = selected.value.join('-')
      for (let i = 0; i < skuList.length; i++) {
        const item = skuList[i]
        if (item.key === key) {
          sku = item
          break
        }
      }
    }

    return sku
  })
  const price = computed(() => {
    if (!props.sku) return 0

    const skuList = props.sku.list
    let price = 0

    if (selected.value.every((value) => value !== '')) {
      let key = selected.value.join('-')
      for (let i = 0; i < skuList.length; i++) {
        const item = skuList[i]
        if (item.key === key) {
          price = item.totalAmount
          break
        }
      }
    } else if (selected.value[0]) {
      for (let i = 0; i < skuList.length; i++) {
        const item = skuList[i]
        if (item.keys[0] === selected.value[0]) {
          price = item.price
          break
        }
      }
    }

    return price
  })
  const skuTitle = computed(() => {
    let title = '请选择套餐属性'
    if (props.sku) {
      const { list } = props.sku
      if (selected.value.every((item) => item !== '')) {
        let key = selected.value.join('-')
        list.forEach((item) => {
          if (item.key === key) title = `已选：${item.labelNames.join('，')}`
        })
      } else if (selected.value[0]) {
        list.forEach((item) => {
          if (item.key[0] === selected.value[0]) title = `已选：${item.labelNames[0]}`
        })
      } else if (selected.value[1]) {
        list.forEach((item) => {
          if (item.key[1] === selected.value[1]) title = `已选：${item.labelNames[1]}`
        })
      } else {
        title = '请选择套餐属性'
      }
    }
    return title
  })
  function onSubmit() {
    console.log(selected.value)
    console.log(selectedSku.value)
    if (!isWeChat()) {
      let link = encodeURIComponent(baseURL + location.pathname + location.search)
      router.push({
        name: 'browserPay',
        query: {
          link,
        },
      })
      return
    }

    if (!isLogin()) {
      toLogin()
      return
    }

    if (!selectedSku.value.id || !selectedSku.value.quantity) {
      Toast('请选择授课方式和课时包')
      return
    }
    router.push({
      name: 'payCourse',
      query: {
        classesId: selectedSku.value.id,
        coachId: props.coachId,
        quantity: selectedSku.value.quantity,
      },
    })
    // emit('submit', {
    //   selected: selected.value,
    //   selectedSku: selectedSku.value,
    // })
  }
  function onClose() {
    emit('update:show', false)
  }
  const initSKU = (newVal) => {
    skuConfig.value = newVal
    resetSelected()
    initAllSpecDisabled()
  }
  const resetSelected = () => {
    if (props.sku.selectedKey) {
      const keys = props.sku.selectedKey?.split('-') || []
      console.log(1, keys)
      const selectedList = new Array(props.sku.specs.length).fill('')

      keys.forEach((key, i) => {
        if (key) selectedList[i] = key
      })

      selected.value = selectedList || []
    }
  }
  const initAllSpecDisabled = () => {
    const sku = props.sku
    sku.specs.forEach((spec, specIndex) => {
      spec.list.forEach((attr) => {
        attr.disabled = checkOptional(attr, specIndex)
      })
    })

    skuConfig.value = sku
    console.log('aaaaa', sku)
  }
  /**
   * 规格是否可选
   * @param attr 属性
   * @param spec 规格下标
   */
  const checkOptional = (attr, specIndex) => {
    const list = JSON.parse(JSON.stringify(selected.value))
    list[specIndex] = attr.value

    let disabled = props.sku.list.some((item) => {
      let i = 0

      for (let j = 0; j < list.length; j++) {
        if (list[j] === item.keys[j]) {
          i++
        } else if (list[j] === '') {
          i++
        }
      }

      // 符合下面条件就退出
      return i === props.sku.specs.length
    })

    return !disabled
  }
  const handleActionClick = (item, specsIndex) => {
    const { value, disabled } = item
    const selectedList = JSON.parse(JSON.stringify(unref(selected.value)))
    if (disabled) return
    if (selectedList[specsIndex] === value) {
      selectedList[specsIndex] = ''
    } else {
      selectedList[specsIndex] = value
    }
    selected.value = selectedList
    initAllSpecDisabled()
    emit('change', selected.value)
  }
</script>

<style lang="scss">
  .sku {
    display: flex;
    flex-direction: column;
    height: 60vh;
    min-height: 60vh;
    max-height: 80vh;
    overflow-y: visible;
    font-size: 0.14rem;
    background: #fff;
  }

  .popup__title {
    font-size: 0.16rem;
    font-weight: 600;
    color: #1a1b1d;
    margin: 0 0.16rem;
    padding: 0.12rem 0 0.02rem 0;
    text-align: center;
  }

  .safeguard {
    padding: 0.13rem 0.16rem;
    display: flex;
    justify-content: space-around;
    .safeguard__item {
      display: flex;
      align-items: center;
    }
    .safeguard__item-icon {
      width: 0.16rem;
      height: 0.16rem;
      margin-right: 0.04rem;
      .safeguard-item-icon {
        vertical-align: initial;
      }
    }
    .safeguard__item-text {
      font-size: 0.12rem;
      color: #b2b1b8;
    }
  }

  .sku__header {
    display: flex;
    margin: 0 0.16rem;
    .sku__header__img-wrap {
      width: 0.96rem;
      height: 0.96rem;
      margin: 0.12rem 0.12rem 0.12rem 0;
      overflow: hidden;
      border-radius: 0.04rem;
      background: #ccc;
      .sku__header-img {
        width: 100%;
        height: 100%;
        display: block;
      }
    }
    .sku__header__goods-info {
      display: flex;
      flex-direction: column;
      padding: 0.12rem 0.2rem 0.12rem 0;
      .sku__goods-price {
        margin-left: -0.01rem;
        color: #f2270c;
        font-family: 'regular';
        .sku__price-symbol {
          display: inline-block;
          padding-bottom: 0.02rem;
          font-size: 0.16rem;
          vertical-align: bottom;
        }
        .sku__price-num {
          font-size: 0.22rem;
          font-weight: 500;
          vertical-align: bottom;
          word-wrap: break-word;
        }
        .sku__header-item {
          margin-top: 0.08rem;
          color: #969799;
          font-size: 0.12rem;
          line-height: 0.16rem;
        }
      }
    }
  }

  .sku__body {
    flex: 1 1 auto;
    min-height: 0.44rem;
    max-height: 2.2rem;
    overflow-y: scroll;
    .sku__group-container {
      padding-top: 0;

      .sku__row {
        margin: 0 0.16rem 0.12rem;
        .sku__row-title {
          font-size: 0.14rem;
          color: #1a1b1d;
        }
        .sku__row-specs {
          padding-top: 0.12rem;
          .sku__row-specs-item {
            position: relative;
            overflow: hidden;
            color: #323233;
            border-radius: 0.04rem;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 0.4rem;
            margin: 0 0.12rem 0.12rem 0;
            font-size: 0.13rem;
            line-height: 0.16rem;
            vertical-align: middle;
            border: 1px solid transparent;
            box-sizing: border-box;
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: #f7f8fa;
            }

            .sku__row-item-name {
              z-index: 1;
              padding: 0.08rem;
            }
          }
          .active {
            border: 1px solid #ffa524;
            color: #ffa524;
            &::before {
              background: #fff6e9;
            }
          }
          .disabled {
            color: #c8c9cc;
            background: #f2f3f5;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .sku-actions {
    display: flex;
    flex-shrink: 0;
    padding: 0.08rem 0.16rem;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.06);
    button::before,
    button::after {
      border: none;
    }
    .sku-actions-button {
      flex: 1;
      height: 0.4rem;
      background: linear-gradient(270deg, #ff6445 0%, #ff9b26 100%);
      border-radius: 0.2rem;
      font-size: 0.16rem;
      font-weight: 600;
      color: #feffff;
    }
  }
</style>
