<template>
  <header class="header">
    <div class="header-search">
      <router-link to="/search">
        <div class="search-input">
          <van-icon name="search" size="0.18rem" />
          <div class="search-input-placeholder">搜索感兴趣的教练</div>
        </div>
      </router-link>
    </div>
    <div class="placeholder-box"></div>
  </header>
</template>

<script setup></script>

<style lang="scss" scoped>
  .header {
    height: 100%;
    user-select: none;
    background: #fff;

    .header-search {
      position: fixed;
      top: 0;
      left: var(--window-left);
      right: var(--window-right);
      padding: 0.08rem 0;
      z-index: 10;
      background: #fff;

      .search-input {
        width: 3.45rem;
        height: 0.34rem;
        padding: 0.08rem 0.1rem;
        margin: 0 auto;
        background: #f3f3f3;
        border-radius: 0.17rem;
        display: flex;
        align-items: center;
        color: #b2b1b7;

        .search-input-placeholder {
          font-size: 0.12rem;
          margin-left: 0.05rem;
        }
      }
    }

    .placeholder-box {
      width: 100%;
      height: 0.5rem;
    }
  }
</style>
