<template>
  <Teleport to="body">
    <div class="ActThemePopup">
      <van-popup v-model:show="show" @close="onClose">
        <div class="popup-wrap">
          <div class="close" @click="updateShow(false)" />
          <div class="title">
            <i class="star" />
            <span>活动规则</span>
            <i class="star" />
          </div>

          <div class="content">
            <p>1、参与对象为已入驻爱教练平台的教练</p>
            <p>2、报名成功后请转发到朋友圈邀请亲友点赞</p>
            <p>3、每个用户每天只能点赞一次，点赞之后不能取消</p>
            <p>4、活动截止时间 5月25日12:00:00 点赞结束</p>
            <p>5、活动结束后，参与活动的教练请联系小爱</p>
          </div>
        </div>
      </van-popup>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, watch } from 'vue'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  watch(
    () => props.modelValue,
    (val) => (show.value = val),
  )

  const show = ref(props.modelValue || false)

  const updateShow = (state) => emit('update:modelValue', state)

  const onClose = () => {
    updateShow(false)
  }
</script>

<style lang="scss" scoped>
  .popup-wrap {
    width: 3.15rem;
    //height: 2.54rem;
    background: url('../images/act-rules-popup-bg.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 0.1rem;
    border: 1px solid transparent;
    padding-bottom: 0.25rem;
    position: relative;
  }

  .close {
    position: absolute;
    right: 0.12rem;
    top: 0.12rem;
    width: 0.12rem;
    height: 0.12rem;
    background: url('../images/popup-close.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  .title {
    margin-top: 0.25rem;
    margin-bottom: 0.1rem;
    text-align: center;

    span {
      margin: 0 0.05rem;
      font-size: 0.16rem;
      color: #1a1b1d;
      font-weight: 600;
    }
  }

  .star {
    width: 0.15rem;
    height: 0.1rem;
    display: inline-block;
    background: url('../images/star.png') no-repeat;
    background-size: 100% 100%;
  }

  .content {
    padding: 0 0.15rem;

    p {
      line-height: 0.2rem;
      color: #1a1b1d;
      margin-bottom: 0.08rem;
    }
  }

  :deep(.van-popup) {
    background: transparent;
  }
</style>
