<template>
  <div class="ActPack">
    <img class="pack-img" src="../images/welfare.png" alt="" />
    <button class="btn" @click="$router.push('/act/a20230321jlzm/collect-gift-bag')"></button>
    <ContactPopup v-model="show" />
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import ContactPopup from './ContactPopup'

  const show = ref(false)
</script>

<style lang="scss" scoped>
  .ActPack {
    width: 3.5rem;
    height: 2.62rem;
    margin: 0 auto;
    border-radius: 0.08rem;
    border: 0.02rem solid #376cfe;
    background: url('../images/bg-1.png') no-repeat;
    background-size: 100% 100%;

    .pack-img {
      width: 2.84rem;
      height: 1.72rem;
      margin: 0.16rem auto 0 auto;
      display: block;
    }

    .btn {
      width: 3.1rem;
      height: 0.5rem;
      background: url('../images/lqlb-btn.png') no-repeat;
      background-size: 100% 100%;
      display: block;
      margin: 0.1rem auto 0 auto;
      cursor: pointer;
    }
  }
</style>
