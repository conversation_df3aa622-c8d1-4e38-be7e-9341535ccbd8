<template>
  <div class="form-wrap min-height-100">
    <div class="video-wrap">
      <upload-video
        v-model="videoFile"
        tip="上传文件大小不超50M，仅支持mp4/mov格式"
        :before-read="onBeforeRead"
        :after-read="onFileAfterRead"
        :before-delete="onFileDelete"
        width="3.35rem"
        height="1.95rem"
      />
    </div>
    <!-- 表单 -->
    <div class="form">
      <div class="form-item">
        <label class="form-item-label">标题</label>
        <input class="form-item-input" v-model="form.title" placeholder="请输入视频标题" />
      </div>
      <div class="form-item not-border">
        <label class="form-item-label">封面图（选填）</label>
        <div class="form-item-uploader">
          <Uploader
            class="uploader"
            v-model="form.coverImage"
            :max-size="10240 * 1024"
            file-type="jpg|png|gif|jpeg"
            preview-size="0.72rem"
            :max-count="1"
          />
        </div>
      </div>
      <div class="form-item">
        <label class="form-item-label">视频描述</label>
        <van-field
          class="form-item-input textarea"
          v-model="form.description"
          rows="3"
          autosize
          type="textarea"
          placeholder="请输入视频描述"
        />
      </div>
    </div>

    <div class="fixed-bottom">
      <button v-preventReClick class="i-button publish-btn" @click="onPublish">发布</button>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import UploadVideo from '@/components/upload-video'
  import Uploader from '@/components/upload-file'
  import Schema from 'async-validator'
  import {
    addUserVideos,
    getVideoDetails,
    uploadFile,
    updateUserVideos,
  } from '@/api/generic-server'
  import { Toast } from 'vant'
  import { ossURLJoin } from '@/common'
  import useKeepAliveStore from '@/store/keepAlive'
  import { ossTempURL } from '@/config'

  const route = useRoute()
  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const videoId = route.query.videoId
  const isEditState = !!videoId
  const videoFile = ref(null)
  const ossVideoSnapshot = '?x-oss-process=video/snapshot,t_1,f_jpg,m_fast,ar_auto'

  const form = reactive({
    title: '',
    description: '',
    tempVideoUrl: '',
    coverImage: [],
  })

  const onFileDelete = () => {
    form.tempVideoUrl = ''
    return true
  }

  //表单校检
  let formValidator = new Schema({
    tempVideoUrl: { required: true, message: '请上传视频' },
    title: { required: true, message: '请输入标题' },
  })

  const getPostInfo = () => {
    let params = {
      id: videoId,
      ignoreHits: true,
    }
    getVideoDetails(params).then((res) => {
      const { data } = res
      form.title = data.title
      form.description = data.description
      form.tempVideoUrl = data.videosUrl
      let videoLink = ossURLJoin(data.videosUrl)

      videoFile.value = {
        url: videoLink,
        poster: videoLink + ossVideoSnapshot,
      }

      if (Array.isArray(data.imageList)) {
        if (data.imageList.length > 0) {
          form.coverImage.push({
            url: ossURLJoin(data.imageList[0]),
            path: data.imageList[0],
          })
        }
      }
    })
  }

  if (isEditState) {
    getPostInfo()
  }

  const onBeforeRead = (file) => {
    const specifyFormat = ['mp4', 'mov']
    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
    const fileSize = file.size / 1024 / 1024
    const isMeet = specifyFormat.includes(fileType) // 是否符合规定文件格式
    if (!isMeet) {
      Toast('只支持上传格式为 ' + specifyFormat.join('、') + ' 的视频文件')
      return false
    }

    if (fileSize > 50) {
      Toast('视频文件大小不能超过50MB')
      return false
    }

    return true
  }

  // 上传视频
  const onFileAfterRead = (file) => {
    file.status = 'uploading'
    file.message = '上传中...'

    let formData = new FormData()
    formData.append('file', file.file)

    uploadFile(formData)
      .then((res) => {
        let { data, code } = res
        if (code === 0) {
          form.tempVideoUrl = data
          let link = ossTempURL + '/' + data
          // 这里重新覆盖本地预览的视频地址

          file.url = link
          file.poster = link + ossVideoSnapshot
          file.status = 'done'
        }
      })
      .catch(() => {
        file.status = 'failed'
        file.message = '上传失败'
      })
  }

  const toPostPage = (id) => {
    router.replace({
      name: 'videoDetails',
      params: {
        id: id,
      },
    })
  }

  const onPublish = () => {
    let cloneFormData = JSON.parse(JSON.stringify(form))
    cloneFormData.coverImage = cloneFormData.coverImage.map((file) => file.path).filter(Boolean)

    formValidator
      .validate(cloneFormData)
      .then(() => {
        let loading = Toast.loading({
          duration: 0,
          forbidClick: true,
          loadingType: 'spinner',
        })

        if (isEditState) {
          cloneFormData.id = videoId
          updateUserVideos(cloneFormData)
            .then(() => {
              keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
              loading.clear()
              Toast.success('修改成功~')
              toPostPage(videoId)
            })
            .catch(() => {
              loading.clear()
            })
        } else {
          addUserVideos(cloneFormData)
            .then((res) => {
              let { data } = res
              keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
              loading.clear()
              Toast.success('发布成功~')
              toPostPage(data)
            })
            .catch(() => {
              loading.clear()
            })
        }
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('uploader', 0.2rem, 0.18rem);

  .form-wrap {
    background-color: #fff;
    padding-bottom: 1rem;

    .video-wrap {
      padding: 0.12rem 0.2rem 0 0.2rem;

      .player {
        width: 100%;
        height: 1.95rem;
        border: 1px solid #ccc;
        border-radius: 0.04rem;
        overflow: hidden;
      }
    }

    .form {
      padding: 0 0.2rem;

      .form-item {
        padding: 0 0.1rem;

        &:not(:last-child) {
          border-bottom: 1px solid #eeeeee;
        }
      }

      .not-border {
        border: none !important;
      }

      .form-item-label {
        font-size: 0.14rem;
        color: #1a1b1d;
        padding-top: 0.17rem;
        display: block;
      }

      .form-item-input {
        width: 100%;
        padding-top: 0.1rem;
        margin-bottom: 0.16rem;
        font-size: 0.15rem;
        outline: none;
        border: none;

        &::-webkit-input-placeholder {
          color: #b2b1b7;
        }
      }

      .form-item-uploader {
        margin-top: 0.06rem;
      }

      .textarea {
        padding: 0.1rem 0 0 0;
      }
    }

    .uploader {
      :deep(.van-uploader__upload) {
        border-radius: 0.04rem;
        border: 1px dashed #e8e8e8;
      }

      :deep(.van-uploader__preview-image) {
        border-radius: 0.04rem;
      }
    }

    .fixed-bottom {
      width: 3.75rem;
      height: 0.6rem;
      line-height: 0.6rem;
      position: fixed;
      bottom: 0;
      text-align: center;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);
      background-color: #fff;

      .publish-btn {
        width: 3.45rem;
        height: 0.4rem;
        background: #ff9b26;
        box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
        border-radius: 0.2rem;
        font-size: 0.16rem;
        color: #fff;
      }
    }
  }
</style>
