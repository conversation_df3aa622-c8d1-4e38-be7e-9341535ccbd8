import { beforeEnter } from '@/views/ai/utils/index'

export default [
  {
    path: '/ai/sale-assistant',
    name: 'SaleAssistant',
    meta: {
      title: '销售助理',
      keepAlive: true,
    },
    beforeEnter,
    component: () => import('../views/ai/saleAssistant/home.vue'),
  },
  {
    path: '/ai/sale-assistant/history',
    name: 'ChatHistory',
    meta: {
      title: '销售助理',
      keepAlive: true,
    },
    component: () => import('../views/ai/saleAssistant/history.vue'),
  },
  {
    path: '/ai/sale-assistant/details',
    name: 'ChatMessage',
    meta: {
      title: '销售助理',
      keepAlive: true,
    },
    component: () => import('../views/ai/saleAssistant/details.vue'),
  },
  {
    path: '/ai/sale-assistant/test',
    name: 'TestAssistant',
    meta: {
      title: '销售助理',
    },
    component: () => import('../views/ai/saleAssistant/test.vue'),
  },
]
