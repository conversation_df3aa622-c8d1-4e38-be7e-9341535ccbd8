<template>
  <page
    :title="$route.meta?.title"
    navigation-bar-type="transparent"
    :navigationBarCoverage="9999999"
    :loading="pageLoading"
  >
    <template #page>
      <div class="banner">
        <img src="./images/active-image-1.png" alt="" />
        <div class="act-rules-btn" @click="actRulesShow = true" />
        <div class="act-end-time">活动截止时间：5月25日 12:00:00</div>
        <LikesMarquee />
      </div>

      <div class="act-content">
        <Tabs v-model="tabIndex">
          <template #intro>
            <div class="tab0-content">
              <div style="position: relative">
                <img class="introduce" :src="getOssURL('/applet/introduce2-1.png')" />
                <div class="video-box">
                  <!--TODO: 按设计稿自定义的视频播放器,如果出现Bug，请切换为 XgPlayer 播放器 -->
                  <Player
                    v-if="false"
                    :src="getOssURL(videoURL)"
                    @play="onPlayerPlay"
                    @pause="onPlayerPause"
                    :poster="getOssURL('/applet/videoPoster.png')"
                  />
                  <!-- TODO: XgPlayer 备用播放器 -->
                  <XgPlayer
                    v-if="true"
                    ref="player"
                    :url="getOssURL(videoURL)"
                    :poster="getOssURL('/applet/videoPoster.png')"
                    height="1.82rem"
                  />
                </div>
                <!-- <div v-if="videoTitleShow" class="video-title" /> -->
              </div>
              <template v-for="item in 15" :key="item">
                <img
                  v-if="item !== 1"
                  class="introduce"
                  :src="getOssURL(`/applet/introduce2-${item}.png`)"
                />
              </template>

              <div class="fixed-page-footer">
                <SolidButton v-if="actInfo.btnType === 1" class="footer-btn" @click="onEnterFor">
                  我要成为明星教练
                </SolidButton>

                <SolidButton
                  v-if="actInfo.btnType === 2"
                  @click="openSharePopup(actInfo.helpCoachInfo.coachInfo)"
                >
                  分享集赞
                </SolidButton>
                <SolidButton
                  v-if="actInfo.btnType === 3 && actInfo.finalResult"
                  @click="contactPopupShow = true"
                  :hand="false"
                >
                  联系客服，领取活动参与权益
                </SolidButton>
                <SolidButton
                  v-if="(actInfo.btnType === 3 || actInfo.btnType === 4) && !actInfo.finalResult"
                  @click="onCollectGiftBag"
                  :hand="false"
                >
                  联系客服，领取活动礼包
                </SolidButton>
              </div>
            </div>
          </template>
          <template #top>
            <div class="tab1-content">
              <ActProgress v-if="actInfo.activityStatus !== 2" />
              <ActEnded v-else />

              <div class="fixed-page-footer" v-show="footerShow">
                <SolidButton v-if="!isLike" @click="giveLike">点赞</SolidButton>
                <SolidButton
                  v-else-if="isSelf"
                  @click="openSharePopup(actInfo.helpCoachInfo.coachInfo)"
                >
                  分享集赞
                </SolidButton>
                <SolidButton v-else @click="openSharePopup(actInfo.helpCoachInfo.coachInfo)">
                  帮ta集赞
                </SolidButton>
                <SolidButton
                  v-if="actInfo.btnType === 3 && actInfo.finalResult"
                  @click="contactPopupShow = true"
                >
                  联系客服，领取活动参与权益
                </SolidButton>
                <SolidButton
                  v-if="(actInfo.btnType === 3 || actInfo.btnType === 4) && !actInfo.finalResult"
                  @click="onCollectGiftBag"
                >
                  联系客服，领取活动礼包
                </SolidButton>
              </div>
            </div>
          </template>
        </Tabs>
      </div>

      <!-- 活动规则 -->
      <ActRulesPopup v-model="actRulesShow" />
      <!-- 分享集赞 -->
      <SharePopup v-model="sharePopupShow" />
      <!-- 登录弹窗 -->
      <LoginPopup v-model="loginPopupShow" />
      <ContactPopup v-model="contactPopupShow" />
      <GiftPopup v-model="giftPopupShow" />
      <!-- 点赞动画 -->
      <LikeAnimation ref="likeAnimRef" />
    </template>
  </page>
</template>

<script>
  export default { name: 'a20230510jlzm' }
</script>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
  import { Toast } from 'vant'
  import { useChildren, useEventListener } from '@vant/use'
  import Tabs from './components/Tabs'
  import LikesMarquee from './components/LikesMarquee'
  import ActRulesPopup from './components/ActRulesPopup'
  import ActProgress from './components/ActProgress'
  import SharePopup from './components/SharePopup'
  import LoginPopup from './components/LoginPopup'
  import GiftPopup from './components/GiftPopup'
  import ActEnded from './components/ActEnded'
  import LikeAnimation from './components/LikeAnimation'
  import ContactPopup from './components/ContactPopup'
  import SolidButton from './components/SolidButton'
  import Player from './components/Player'
  import { reqActInfo, reqUserLike } from './api'
  import { isLogin, getOssURL } from '@/common'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'
  import { isInViewPort } from '@/utils/elem'
  import { isQyWeChat, isWeChat, throttle } from '@/utils'
  import { toOauthPage } from '@/views/act/a20230510jlzm/weChatAuth'
  import useKeepAliveStore from '@/store/keepAlive'
  import XgPlayer from '@/components/xg-player'

  const route = useRoute()
  const router = useRouter()
  const player = ref(null)

  const videoURL = '/applet/jlzm-1.mp4'
  // TODO: t_3000 表示取的第一秒的画面 以此类推
  // const ossVideoSnapshot = "?x-oss-process=video/snapshot,t_3000,f_jpg,m_fast,ar_auto";

  const shareUserId = route.query.shareUserId || null

  if (isWeChat() && !sessionProxyStorage.weChatUId && !isQyWeChat() && !isLogin()) {
    sessionProxyStorage.lastActPageRoute = route.fullPath
    toOauthPage()
  }

  const tabIndex = ref(shareUserId ? 1 : 0)
  if (route.query.tabIndex) {
    tabIndex.value = Number(route.query.tabIndex) || tabIndex.value
  }
  const keepAliveStore = useKeepAliveStore()
  const actRulesShow = ref(false)
  const sharePopupShow = ref(false)
  const loginPopupShow = ref(false)
  const contactPopupShow = ref(false)
  const giftPopupShow = ref(false)
  const pageLoading = ref(true)
  const likeAnimRef = ref(null)
  const footerShow = ref(false)
  const videoTitleShow = ref(true)

  const actInfo = ref({})
  const coachShareInfo = ref({})

  const isLike = computed(() => {
    if (!actInfo.value) return
    return !!actInfo.value.currentLikeCoachUserId
  })

  const isSelf = computed(() => {
    if (!actInfo.value) return
    return actInfo.value.loginUserId === actInfo.value.helpCoachInfo?.coachInfo.coachUserId
  })

  const openLoginPopup = () => {
    loginPopupShow.value = true
  }

  function setWeChatShare() {
    setWxShare({
      title: '投票选出，明星运动教练，让优秀的人更优秀！',
      desc: '谁是明星教练，您决定！快来投票吧！',
      link: baseURL + '/act/a20230510jlzm',
      imgUrl: ossURL + '/applet/510ActShareIcon.png',
    })
  }

  function initialize() {
    const params = { shareUserId }
    reqActInfo(params)
      .then((res) => {
        pageLoading.value = false
        const { data } = res
        actInfo.value = data

        if (data.activityStatus === 2) {
          tabIndex.value = 1
          Toast({
            message: '活动已结束',
            duration: 3000,
          })
        }

        if (data.btnType === 2) {
          tabIndex.value = 1
        }
      })
      .catch(() => {
        pageLoading.value = false
      })
  }

  const onEnterFor = () => {
    if (!isLogin()) {
      openLoginPopup(true)
    } else {
      router.push('/act/a20230510jlzm/enter-for')
    }
  }

  const onCollectGiftBag = () => {
    if (isLogin()) {
      router.push('/act/a20230510jlzm/collect-gift-bag')
    } else {
      openLoginPopup()
    }
  }

  const openSharePopup = (coachInfo) => {
    sharePopupShow.value = true
    coachShareInfo.value = coachInfo
  }

  const openGiftPopup = () => {
    giftPopupShow.value = true
  }

  // 点赞动画
  const playLikeAnim = () => {
    likeAnimRef.value?.play()
  }

  const openContactPopup = () => {
    // 每天只弹一次，活动期间做多三次
    let nowTime = new Date().getTime()
    const dayEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime().toString()
    const CONTACT_POPUP_321 = localProxyStorage.CONTACT_POPUP_321

    if (CONTACT_POPUP_321 && typeof CONTACT_POPUP_321 === 'string') {
      const data = localProxyStorage.CONTACT_POPUP_321.split('|')
      if (nowTime > data[data.length - 1] && data.length < 3) {
        contactPopupShow.value = true
        data.push(dayEndTime)
        localProxyStorage.CONTACT_POPUP_321 = data.join('|')
      }
    } else {
      localProxyStorage.CONTACT_POPUP_321 = dayEndTime
      contactPopupShow.value = true
    }
  }

  const showContactPopup = () => (contactPopupShow.value = true)

  const giveLike = () => {
    if (isLogin()) {
      let params = { coachUserId: actInfo.value.helpCoachInfo.coachInfo.coachUserId }

      reqUserLike(params).then(() => {
        playLikeAnim()
        initialize()
        // 3s后 弹窗领取礼包弹窗
        setTimeout(() => {
          if (isSelf.value && actInfo.value.btnType === 2) {
            openSharePopup(actInfo.value.helpCoachInfo.coachInfo)
          } else {
            giftPopupShow.value = true
          }
        }, 1800)
      })
    } else {
      openLoginPopup()
    }
  }

  const handleScroll = () => {
    if (tabIndex.value !== 1) return

    const elem = document.documentElement.querySelector('.Coach')
    if (!elem) return
    footerShow.value = !isInViewPort(elem)
  }

  const beforeRouteLeave = (to) => {
    let pages = ['coachDetails']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('a20230510jlzm')
    }
  }

  function onPlayerPlay() {
    videoTitleShow.value = false
  }

  function onPlayerPause() {
    videoTitleShow.value = true
  }

  useEventListener('scroll', throttle(handleScroll, 100))

  const { linkChildren } = useChildren('321ACT')

  linkChildren({
    actInfo,
    openSharePopup,
    openLoginPopup,
    openContactPopup,
    showContactPopup,
    openGiftPopup,
    shareUserId,
    coachShareInfo,
    initialize,
    playLikeAnim,
    giveLike,
  })

  onBeforeRouteLeave(beforeRouteLeave)
  setWeChatShare()
  initialize()
</script>

<style lang="scss" scoped>
  @import './font/index.css';

  .banner {
    position: relative;

    img {
      width: 100%;
      height: 3.04rem;
    }

    .act-rules-btn {
      width: 0.83rem;
      height: 0.33rem;
      background: url('./images/act-rule-btn.png') no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0;
      right: 0.3rem;
      z-index: 999;
    }

    .act-end-time {
      width: 100%;
      position: absolute;
      bottom: 0.64rem;
      line-height: 0.2rem;
      text-align: center;
      color: #ffffff;
    }
  }

  .act-content {
    margin-top: -0.54rem;
  }

  .tab0-content {
    .video-box {
      position: absolute;
      top: 1.52rem;
      left: 0.26rem;
      width: 3.24rem;
      height: 1.82rem;
      border: 1px solid #9f48c8;
    }

    .video-title {
      position: absolute;
      top: 2.89rem;
      left: 0.14rem;
      width: 2.55rem;
      height: 0.25rem;
      background: url('./images/video-title.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .tab1-content {
    padding-top: 0.1rem;
    padding-bottom: 1rem;
  }

  .introduce {
    width: 100%;
    vertical-align: top;
  }

  .fixed-page-footer {
    background-color: transparent;
    z-index: 150;
    padding: 0.15rem;
  }

  :deep(.inviter) {
    display: none;
  }
</style>
