<template>
  <van-popup round position="bottom">
    <van-cascader
      ref="cascadeInstance"
      v-model="cascade"
      title="请选择所在地区"
      @close="onClose"
      :options="options"
      :field-names="{
        text: 'adName',
        value: 'adCode',
        children: 'children',
      }"
      @finish="onFinish"
    />
  </van-popup>
</template>

<script setup>
  import { ref, shallowRef, watch } from 'vue'
  import { districtsURL } from '@/config'
  import axios from 'axios'

  const props = defineProps({
    defaultSelected: {
      type: String,
      default: '',
    },
  })
  const emit = defineEmits(['finish', 'update:show'])

  const cascade = ref('')
  const cascadeInstance = ref(null)

  watch(
    () => props.defaultSelected,
    (newVal) => {
      cascade.value = newVal
    },
    {
      immediate: true,
    },
  )

  // 地区数据
  let options = shallowRef(null)

  axios.get(districtsURL).then((res) => {
    let { data } = res
    data.map((item) => {
      item.children.map((childItem) => {
        if (Array.isArray(childItem.children) && childItem.children.length === 0) {
          delete childItem.children
        }
      })
    })
    options.value = data
  })

  const onClose = () => {
    emit('update:show', false)
  }

  const onFinish = (res) => {
    res.address = res.selectedOptions.map((option) => option.adName).join('/')
    emit('finish', res)
    onClose()
  }
</script>
