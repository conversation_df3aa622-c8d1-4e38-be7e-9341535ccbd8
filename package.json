{"name": "ijiaolian-h5", "version": "4.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode testing", "build:pre": "vue-cli-service build --mode pre", "build:prod": "vue-cli-service build --mode production", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@vant/use": "^1.4.2", "@vueup/vue-quill": "^1.0.0-beta.9", "async-validator": "^4.0.7", "axios": "^0.26.1", "clipboard": "^2.0.11", "core-js": "^3.8.3", "cropperjs": "^1.5.13", "dayjs": "^1.11.1", "html2canvas": "^1.4.1", "lottie-web": "^5.10.2", "markdown-it": "^14.1.0", "normalize.css": "^8.0.1", "pinia": "^2.0.13", "qrcodejs2": "0.0.2", "quill-image-uploader": "^1.2.2", "tiny-cookie": "^2.4.0", "vant": "^3.6.12", "vconsole": "^3.14.6", "vue": "^3.2.13", "vue-router": "^4.0.3", "weixin-js-sdk": "^1.6.0", "xgplayer": "^2.31.6"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/babel-plugin-jsx": "^1.1.1", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^2.0.0-0", "@vue/vue3-jest": "^27.0.0-alpha.1", "babel-jest": "^27.0.6", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "jest": "^27.0.5", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0"}, "volta": {"node": "12.9.0"}}