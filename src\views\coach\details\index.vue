<template>
  <Page :title="$route.meta.title" navigation-bar-type="transparent">
    <template #page>
      <!-- 商品图 -->
      <ProductImage :loading="loading" :medium="medium" />
      <!-- 商品信息 -->
      <ProductInfo :loading="loading" :data-source="details" />
      <!-- 商品服务保障 -->
      <ProductService />
      <!-- 荣誉证书 -->
      <ProductCertificate :loading="loading" :certificate="certificate" />
      <!-- 教练动态 -->
      <ProductNews :loading="newsLoading" :news="news" />
      <!-- 详情介绍 -->
      <ProductDetails :data-source="details" />
      <!-- 购物须知 -->
      <ShoppingNotes />
      <!-- 关于爱教练 -->
      <About />
      <!-- 评论 -->
      <ProductComment />
      <!-- 操作栏 -->
      <ProductActionBar :loading="loading" @action-click="handleActionClick" />
      <!-- 商品规格 -->
      <Sku
        v-model:show="skuPopupShow"
        :coachId="id"
        :sku="skuConfig"
        :productImage="productImage"
        @change="handleChange"
      />
    </template>
  </Page>
</template>

<script setup>
  import { computed, ref } from 'vue'
  import { useRoute } from 'vue-router'
  import Page from '@/layout/components/Page'
  import ProductImage from './components/ProductImage'
  import ProductInfo from './components/ProductInfo'
  import ProductService from './components/ProductService'
  import ProductCertificate from './components/ProductCertificate'
  import ProductNews from './components/ProductNews'
  import ProductDetails from './components/ProductDetails'
  import ProductComment from './components/ProductComment'
  import About from './components/About'
  import ShoppingNotes from './components/ShoppingNotes'
  import ProductActionBar from './components/ProductActionBar'
  import Sku from './components/Sku'

  import { getOssURL } from '@/common'
  import { reqGetCoachDetailV2 } from '@/api/coach-server'
  import { getTeachingContent } from '@/api/generic-server'

  import { isArray } from '@/utils/is'

  const route = useRoute()
  const details = ref([])

  const id = computed(() => route.params.id)
  const news = ref(null)
  const loading = ref(true)
  const newsLoading = ref(true)
  const skuPopupShow = ref(false)

  const productImage = computed(() => {
    if (!details.value) return ''
    return details.value.mainImage
  })
  const medium = computed(() => {
    let array = []

    details.value?.personalVideos?.forEach((url) => {
      array.push({
        url: getOssURL(url),
        type: 'video',
        poster: getOssURL(details.value.mainImage),
      })
    })

    details.value?.personalImages?.forEach((url) => {
      array.push({ url: getOssURL(url), type: 'image' })
    })

    return array
  })

  const skuConfig = computed(() => {
    if (!details.value) return

    const { courseSettings = [], courseList = [], skuKey } = details.value
    const skuConfig = { specs: [], list: [], selectedKey: [] }

    if (courseSettings) {
      courseSettings.forEach((spec, i) => {
        skuConfig.specs.push({
          k: spec.name,
          k_s: 's' + (i + 1),
          list: spec.attrs,
        })
      })
    }

    if (courseList) {
      skuConfig.list = courseList.map((item) => {
        return {
          ...item,
          keys: item.key.split('-'),
        }
      })
    }

    if (skuKey) {
      skuConfig.selectedKey = skuKey
    }

    return skuConfig
  })

  const certificate = computed(() => {
    if (!details.value) return

    const { playerLevelCredential = null, honorAwards = [], platformAwards = [] } = details.value
    const allCertificate = []

    if (playerLevelCredential) {
      allCertificate.push({
        name: playerLevelCredential?.name,
        url: getOssURL(playerLevelCredential?.url),
        type: 1,
        tagName: '职业认证',
      })
    }

    if (isArray(honorAwards)) {
      honorAwards.forEach((item) => {
        allCertificate.push({
          name: item.name,
          url: getOssURL(item.url),
          type: 2,
          tagName: '荣誉奖项',
        })
      })
    }

    if (isArray(platformAwards)) {
      platformAwards.forEach((item) => {
        allCertificate.push({
          name: item.name,
          url: getOssURL(item.url),
          type: 3,
          tagName: '平台活动奖项',
        })
      })
    }

    return allCertificate
  })

  function getDetails() {
    let params = { coachId: id.value, courseId: '' }
    reqGetCoachDetailV2(params).then((res) => {
      loading.value = false
      details.value = res.data
      console.log(skuConfig)
    })
  }

  function getProductNews() {
    let params = { coachId: id.value, pageNum: 1, pageSize: 3 }
    getTeachingContent(params).then((res) => {
      news.value = res.data.records
      newsLoading.value = false
    })
  }

  function openCustomerServiceChat() {
    window.open('https://work.weixin.qq.com/kfid/kfcdeb0c2a20e51cc36')
  }

  function openSkuPopup() {
    skuPopupShow.value = true
  }

  function handleActionClick(action) {
    if (action === 'service') {
      openCustomerServiceChat()
    } else if (action === 'buy') {
      openSkuPopup()
    }
  }

  function handleChange(value) {
    console.log(value)
  }

  getDetails()
  getProductNews()
  // setTimeout(() => {
  // }, 1000)

  // getDetails()
</script>

<style scoped lang="scss"></style>
