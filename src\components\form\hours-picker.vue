<template>
  <div>
    <van-field
      v-bind="attrs"
      v-model="inputValue"
      :label="label"
      @click="pickerShow = true"
      readonly
      :placeholder="placeholder"
    />
    <hours-picker v-model="time" v-model:show="pickerShow" @confirm="onConfirm" />
  </div>
</template>

<script setup>
  import { ref, useAttrs, watch } from 'vue'
  import HoursPicker from '../working-hours-picker'

  const attrs = useAttrs()

  const inputValue = ref('')

  const time = ref([])

  const pickerShow = ref(false)

  const props = defineProps({
    modelValue: Array,
    label: String,
    placeholder: String,
    format: Function,
  })

  const emit = defineEmits(['update:modelValue'])

  const updateInputValue = (value) => {
    if (Array.isArray(value) && value.length >= 1) {
      inputValue.value = value[0] + ' - ' + value[1]
    }
  }

  const onConfirm = (value) => {
    updateInputValue(value)
    emit('update:modelValue', value)
    pickerShow.value = false
  }

  watch(
    () => props.modelValue,
    (newVal) => {
      if (Array.isArray(newVal)) {
        time.value = newVal
        updateInputValue(newVal)
      }
    },
    {
      immediate: true,
    },
  )
</script>

<style lang="scss" scoped></style>
