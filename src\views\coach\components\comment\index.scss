@import "@/styles/mixins/mixins.scss";

@include Icon("reply", 0.15rem, 0.15rem) {
  margin-right: 0.03rem;
}

.comment-item,
.sub-comment-item {
  display: flex;
  padding: 0.1rem 0.15rem 0 0.15rem;

  .head-portrait {
    margin-right: 0.1rem;
    user-select: none;

    .van-image {
      width: 100%;
      height: 100%;
    }

  }

  .username {
    color: #1a1b1d;
    user-select: none;
  }

  .arrow-username {
    position: relative;
    padding-left: 0.1rem;
    margin-left: 0.04rem;

    &::before{
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      width: 0;
      height: 0;
      transform: translateY(-50%);
      border-left: #CCCCCC 0.06rem solid;
      border-bottom: transparent 0.05rem solid;
      border-top: transparent 0.05rem solid;
    }
  }

  .content-box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }



  .content {
    margin-top: 0.06rem;
    font-size: 0.14rem;
    color: #616568;
    word-break: break-word;
  }

  .content-images {
    margin-top: 0.08rem;
  }

  .content-other {
    display: flex;
    margin-top: 0.06rem;
    margin-bottom: 0.12rem;
    user-select: none;
  }

  .reply-comment {
    margin-left: 0.17rem;
    font-size: 0.12rem;
    color: #a7a7a7;
    display: flex;
    cursor: pointer;
  }

  .content-time {
    font-size: 0.12rem;
    color: #b2b1b7;
  }

  :deep(.content-image) {
    margin-bottom: 0.1rem;

    &:not(:nth-child(3n + 0)) {
      margin-right: 0.1rem;
    }

    img {
      display: inline !important;
    }
  }

  .fetch-more {
    font-size: 0.12rem;
    color: #606266;
    padding: 0.06rem 0.12rem;
    margin-left: 0.38rem;
    margin-bottom: 0.1rem;
    background: #f7f7f7;
    border-radius: 0.04rem;
    cursor: pointer;
  }
}

.comment-item {
 &:not(:last-child) {
   .content-box {
     border-bottom: 1px solid #f3f3f3;
   }
 }
}


.comment-item {
  .sub-comment-item {
    display: flex;
    padding: 0.1rem 0 0 0 !important;

    .head-portrait {
      width: 0.22rem;
      height: 0.22rem;
    }

    .content-box {
      border-bottom: none !important;
    }
  }
}

.sub-comment-item {
  display: flex;
  padding: 0.1rem 0.15rem 0 0.15rem;

  .head-portrait {
    width: 0.32rem;
    height: 0.32rem;
  }

  &:not(:last-child) {
    .content-box {
      border-bottom: 1px solid #f3f3f3;
    }
  }

}
