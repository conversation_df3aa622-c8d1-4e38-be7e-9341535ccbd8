<template>
  <div class="tags">
    <van-tag
      v-for="(tag, index) in list"
      :key="index"
      class="tag"
      type="primary"
      :color="color"
      :text-color="textColor"
    >
      {{ tag }}
    </van-tag>
  </div>
</template>

<script setup>
  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    color: {
      type: String,
      default: '#FFF3E5',
    },
    textColor: {
      type: String,
      default: '#FF6445',
    },
  })
</script>

<style lang="scss" scoped>
  .tag {
    margin-bottom: 0.08rem;
    font-size: 0.13rem;
    padding: 0.04rem 0.08rem;
    border-radius: 0.02rem;

    &:not(:last-child) {
      margin-right: 0.08rem;
    }
  }
</style>
