<template>
  <van-popup style="height: 80vh; padding-bottom: 0.6rem" class="popup" round position="bottom">
    <van-tabs
      v-model:active="active"
      scrollspy
      sticky
      offset-top="20vh"
      title-active-color="#ff7054"
    >
      <van-tab v-for="(level1, index) in data" :key="index" :title="level1.name">
        <div v-for="(level2, index) in level1.childCategoriesVos" :key="index">
          <div class="subtitle">{{ level1.name + '/' + level2.name }}</div>
          <div class="skills">
            <div
              v-for="(skills, index) in level2.childCategoriesVos"
              :key="index"
              class="skills-item omit"
              :class="{ active: !!selected[skills.id] }"
              @click="toggle(level1, level2, skills)"
            >
              {{ skills.name }}
            </div>
          </div>
        </div>
      </van-tab>
    </van-tabs>
    <div class="toolbar">
      <button class="cancel" @click="$emit('update:show', false)">取消</button>
      <button class="confirm" @click="handleConfirm">确认</button>
    </div>
  </van-popup>
</template>

<script setup>
  import { ref, shallowRef, watch, computed } from 'vue'
  import { getCategoriesList } from '@/api/coach-server'
  import { Toast } from 'vant'

  let props = defineProps({
    selectedDefault: {
      type: Object,
      default: () => {},
    },
    // 是否开启单选模式
    radio: {
      type: Boolean,
      default: false,
    },
    maxSelectNum: {
      type: Number,
      default: 5,
    },
  })

  const emit = defineEmits(['confirm', 'update:modelValue'])
  const active = ref(0)
  const data = shallowRef([])
  const selected = ref({})

  const initialize = () => {
    getCategoriesList()
      .then((res) => {
        data.value = res.data
      })
      .catch(() => {})
  }

  const toggle = (level1, level2, skills) => {
    skills.selected = !skills.selected

    if (props.radio) {
      selected.value = {}
    }

    if (skills.selected) {
      if (Object.keys(selected.value).length >= props.maxSelectNum) {
        Toast(`最多选中${props.maxSelectNum}个`)
        return
      }
      selected.value[skills.id] = [level1, level2, skills]
    } else {
      delete selected.value[skills.id]
    }
  }

  const _selected = computed(() => {
    let _selected = []
    for (let key in selected.value) {
      let item = selected.value[key]
      let arr = []
      item.forEach((type) => {
        arr.push({
          id: type.id,
          name: type.name,
        })
      })
      _selected.push(arr)
    }
    return _selected
  })

  const handleConfirm = () => {
    emit('confirm', {
      selected: _selected.value,
    })
  }

  watch(
    () => props.selectedDefault,
    (newVal) => {
      if (!newVal) return
      selected.value = newVal
      handleConfirm()
    },
    {
      immediate: true,
      // deep: true,
    },
  )

  initialize()
</script>

<style lang="scss" scoped>
  .toolbar {
    position: fixed;
    bottom: 0;
    z-index: 3000;
    width: 100%;
    height: 0.52rem;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.15rem;
    color: #969799;
    padding: 0 0.15rem;

    .confirm {
      width: 1.65rem;
      height: 0.4rem;
      background: #ff9b26;
      border-radius: 0.2rem;
      color: #fff;
    }

    .cancel {
      width: 1.65rem;
      height: 0.4rem;
      background: #fff;
      border-radius: 0.2rem;
      border: 1px solid #ff9b26;
      color: #ff6445;
    }
  }
  :deep(.van-tabs__wrap),
  :deep(.van-tabs) {
    border-radius: 16px 16px 0 0;
  }

  :deep(.van-tabs__content) {
    padding: 0 0.15rem;
  }

  :deep(.van-tabs__line) {
    height: 2px;
    background: #ff7054;
  }

  .subtitle {
    margin: 0.1rem 0;
    font-size: 0.14rem;
    color: #a8a8a8;
  }

  .skills {
    .skills-item {
      width: 0.8rem;
      height: 0.35rem;
      line-height: 0.35rem;
      margin-right: 0.08rem;
      margin-bottom: 0.08rem;
      padding: 0 0.05rem;
      text-align: center;
      display: inline-block;
      font-size: 0.12rem;
      background: #f3f3f3;
      border-radius: 0.04rem;

      &:nth-child(4n + 0) {
        margin-right: 0;
      }

      //&:nth-last-child(-n + 4) {
      //  margin-bottom: 0;
      //}
    }

    .active {
      color: #ff684a;
      background: #ffefec;
    }
  }
</style>
