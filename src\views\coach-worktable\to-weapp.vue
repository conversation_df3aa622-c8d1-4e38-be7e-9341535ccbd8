<template>
  <page title="打开小程序" :loading="loading">
    <template #page>
      <p class="title">点击以下按钮打开“爱教练”</p>
      <van-button type="success" block>打开小程序</van-button>
      <div class="btn-box">
        <wx-open-launch-weapp
          id="launch-btn"
          appid="wx2303ec2a61fff57b"
          :path="path"
          :env-version="miniprogramEnv"
        >
          <component
            :is="'script'"
            type="text/wxtag-template"
            class="btn"
            style="display: block; height: 0.5rem; width: 3.47rem"
          >
            <div
              class="btn-content"
              style="
                color: #ffffff;
                padding: 16px 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 30px;
                height: 0.5rem;
                width: 3.47rem;
              "
            ></div>
          </component>
        </wx-open-launch-weapp>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'
  import authWeChatSDK from '@/utils/weChat/authWeChatSDK'
  import { isWeChat } from '@/utils'
  import { miniprogramEnv } from '@/config'

  const route = useRoute()
  const loading = ref(true)
  const path = route.query.path

  const getAuth = () => {
    if (!isWeChat()) {
      loading.value = false
      return
    }

    authWeChatSDK()
      .then(() => {
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }
  setTimeout(() => {
    getAuth()
  }, 30)
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';
  .page {
    padding: 0.1rem 0.2rem;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  .title {
    margin-top: 30vh;
    margin-bottom: 10vh;
    text-align: center;
  }
  .btn-box {
    position: fixed;
    top: 41vh;
    width: 3.47rem;
    height: 0.66rem;
    background-size: 100% 100%;
    z-index: 999;
    #launch-btn {
      margin: 0.21rem auto 0;
      display: block;
      height: 0.5rem;
      line-height: 0.4rem;
      width: 3.47rem;
      background: transparent;
      font-size: 0.16rem;
      font-weight: 600;
      color: #ffffff;
      z-index: 100;
    }
    .btn,
    .btn-content {
      display: block;
      width: 3.47rem;
      height: 0.5rem;
    }
    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .list {
      display: flex;
      padding-top: 0.21rem;
      .list-item {
        width: 33.33%;
        padding: 0 0.17rem;
        display: flex;
        align-items: center;
        flex-direction: column;
        position: relative;
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          right: 0;
          top: 0.48rem;
          width: 0.01rem;
          height: 0.28rem;
          background: #d6b483;
          background-image: initial;
          background-position-x: initial;
          background-position-y: initial;
          background-size: initial;
          background-attachment: initial;
          background-origin: initial;
          background-clip: initial;
          background-color: rgb(214, 180, 131);
        }
        .list-item-icon {
          width: 0.4rem;
          height: 0.4rem;
        }
        .list-item-title {
          margin-top: 0.07rem;
          font-size: 0.14rem;
          color: #865500;
          font-weight: 600;
        }
        .list-item-content {
          margin-top: 0.04rem;
          font-size: 0.11rem;
          color: #a16f29;
          text-align: center;
        }
      }
    }
    .tips {
      text-align: center;
      margin-top: 0.08rem;
      font-size: 0.14rem;
      color: #9e6d1e;
      line-height: 0.2rem;
    }
  }
  .product-action-bar {
    position: fixed;
    bottom: 0;
    width: 3.75rem;
    height: 0.5rem;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    box-sizing: content-box;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: #fff;
    box-shadow: 0px -1px 0.04rem 0x rgba(0, 0, 0, 0.06);
  }

  .product-action-bar--skeleton {
    width: 100%;
    height: 100%;
  }
</style>
