<template>
  <!--  活动进行中的排行榜 -->
  <div class="ActProgress">
    <Coach v-if="actInfo.helpCoachInfo" />
    <!--    <ActPack v-if="isLoginState && !actInfo.helpCoachInfo" />-->
    <RankingList />
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useParent } from '@vant/use'
  import RankingList from './RankingList'
  // import ActPack from "./ActPack";
  import Coach from './Coach'
  // import { isLogin } from "@/common";

  const { parent } = useParent('321ACT')
  const actInfo = ref(parent.actInfo.value)
  // const isLoginState = ref(isLogin());
</script>

<style lang="scss" scoped></style>
