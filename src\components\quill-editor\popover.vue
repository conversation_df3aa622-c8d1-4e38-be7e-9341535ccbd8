<template>
  <div class="i-popover">
    <div ref="contentRef" class="popover-content" :class="{ hide: !show }">
      <slot></slot>
    </div>
    <span ref="referenceRef" @click="show = !show" class="i-popover__wrapper">
      <slot name="reference"></slot>
    </span>
  </div>
</template>

<script setup>
  import { ref, onUnmounted } from 'vue'
  const show = ref(false)
  const referenceRef = ref('')
  const contentRef = ref('')

  // TODO: 多次使用改组件则会绑定多个 document事件，后续优化成单例模式封装
  function handleDocumentClick(e) {
    if (
      !show.value ||
      !contentRef.value ||
      contentRef.value.contains(e.target) ||
      (referenceRef.value && referenceRef.value.contains(e.target))
    )
      return
    show.value = false
  }

  document.addEventListener('click', handleDocumentClick, false)

  onUnmounted(() => {
    document.removeEventListener('click', handleDocumentClick)
  })
</script>

<style lang="scss" scoped>
  .i-popover {
    width: 32px;
    position: relative;
    float: left;

    :deep(.ql-picker) {
      float: left !important;
    }

    .hide {
      display: none;
    }

    //:deep(.popover-content) {
    //  position: absolute;
    //  top: 38px;
    //  left: 50%;
    //  z-index: 30;
    //  transform: translateX(-50%);
    //  width: max-content;
    //  border: 1px solid #ccc;
    //  border-radius: 4px;
    //  padding: 4px;
    //  background-color: #fff;
    //  filter: drop-shadow(0px 2px 4px rgba(204, 204, 204, 0.9));
    //  user-select: none;
    //
    //  &::before {
    //    content: "";
    //    width: 0;
    //    height: 0;
    //    border-style: solid;
    //    border-width: 0 10px 10px 10px;
    //    border-color: transparent transparent #fff transparent;
    //    position: absolute;
    //    top: -10px;
    //    left: 0;
    //    right: 0;
    //    margin: auto;
    //    z-index: 2;
    //  }
    //
    //  &::after {
    //    content: "";
    //    width: 0;
    //    height: 0;
    //    border-style: solid;
    //    border-width: 0 10px 10px 10px;
    //    border-color: transparent transparent rgb(204, 204, 204) transparent;
    //    position: absolute;
    //    top: -11px;
    //    left: 0;
    //    right: 0;
    //    margin: auto;
    //    z-index: 1;
    //  }
    //
    //  .ql-picker,
    //  button {
    //    float: left !important;
    //    margin-right: 1px !important;
    //  }
    //}

    :deep(.popover-content) {
      position: absolute;
      top: -48px;
      left: 50%;
      transform: translateX(-50%);
      width: max-content;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 4px;
      background-color: #fff;
      //filter: drop-shadow(0px 2px 4px rgba(204, 204, 204, 0.9));
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 10%);

      &::before {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 10px 0 10px;
        border-color: #fff transparent transparent transparent;
        position: absolute;
        bottom: -10px;
        left: 0;
        right: 0;
        margin: auto;
        z-index: 2;
      }
      &::after {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 10px 0 10px;
        border-color: rgb(204, 204, 204) transparent transparent transparent;
        position: absolute;
        bottom: -11px;
        left: 0;
        right: 0;
        margin: auto;
        z-index: 1;
      }

      .ql-picker,
      button {
        float: left !important;
        margin-right: 1px !important;
      }
    }
  }
</style>
