<template>
  <div class="platform-feature">
    <div v-for="(item, index) in platformFeature" :key="index" class="platform-feature-item">
      <img class="platform-feature-icon" :src="item.icon" alt="" />
      <div class="platform-feature-content">
        <div class="f10 platform-feature-text">{{ item.label }}</div>
        <div class="f10 platform-feature-text">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { platformFeature } from '../home-config'
</script>

<style lang="scss" scoped>
  .platform-feature {
    height: 0.4rem;
    background: #f1f1f2;
    padding: 0 0.14rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none;

    .platform-feature-item {
      height: 100%;
      display: flex;
      align-items: center;

      .platform-feature-icon {
        width: 0.18rem;
        height: 0.19rem;
        margin-right: 0.05rem;
      }

      .platform-feature-content {
        padding: 0.08rem 0;
      }

      .platform-feature-text {
        line-height: 0.12rem;
        transform-origin: left;
        color: #999999;
      }
    }
  }
</style>
