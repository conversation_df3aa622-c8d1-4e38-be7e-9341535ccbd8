<template>
  <div>
    <div class="error">
      <i class="icon icon-error-tip" />
      <span>{{ tipsTxt }}</span>
    </div>
  </div>
</template>

<script setup>
  import { watch, ref, onMounted } from 'vue'
  const props = defineProps({
    tipTxt: {
      type: String,
      default: '',
    },
  })
  const tipsTxt = ref('')
  watch(
    () => props.tipTxt,
    (newVal) => {
      tipsTxt.value = newVal
    },
  )
  onMounted(() => {
    tipsTxt.value = props.tipTxt
  })
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';

  @include Icon('error-tip', 0.13rem, 0.13rem);
  .error {
    display: flex;
    align-items: center;
    padding-top: 0.02rem;
    span {
      margin-left: 0.05rem;
      font-size: 0.12rem;
      color: #ff6445;
    }
  }
</style>
