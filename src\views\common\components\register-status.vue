<template>
  <div>
    <div v-if="isShowTop" class="register-status">
      <div :class="'icon-' + icon" />
      <div class="title">{{ title }}</div>
      <slot name="desc"></slot>
      <button v-if="buttonText" class="back-home-btn" @click="$emit('button-click')">
        {{ buttonText }}
        <!-- <span v-if="showCoundDown">（{{ seconds }}s）</span> -->
      </button>
    </div>
    <div class="scan-code" v-if="statusType == 'common'">
      <p class="text">扫码关注 <strong>「爱教练」</strong> 微信公众号</p>
      <p class="text">随时了解爱教练</p>
      <div class="qrcode">
        <img src="../../../assets/images/wx.jpg" alt="" />
      </div>
    </div>
    <div class="scan-code" v-else-if="statusType == 'coach'">
      <p class="text">请扫码添加客服企微,助您快速完成审核</p>
      <p class="text">并及时为您推荐学员</p>
      <div class="qrcode">
        <img src="../../../assets/images/cs-qrcode.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
  defineProps({
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    //教练入驻文案展示区分
    statusType: {
      type: String,
      default: 'common',
    },
    buttonText: {
      type: String,
      default: '',
    },
    showCoundDown: {
      type: Boolean,
      default: false,
    },
    isShowTop: {
      type: Boolean,
      default: true,
    },
  })
  defineEmits(['buttonClick'])
  // const seconds = ref(10);
  // onMounted(() => {
  //   console.log(props.showCoundDown);
  //   if (props.showCoundDown) {
  //     const timer = setInterval(() => {
  //       seconds.value--;
  //       if (seconds.value === 0) {
  //         clearInterval(timer);
  //         emit("button-click");
  //       }
  //     }, 1000);
  //   }
  // });
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';
  @include Icon('success', 0.48rem, 0.48rem);
  @include Icon('error', 0.48rem, 0.48rem);

  .register-status {
    text-align: center;
    padding: 0.5rem 0.4rem;
    background-color: #fff;

    .title {
      margin-top: 0.15rem;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .back-home-btn {
      width: 2rem;
      height: 0.4rem;
      margin-top: 0.36rem;
      background: #ff9b26;
      color: #fff;
      font-size: 0.17rem;
      font-weight: 600;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.2rem;
    }
  }

  .scan-code {
    height: 3.62rem;
    margin-top: 0.05rem;
    padding-top: 0.4rem;
    background-color: #fff;
    text-align: center;

    .text {
      line-height: 0.2rem;
      font-size: 0.14rem;
      color: #1f1f1f;
    }

    .qrcode {
      width: 1.33rem;
      height: 1.33rem;
      display: inline-block;
      margin-top: 0.23rem;
      background: #ffffff;
      border: 1px solid #ececec;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
