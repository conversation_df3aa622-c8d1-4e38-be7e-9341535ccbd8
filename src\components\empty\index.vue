<template>
  <div class="i-empty" :style="{ 'padding-top': top }">
    <div class="i-empty-image">
      <slot name="image">
        <img src="../../assets/images/empty.png" alt="empty" />
      </slot>
    </div>
    <p v-if="description" class="i-empty__description" v-html="description"></p>
  </div>
</template>

<script>
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'Empty',
    props: {
      description: {
        type: String,
        default: '',
      },
      top: {
        type: String,
        default: '0.32rem',
      },
    },
  })
</script>

<style lang="scss" scoped>
  .i-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.32rem 0;

    .i-empty-image {
      width: 0.98rem;
      height: 1rem;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .i-empty__description {
      margin-top: 0.1rem;
      font-size: 0.12rem;
      color: #b2b1b7;
      text-align: center;
    }
  }
</style>
