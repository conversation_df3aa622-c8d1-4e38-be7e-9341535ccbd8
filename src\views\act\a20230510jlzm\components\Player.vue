<template>
  <div class="video-player" :style="{ width, height }" @click="play">
    <video ref="video" :src="src" playsinline webkit-playsinline x5-playsinline />

    <div v-if="poster && posterShow" class="player-poster">
      <img :src="poster" alt="" />
    </div>

    <div v-show="loading" class="player-loading">
      <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
        <path d="M100,50A50,50,0,1,1,50,0"></path>
      </svg>
    </div>

    <div class="player-controls">
      <div v-show="!isPlay" class="player-play" @click.stop="play"></div>

      <!-- 进度条 -->
      <div class="progress-bar" v-show="controlsShow">
        <div ref="progressOuter" class="outer">
          <div class="played" :style="{ width: progress + '%' }">
            <div ref="progressBtn" class="progress-btn"></div>
          </div>
        </div>
      </div>

      <div class="player-time" v-show="controlsShow">
        <span>{{ currentTime }}</span>
        <em>{{ duration }}</em>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue'

  defineProps({
    src: {
      type: String,
      default: null,
    },
    width: {
      type: String,
      default: '',
    },
    height: {
      type: String,
      default: '1.82rem',
    },
    poster: {
      type: String,
      default: null,
    },
  })

  const emit = defineEmits(['play', 'pause'])

  const video = ref(null)
  const progress = ref(0)
  const progressOuter = ref(null)
  const progressBtn = ref(null)
  const duration = ref('00:00')
  const currentTime = ref('00:00')
  const isPlay = ref(false)
  const controlsShow = ref(true)
  const loading = ref(false)
  const posterShow = ref(true)
  let hideControlsTimeoutId

  function formatVideoDuration(duration) {
    let hours = Math.floor(duration / 3600)
    let minutes = Math.floor((duration % 3600) / 60)
    let seconds = Math.floor(duration % 60)

    let timeString = ''

    if (hours > 0) {
      timeString = ('0' + hours).slice(-2) + ':'
    }

    timeString += ('0' + minutes).slice(-2) + ':' + ('0' + seconds).slice(-2)

    return timeString
  }

  function hideControls() {
    controlsShow.value = false
  }

  function showControls() {
    controlsShow.value = true
    // 清除未执行的定时器
    if (hideControlsTimeoutId) {
      clearTimeout(hideControlsTimeoutId)
      hideControlsTimeoutId = null
    }
    // 启动新的定时器
    hideControlsTimeoutId = setTimeout(hideControls, 2000)
  }

  function initPlayer() {
    // 设置格式化后的视频时长字符串
    video.value.addEventListener('loadedmetadata', function () {
      duration.value = formatVideoDuration(video.value.duration || 0)
    })

    // 更新进度条和小圆点的位置
    video.value.addEventListener('timeupdate', function () {
      progress.value = Math.floor((video.value.currentTime / video.value.duration) * 100)
      currentTime.value = formatVideoDuration(video.value.currentTime)
    })

    video.value.addEventListener('play', function () {
      posterShow.value = false
      // // 启动定时器
      hideControlsTimeoutId = setTimeout(hideControls, 3000)
      emit('play')
    })

    video.value.addEventListener('pause', function () {
      // 清除未执行的定时器
      if (hideControlsTimeoutId) {
        clearTimeout(hideControlsTimeoutId)
        hideControlsTimeoutId = null
      }
      controlsShow.value = true

      emit('pause')
    })

    video.value.addEventListener('waiting', () => {
      loading.value = true
    })

    video.value.addEventListener('canplay', () => {
      loading.value = false
    })

    video.value.addEventListener('click', () => {
      showControls()
    })

    video.value.addEventListener('mousemove', () => {
      showControls()
    })

    video.value.addEventListener('touchstart', () => {
      showControls()
    })

    video.value.addEventListener('touchend', () => {
      showControls()
    })

    initProgress()
  }

  function initProgress() {
    function seek(e) {
      // 清除未执行的定时器
      if (hideControlsTimeoutId) {
        clearTimeout(hideControlsTimeoutId)
        hideControlsTimeoutId = null
      }

      const progressWidth = progressOuter.value.offsetWidth
      const progressLeft = progressOuter.value.getBoundingClientRect().left
      const moveX = e.offsetX || e.clientX || e.pageX || e.touches[0].clientX - progressLeft
      const duration = video.value.duration

      video.value.currentTime = (moveX / progressWidth) * duration
      progress.value = Math.floor((video.value.currentTime / video.value.duration) * 100)
      e.stopPropagation()
    }

    progressOuter.value.addEventListener('click', seek)
    progressOuter.value.addEventListener('touchstart', () => {})
    progressOuter.value.addEventListener('touchmove', seek)
    progressOuter.value.addEventListener('touchend', () => {})
  }

  function play() {
    if (video.value.paused) {
      video.value.play()
      isPlay.value = true
    } else {
      video.value.pause()
      isPlay.value = false
    }
  }

  onMounted(() => {
    initPlayer()
  })

  defineExpose({
    play,
  })
</script>

<style lang="scss" scoped>
  .video-player {
    width: 100%;
    position: relative;
    cursor: pointer;
    background: #000;

    video {
      width: 100%;
      height: 100%;
      outline: none;
      object-fit: cover;
    }

    .player-loading {
      width: 100px;
      height: 100px;
      overflow: hidden;
      transform: scale(0.7);
      position: absolute;
      left: 50%;
      top: 50%;
      margin: -50px auto auto -50px;

      svg {
        border-radius: 50%;
        animation: loadingRotate 1s linear infinite;

        path {
          stroke: #ddd;
          stroke-dasharray: 236;
          animation: loadingDashOffset 2s linear infinite;
          animation-direction: alternate-reverse;
          fill: none;
          stroke-width: 12px;
        }
      }

      @keyframes loadingRotate {
        0% {
          transform: rotate(0);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @keyframes loadingDashOffset {
        0% {
          stroke-dashoffset: 236;
        }
        100% {
          stroke-dashoffset: 0;
        }
      }
    }

    .player-poster {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      //z-index: 100;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .player-play {
      width: 0.3rem;
      height: 0.27rem;
      background: url('../images/video-play.png') no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 0.06rem;
      bottom: 0.24rem;
      cursor: pointer;
      z-index: 10;
    }

    .player-time {
      position: absolute;
      bottom: 0.22rem;
      right: 0;
      padding: 0 0.06rem;
      font-size: 0.12rem;

      span {
        color: #ffffff;

        &::after {
          content: '/';
          display: inline-block;
          padding: 0 3px;
        }
      }

      em {
        color: hsla(0, 0%, 100%, 0.5);
      }
    }

    .progress-bar {
      width: 100%;
      position: absolute;
      bottom: 0.08rem;
      padding: 0 0.06rem;

      .outer {
        width: 100%;
        height: 0.04rem;
        background: hsla(0, 0%, 100%, 0.3);
        padding: 0 0.16rem;
        cursor: pointer; /* 让进度条显示手型指针，以表明它可以被拖动 */
        border-radius: 0.02rem;
        position: relative;

        .played {
          height: 100%;
          background-color: #fff;
          position: absolute;
          left: 0;
          top: 0;
        }

        .progress-btn {
          position: absolute;
          left: 100%;
          top: -0.06rem;
          width: 0.14rem;
          height: 0.14rem;
          border-radius: 0.06rem;
          background: #fff;
          box-shadow: 0 0 0.02rem 0 rgba(0, 0, 0, 0.26);
        }
      }
    }
  }
</style>
