
@mixin Icon($name, $width, $height) {
  $class-name: 'icon-' + $name;
  $iconUrl: '~@/assets/images/icon/icon-#{$name}.png';

  .#{$class-name} {
    width: $width;
    height: $height;
    display: inline-block;
    background: url($iconUrl) no-repeat;
    background-size: 100% 100%;
    @content;
  }
}

@mixin Button($className, $width) {
  .#{$className} {
    width: $width;
    background-color: var(--i-primary);
    border-radius: 0.22rem;
    padding: 0.10rem 0;
    font-size: 0.17rem;
    font-weight: 600;
    letter-spacing: 0.01rem;
    color: #FEFFFF;
    @content;
  }
}

@mixin TextEllipsis($rowNum) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $rowNum;
  word-break: break-all;
}