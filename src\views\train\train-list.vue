<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="page-content">
        <van-sticky offset-top="0.44rem">
          <van-dropdown-menu class="dropdown-menu" active-color="#ff6445" duration="0">
            <van-dropdown-item
              ref="classifyRef"
              :lazy-render="false"
              :title="classifyMenuTitle"
              :title-class="classifyTitleClass"
              @open="handleClassifyMenuOpen"
              @close="handleClassifyMenuClose"
            >
              <classify-cascader v-model="classifyValue" @change="onClassifyChange" />
            </van-dropdown-item>
            <van-dropdown-item
              v-model="queryParams.county"
              :title-class="areaTitleClass"
              :options="areaOptions"
              @change="onAreaChange"
            />
          </van-dropdown-menu>
        </van-sticky>

        <div class="list-warp">
          <van-list
            class="train-box flex"
            v-model:loading="loading"
            :immediate-check="false"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
          >
            <div
              class="train-item"
              v-for="item in articleList"
              :key="item"
              @click="onViewDetails(item)"
            >
              <h3>{{ item.title }}</h3>
              <div class="intro">{{ item.description }}</div>
              <div class="train-foot flex">
                <div class="foot-l">
                  <span>{{ item.thirdlyCategoriesName }}</span>
                  <span v-if="item.cityName">{{ item.cityName }}{{ item.countyName }}</span>
                </div>
                <div class="foot-r">
                  <span>培训时间：{{ item.createTime.split(' ')[0] }}</span>
                  <span>{{ item.readCount }}浏览</span>
                </div>
              </div>
            </div>
          </van-list>
          <empty class="empty" v-show="emptyShow" description="暂无内容" top="1.96rem" />
        </div>
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'trainList' }
</script>

<script setup>
  import { ref, computed } from 'vue'
  import { getTrainsList } from '@/api/generic-server'
  import { useRouter, onBeforeRouteLeave } from 'vue-router'
  import { reqAreaOptions } from '@/api/common'
  import { localProxyStorage } from '@/utils/storage'
  import ClassifyCascader from '@/views/search/components/ClassifyCascader'
  import Empty from '@/components/empty'
  import useKeepAliveStore from '@/store/keepAlive'

  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const classifyMenuTitle = ref('分类')
  const classifyRef = ref(null)
  const classifyValue = ref([])
  let beforeClassifyValue = []
  const selectedClassifyItems = ref([])
  const articleList = ref([])
  const areaOptions = ref([{ text: '不限地区', value: '' }])
  const allCity = ref([])
  const emptyShow = ref(false)
  const selectedCity = localProxyStorage.selectedGeolocation || { adcode: '440100', city: '广州市' }

  const loading = ref(false)
  const finished = ref(false)
  const queryParams = ref({
    pageNum: 0,
    pageSize: 10,
    city: selectedCity.adcode,
    county: '',
    countyList: [],
    townsList: [],
    firstCategoriesId: '',
    secondCategoriesId: '',
    thirdlyCategoriesId: '',
  })

  const finishedText = computed(() => (articleList.value.length > 0 ? '-没有更多了-' : ''))

  const onCriteriaQuery = () => {
    loading.value = false
    finished.value = false
    articleList.value.length = 0
    queryParams.value.pageNum = 1

    getArticleList()
  }

  const classifyTitleClass = computed(() => {
    return classifyValue.value.length !== 0 ? 'dropdown-title-active' : ''
  })

  const areaTitleClass = computed(() => {
    return queryParams.value.county !== '' ? 'dropdown-title-active' : ''
  })

  const onClassifyChange = (value) => {
    selectedClassifyItems.value = value.selected

    queryParams.value.firstCategoriesId = classifyValue.value[0] || ''
    queryParams.value.secondCategoriesId = classifyValue.value[1] || ''
    queryParams.value.thirdlyCategoriesId = classifyValue.value[2] || ''

    if (value.selected.length === 3) {
      classifyRef.value?.toggle(false)
    }
  }

  const handleClassifyMenuOpen = () => {
    beforeClassifyValue = classifyValue.value
  }

  const handleClassifyMenuClose = () => {
    if (beforeClassifyValue.toString() === classifyValue.value.toString()) return

    if (selectedClassifyItems.value.length === 0) {
      classifyMenuTitle.value = '分类'
    } else {
      let lastItem = selectedClassifyItems.value[selectedClassifyItems.value.length - 1]
      classifyMenuTitle.value = lastItem.label
    }

    onCriteriaQuery()
  }

  const getArticleList = () => {
    let params = JSON.parse(JSON.stringify(queryParams.value))

    if (params.city === '000000') {
      params.city = params.county
      params.county = ''
      params.countyList.length = 0
    }

    getTrainsList(params).then((res) => {
      const { data } = res
      articleList.value = articleList.value.concat(data.records)
      emptyShow.value = articleList.value.length === 0

      loading.value = false
      if (data.records.length === 0 || data.records.length < queryParams.value.pageSize) {
        finished.value = true
      }
    })
  }

  const onAreaChange = () => {
    queryParams.value.countyList.length = 0

    if (queryParams.value.county) {
      queryParams.value.countyList = [queryParams.value.county]
    }

    onCriteriaQuery()
  }

  const getAreaOptions = () => {
    reqAreaOptions().then((res) => {
      const { data } = res
      data.forEach((item) => {
        allCity.value = allCity.value.concat(item.children)
      })

      if (selectedCity.adcode === '000000') {
        let allCityOptions = []
        allCity.value.forEach((city) => {
          allCityOptions.push({ text: city.adName, value: city.adCode })
        })

        areaOptions.value = areaOptions.value.concat(allCityOptions)
      } else {
        areaOptions.value = areaOptions.value.concat(getCityChildren(selectedCity.adcode))
      }
    })
  }

  const getCityChildren = (adcode) => {
    let arr = []
    let options = allCity.value.find((city) => city.adCode === adcode).children

    options.forEach((item) => {
      arr.push({ text: item.adName, value: item.adCode })
    })

    return arr
  }

  const onLoad = () => {
    queryParams.value.pageNum += 1
    getArticleList()
  }

  const onViewDetails = (article) => router.push('/train-details/' + article.trainsId)

  const initialize = () => {
    getAreaOptions()
    onLoad()
  }

  onBeforeRouteLeave((to) => {
    let pages = ['trainDetails']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('trainList')
    }
  })

  initialize()
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';

  @include Icon('selected', 0.15rem, 0.15rem);
  // 公共样式
  $color1: #1f1f1fff;

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .train-box {
    flex-wrap: wrap;
  }

  .train-item {
    padding: 0.15rem 0.12rem;
    width: 3.75rem;
    flex-shrink: 0;
    background-color: #fff;
    border-bottom: 0.01rem solid #f7f7f7;

    h3 {
      font-size: 0.15rem;
      color: $color1;
    }

    .intro {
      padding-top: 0.04rem;
      margin-bottom: 0.12rem;
      max-height: 0.37rem;
      font-size: 0.13rem;
      color: #616568;
      line-height: 0.17rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }

    .train-foot {
      .foot-l span {
        padding: 0.01rem 0.02rem;
        border: 0.01rem solid rgba($color: #979797, $alpha: 0.8);
        border-radius: 0.02rem;
        font-size: 0.1rem;
        color: #979797;
        margin-right: 0.04rem;
      }

      .foot-r span {
        font-size: 0.1rem;
        color: #979797;

        &:first-child {
          margin-right: 0.1rem;
        }
      }
    }
  }

  :deep(.van-list__loading) {
    margin: 0 auto;
  }

  :deep(.van-dropdown-menu__bar) {
    //box-shadow: none;
    //height: 0.5rem;
  }

  :deep(.van-list__finished-text) {
    margin: 0 auto;
  }
</style>
