<template>
  <page navigationBarType="none">
    <template #page>
      <div :class="{ 'is-inviter': isInviter }">
        <header-search />
        <banner />
        <platform-feature />
        <quick-nav />
        <recommend />
        <latest-release />
        <module-entry />
        <suspended-panel />
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'home' }
</script>

<script setup>
  import { ref, onMounted } from 'vue'
  import { onBeforeRouteLeave } from 'vue-router'
  import { ossURL, baseURL } from '@/config'
  import setWxShare from '@/utils/weChat/share'

  import HeaderSearch from './components/HeaderSearch'
  import Banner from './components/Banner'
  import PlatformFeature from './components/PlatformFeature'
  import QuickNav from './components/QuickNav'
  import Recommend from './components/Recommend'
  import LatestRelease from './components/LatestRelease'
  import ModuleEntry from './components/ModuleEntry'
  import SuspendedPanel from './components/SuspendedPanel'
  import useKeepAliveStore from '@/store/keepAlive'
  const keepAliveStore = useKeepAliveStore()
  const isInviter = ref(false)

  onMounted(() => {
    setTimeout(() => {
      let isState = document.querySelector('.inviter')
      isInviter.value = !!isState
    }, 500)
  })

  onBeforeRouteLeave((to) => {
    let pages = [
      'coachDetails',
      'shopDetails',
      'newsDetails',
      'newsList',
      'videoDetails',
      'videoList',
      'recruitDetails',
      'recruitList',
      'trainDetails',
      'trainList',
    ]
    if (!pages.includes(to.name)) {
      // 卸载缓存
      keepAliveStore.removeKeepAlive('home')
    }
  })

  setWxShare({
    title: '爱教练——全国体育私教预约平台',
    desc: '找教练，就到爱教练',
    link: baseURL + '/',
    imgUrl: ossURL + '/h5-assets/logo.png',
  })
</script>

<style lang="scss" scoped>
  :deep(.inviter) {
    width: initial;
    position: fixed;
    top: 0;
    left: var(--window-left);
    right: var(--window-right);
    z-index: 10;
  }

  .is-inviter {
    padding-top: 0.58rem;

    :deep(.header-search) {
      top: 0.58rem;
    }
  }
</style>
