## FieldDatePicker 选择时间组件
利用 将 vant Field DatetimePicker Popup 组装的时间选择器

### 使用
```vue
<template>
  <FieldDatePicker v-model="time" label="选择日期" type="date" />
</template>
<script setup>
  import FieldDatePicker from '@/components/field-date-picker'
  const time = new Date(2022, 3, 24);
</script>
```

### Attributes
| 参数         | 说明       | 类型           | 可选值 | 默认值                 |
|------------|----------|--------------|-----|---------------------|
| modelValue | 尺寸       | String, Date |     | —                   |
| format     | 输入框的日期格式 | String       |     | YYYY/MM/DD HH:mm:ss |