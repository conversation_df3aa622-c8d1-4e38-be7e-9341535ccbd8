<template>
  <div
    class="step"
    :class="{ 'is-finish': isFinish, 'is-process': isProcess, 'is-error': isError }"
    ref="step"
  >
    <div class="step__circle"></div>
    <div class="step__line"></div>
    <div class="step__title">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, getCurrentInstance, watch } from 'vue'

  const instance = getCurrentInstance()
  const step = ref(null)
  const isFinish = ref(false)
  const isProcess = ref(false)
  const isError = ref(false)

  onMounted(() => {
    let parent = instance.proxy.$parent
    let children = Array.from(parent.$el.children)
    let index = children.indexOf(step.value)

    const setStep = (value) => {
      isFinish.value = false
      isProcess.value = false
      isError.value = false

      if (index < value) {
        isFinish.value = true
        return
      }

      if (index === value && parent.$props.status === 'error') {
        isError.value = true
      } else if (index === value && parent.$props.status === 'finish') {
        isFinish.value = true
      } else if (index === value) {
        isProcess.value = true
      }
    }

    watch(() => parent.$props.active, setStep, { immediate: true })
  })
</script>

<style lang="scss" scoped>
  .step {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    &:last-child {
      .step__line {
        display: none;
      }
    }
  }

  .step__circle {
    width: 0.07rem;
    height: 0.07rem;
    background: #b9b4b4;
    border-radius: 50%;
    position: relative;
    z-index: 2;
    transition: 0.2s;
  }

  .step__line {
    position: absolute;
    top: 0.03rem;
    left: 50%;
    right: -50%;
    width: 100%;
    height: 1px;
    background: #edebeb;
    z-index: 1;
    transition: 0.2s;
  }

  .step__title {
    font-size: 0.12rem;
    position: absolute;
    top: 0.24rem;
    color: #170606;
  }

  .is-finish {
    .step__circle {
      width: 0.16rem;
      height: 0.16rem;
      background: url('../../assets/images/icon/icon-check.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      top: -0.04rem;
    }
    .step__line {
      background: #fad3d2;
    }
  }

  .is-error {
    .step__circle {
      width: 0.16rem;
      height: 0.16rem;
      background: url('../../assets/images/icon/icon-erorr-1.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      top: -0.04rem;
    }
  }

  .is-process {
    .step__circle {
      width: 0.16rem;
      height: 0.16rem;
      background: #ffdcb2;
      position: relative;
      top: -0.04rem;

      &:after {
        content: '';
        width: 0.08rem;
        height: 0.08rem;
        border-radius: 50%;
        background-color: #ff9b26;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .step__title {
      color: #ff9b26;
    }
  }
</style>
