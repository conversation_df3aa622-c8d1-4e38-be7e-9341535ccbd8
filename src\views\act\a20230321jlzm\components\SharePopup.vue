<template>
  <Teleport to="body">
    <div class="SharePopup">
      <van-popup v-model:show="sharePopupShow" @close="onClose">
        <div class="popup-wrap">
          <div class="close" @click="updateShow(false)" />
          <div class="title">
            <i class="star" />
            <span>分享集赞</span>
            <i class="star" />
          </div>

          <div class="content">
            <div class="tip">分享给更多好友点赞，排名上升更快喔～</div>
            <div class="action">
              <div class="action-plate" @click="openSharePoster">
                <div class="icon-poster">
                  <img src="../images/icon-poster.png" alt="" />
                </div>
                <span>领取海报</span>
              </div>
              <div class="action-plate" @click="showWeChatShare">
                <div class="icon-wx">
                  <img src="../images/icon-wx.png" />
                </div>
                <span>分享朋友圈</span>
              </div>
            </div>
          </div>
        </div>
      </van-popup>

      <WeChatShareTip v-model="weChatShareTipShow" />
      <SharePoster v-model="sharePosterShow" />
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import WeChatShareTip from './WeChatShareTip'
  import SharePoster from './SharePoster'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: true,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  watch(
    () => props.modelValue,
    (val) => (sharePopupShow.value = val),
  )

  const sharePopupShow = ref(props.modelValue || false)
  const weChatShareTipShow = ref(false)
  const sharePosterShow = ref(false)

  const updateShow = (state) => emit('update:modelValue', state)

  const onClose = () => {
    updateShow(false)
  }

  const showWeChatShare = () => {
    weChatShareTipShow.value = true
    updateShow(false)
  }

  const openSharePoster = () => {
    sharePosterShow.value = true
    updateShow(false)
  }
</script>

<style lang="scss" scoped>
  .popup-wrap {
    width: 3.15rem;
    background: #ffffff;
    border-radius: 0.1rem;
    border: 2px solid #376cfe;
    padding-bottom: 0.25rem;
    position: relative;
  }

  .close {
    position: absolute;
    right: 0.12rem;
    top: 0.12rem;
    width: 0.12rem;
    height: 0.12rem;
    background: url('../images/popup-close.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  .title {
    margin-top: 0.25rem;
    margin-bottom: 0.1rem;
    text-align: center;

    span {
      margin: 0 0.05rem;
      font-size: 0.16rem;
      color: #1a1b1d;
      font-weight: 600;
    }
  }

  .star {
    width: 0.15rem;
    height: 0.1rem;
    display: inline-block;
    background: url('../images/star.png') no-repeat;
    background-size: 100% 100%;
  }

  .content {
    .tip {
      text-align: center;
      color: #616568;
    }
  }

  :deep(.van-popup) {
    background: transparent;
  }

  .action {
    display: flex;
    justify-content: space-between;
    margin-top: 0.15rem;
    padding: 0 0.7rem;

    .icon-poster {
      width: 0.52rem;
      height: 0.52rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(136deg, #1fe0d0 1%, #08bbac 100%);
      box-shadow: 0 0.02rem 0.12rem 0 rgba(8, 187, 172, 0.2);
      border: 0.02rem solid #ffffff;
      border-radius: 50%;

      img {
        width: 0.25rem;
        height: 0.24rem;
      }
    }

    .icon-wx {
      width: 0.52rem;
      height: 0.52rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(136deg, #ffffff 0%, #f1f1f1 100%);
      box-shadow: 0 0.02rem 0.12rem 0 rgba(160, 160, 160, 0.2);
      border: 0.02rem solid #ffffff;
      border-radius: 50%;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        top: -0.22rem;
        left: -0.05rem;
        width: 0.61rem;
        height: 0.26rem;
        background: url('../images/icon-qp.png') no-repeat;
        background-size: 100% 100%;
      }

      img {
        width: 0.35rem;
      }
    }

    .action-plate {
      width: 0.75rem;
      height: 1.09rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      span {
        color: #ce6100;
        margin-top: 0.08rem;
      }
    }
  }
</style>
