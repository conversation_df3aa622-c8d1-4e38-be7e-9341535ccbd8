# ijiaolian-h5

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Run your unit tests
```
npm run test:unit
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### git 提交规范

统一格式：
<type>(<scope>): <subject>
// 注意冒号 : 后有空格
// 如 feat(user): 增加用户中心的 xx 功能

scope 表示 commit 的作用范围，如用户中心、购物车中心，也可以是目录名称，一般可以限定几种；

subject 用于对 commit 进行简短的描述；

type 必填，表示提交类型，值一般有以下几种：

feat：新功能 feature
bug：测试反馈 bug 列表中的 bug 号
fix： 修复 bug
ui：更新UI；
docs： 文档注释变更
style： 代码格式(不影响代码运行的变动)；
refactor： 重构、优化(既不增加新功能，也不是修复bug)；
perf： 性能优化；
release：发布；
deploy：部署；
test： 增加测试
chore： 构建过程或辅助工具的变动
revert： 回退
build： 打包
