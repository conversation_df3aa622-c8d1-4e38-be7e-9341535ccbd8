// import { isIOS } from '@/utils'
import { getWorkChatSign } from '@/api/ai-server'
import { isQyWeChat } from '@/utils/index'

// async function getConfigSignature() {
//   let url = isIOS() ? window.entryUrl : location.href.split('#')[0]
//   const res = await getWorkChatSign({ url, type: 1 })

//   const { timestamp, noncestr, signature } = res.data || {}
//   console.log('企业签名:', timestamp, noncestr, signature)
//   // 根据 url 生成企业签名
//   return {
//     timestamp,
//     nonceStr: noncestr,
//     signature,
//   }
// }
async function getAgentConfigSignature() {
  let url = location.href.split('#')[0]

  const res = await getWorkChatSign({ url, type: 2 })

  const { timestamp, noncestr, signature } = res.data || {}
  console.log('企业应用签名:', timestamp, noncestr, signature)
  // 根据 url 生成企业应用签名
  return {
    timestamp,
    nonceStr: noncestr,
    signature,
  }
}
// 企业微信SDK授权
const authWorkChat = () => {
  return new Promise((resolve, reject) => {
    if (isQyWeChat()) {
      const ww = window.ww
      ww.register({
        corpId: 'ww8f126d814ac0287e', // 必填，当前用户企业所属企业ID
        agentId: process.env.VUE_APP_AGENT_ID, // 当前用户所属应用的ID
        jsApiList: [
          'getExternalContact',
          'getCurExternalContact',
          'launchMiniprogram',
          'setClipboardData',
          'getClipboardData',
        ], // 必填，需要使用的JSAPI列表
        // getConfigSignature, // 必填，根据url生成企业签名的回调函数
        getAgentConfigSignature, // 必填，根据url生成应用签名的回调函数
        // onConfigFail: (err) => {
        //   console.log('注册失败', err)
        //   reject(err)
        // },
        // onConfigSuccess: (res) => {
        //   console.log('注册成功', res)
        //   resolve(ww)
        // },
        onAgentConfigFail: (err) => {
          console.log('注册失败', err)
          reject(err)
        },
        onAgentConfigSuccess: (res) => {
          console.log('注册成功', res)
          resolve(ww)
        },
      })
    } else {
      reject({ msg: '请在企业微信环境下，授权JS-SDK' })
    }
  })
}

export default authWorkChat
