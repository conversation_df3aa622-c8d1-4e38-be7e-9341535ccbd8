<template>
  <page :title="$route.meta?.title" @pageshow="onPageShow">
    <template #page>
      <div class="page-content page-bg-white">
        <search-head autofocus @search="onSearch" />
        <div class="search-history" v-if="isShowSearchHistory">
          <div class="recent-search-head">
            <p>搜索历史</p>
            <button @click="onClearHistory" class="i-button icon-delete" />
          </div>
          <div class="recent-history-tags">
            <span v-for="keyword in searchHistory" :key="keyword">
              <router-link :to="getLink(keyword)">
                {{ keyword }}
              </router-link>
            </span>
          </div>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { Dialog } from 'vant'
  import SearchHead from './components/SearchHead'
  import { localProxyStorage } from '@/utils/storage'
  import { isIOS } from '@/utils'

  const router = useRouter()
  const searchHistory = ref(localProxyStorage?.searchHistory || [])

  const isShowSearchHistory = computed(() => {
    return searchHistory.value.length > 0
  })

  const onSearch = (keyword) => {
    if (!keyword) return

    const repeatKeywordIndex = searchHistory.value.findIndex((text) => {
      return text === keyword
    })

    if (repeatKeywordIndex > -1) {
      searchHistory.value.splice(repeatKeywordIndex, 1)
    }

    searchHistory.value.unshift(keyword)
    localProxyStorage.searchHistory = searchHistory.value

    router.push({
      name: 'searchResult',
      query: {
        keyword,
      },
    })
  }

  const onClearHistory = () => {
    Dialog.confirm({
      message: '确认要清空吗？',
    })
      .then(() => {
        searchHistory.value = []
        localProxyStorage.searchHistory = []
      })
      .catch(() => {})
  }

  const getLink = (keyword) => {
    return '/search-result?keyword=' + keyword
  }

  const onPageShow = (event) => {
    if (isIOS()) {
      if (event.persisted) {
        window.location.reload()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .search-history {
    padding: 0 0.14rem;

    .recent-search-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 0.1rem;
      user-select: none;

      p {
        font-size: 0.14rem;
        color: #000000;
      }

      .icon-delete {
        width: 0.16rem;
        height: 0.16rem;
        background: url('../../assets/images/home/<USER>') no-repeat;
        background-size: 100% 100%;
      }
    }

    .recent-history-tags {
      width: 100%;
      margin-top: 0.18rem;
      overflow: hidden;

      span {
        font-size: 0.12rem;
        //background: #f8f7f7;
        background: #f0f2f5;
        border-radius: 0.12rem;
        color: #616568;
        display: block;
        float: left;
        margin-right: 0.08rem;
        margin-bottom: 0.15rem;

        a {
          display: block;
          height: 0.23rem;
          line-height: 0.23rem;
          padding: 0 0.1rem;
        }
      }
    }
  }
</style>
