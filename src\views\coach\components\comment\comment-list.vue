<template>
  <div class="comment-wrap">
    <van-list
      class="comment-list"
      v-model:loading="loading"
      :finished="finished"
      @load="onLoad"
      ref="listRef"
    >
      <comment-item
        v-for="(item, index) in list"
        :key="index"
        :comment="item"
        @lookAllReply="({ comment }) => openCommentDetails(index, comment)"
        @addSubComment="(comment) => onAddSubComment(index, comment)"
      />
    </van-list>

    <empty v-if="emptyShow" top="0.35rem" description="暂无评价" />

    <!-- 评论详情列表 -->
    <comment-details
      v-model="commentDetailsShow"
      :comment="curLookComment"
      @replySuccess="updateCommentData"
    />
  </div>
</template>

<script setup>
  import { ref, nextTick } from 'vue'
  import CommentItem from './comment-item'
  import CommentDetails from './comment-details'
  import Empty from '@/components/empty'
  import { reqCoachCommentList } from '@/api/user-server'

  const props = defineProps({
    max: {
      type: Number,
      default: null,
    },
    pageSize: {
      type: Number,
      default: 7,
    },
    coachId: {
      type: [String, Number],
      default: null,
    },
  })

  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const commentDetailsShow = ref(false)

  const listRef = ref(false)
  const pageNum = ref(0)
  const curLookComment = ref(null)
  const curLookCommentIndex = ref(null)
  const emptyShow = ref(false)

  const onLoad = () => {
    pageNum.value += 1
    let params = {
      coachId: props.coachId,
      pageSize: props.pageSize,
      pageNum: pageNum.value,
    }
    reqCoachCommentList(params).then((res) => {
      const { data } = res

      list.value = list.value.concat(data)
      emptyShow.value = list.value.length === 0

      if (typeof props.max === 'number' && list.value.length >= props.max) {
        list.value = list.value.splice(0, props.max)
        loading.value = false
        finished.value = true
        return
      }

      // 加载状态结束
      loading.value = false

      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        finished.value = true
      }
    })
  }

  const reset = () => {
    list.value = []
    pageNum.value = 0
    loading.value = false
    finished.value = false

    nextTick(() => {
      listRef.value?.check()
    })
  }

  // 查看单条评论的所有回复列表
  const openCommentDetails = (index, comment) => {
    curLookComment.value = comment
    curLookCommentIndex.value = index
    console.log(index, 'curLookCommentIndex')
    commentDetailsShow.value = true
  }

  // 添加一级评论
  const addComment = (comment) => {
    list.value.unshift(comment)
    emptyShow.value = list.value.length === 0
    listRef.value.$el.scrollTo(0, 0)
  }

  const updateCommentData = ({ count, replyList }) => {
    list.value[curLookCommentIndex.value].replyCount = count
    list.value[curLookCommentIndex.value].replyListVO = replyList
  }

  // 添加子评论
  const onAddSubComment = (index, comment) => {
    list.value[index].replyCount += 1
    if (Array.isArray(list.value[index].replyListVO)) {
      list.value[index].replyListVO.unshift(comment)
    } else {
      list.value[index].replyListVO = [comment]
    }

    console.log(list.value[index], '数据')
  }

  defineExpose({
    reset,
    addComment,
  })
</script>

<style lang="scss" scoped></style>
