<template>
  <div ref="containerRef" class="latest-release">
    <div class="header">
      <span class="title">最新教练</span>
      <div @click="$router.push('/search-result?sort=3')">
        <span class="more">更多</span>
        <van-icon class="icon" size="0.12rem" name="arrow" />
      </div>
    </div>

    <ijl-skeleton class="skeleton" :loading="skeletonLoading" :count="3" animated>
      <template #skeleton>
        <div style="width: 1.08rem">
          <ijl-skeleton-image height="1.08rem" />
          <ijl-skeleton-row width="40%" />
          <ijl-skeleton-row />
        </div>
      </template>
      <template #content>
        <van-swipe class="swipe" lazy-render :autoplay="3000" indicator-color="#FF9B26FF">
          <van-swipe-item v-for="(arr, index) in coachList" :key="index">
            <div class="coach-container">
              <router-link
                v-for="item in arr"
                :key="item.id"
                class="coach"
                :to="getRouterPath(item)"
              >
                <ijl-image class="coach-img" :src="getCoachCover(item.coachImages)" fit="cover" />
                <div class="coach-name">{{ item.realName }}</div>
                <div class="coach-tag">{{ item.teachTitle }}</div>
              </router-link>
            </div>
          </van-swipe-item>
        </van-swipe>
      </template>
    </ijl-skeleton>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { reqNewestCoachList } from '@/api/coach-server'
  import IjlImage from '@/components/image'
  import { isElemVisibleRange } from '@/utils/elem'
  import { useEventListener } from '@vant/use'
  import { throttle } from '@/utils'

  const containerRef = ref(null)
  const coachList = ref([])
  const skeletonLoading = ref(true)
  let isLoad = false

  const slicer = (data, len) => {
    if (!Array.isArray(data)) return []

    const arr = []
    for (let i = 0; i < data.length; i += len) {
      arr.push(data.slice(i, i + len))
    }
    return arr
  }

  const getCoachCover = (data) => {
    let images = JSON.parse(data)
    if (Array.isArray(images)) {
      return images?.[0]
    }
  }

  const getRouterPath = (row) => {
    return '/coach/details/' + row.id
  }

  const getNewestCoachList = () => {
    let params = { pageNum: 1, pageSize: 12 }
    reqNewestCoachList(params).then((res) => {
      const { data } = res
      coachList.value = slicer(data.records, 3)
      skeletonLoading.value = false
    })
  }

  const handleScroll = () => {
    if (isElemVisibleRange(containerRef.value) && !isLoad) {
      isLoad = true
      getNewestCoachList()
    }
  }

  useEventListener('scroll', throttle(handleScroll, 100))
</script>

<style lang="scss" scoped>
  @import '@/styles/mixins/mixins.scss';

  .latest-release {
    background-color: #fff;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.08rem 0.12rem;
      margin-top: 0.08rem;
      user-select: none;

      .title {
        font-size: 0.16rem;
        font-weight: bold;
        color: #1a1b1d;
      }

      .more {
        font-size: 0.12rem;
        color: #979797;
      }

      .icon {
        color: #979797;
      }
    }

    .swipe {
      height: 1.94rem;

      :deep(.van-swipe__indicators) {
        bottom: 0.08rem;
      }

      :deep(.van-swipe__indicator) {
        width: 0.11rem;
        height: 0.04rem;
        border-radius: 0.02rem;
        background-color: #f2f2f2;
      }
    }

    .skeleton {
      :deep(.ijl-skeleton) {
        padding: 0 0.12rem;
      }
    }

    .coach-container {
      display: flex;
      justify-content: space-between;
      padding: 0 0.12rem;

      .coach {
        width: 1.08rem;

        .coach-img {
          width: 100%;
          height: 1.08rem;
          overflow: hidden;
          border-radius: 0.06rem;
          object-fit: cover;
          vertical-align: top;
        }

        .coach-name {
          font-size: 0.14rem;
          color: #1f1f1f;
          margin-top: 0.05rem;
        }

        .coach-tag {
          font-size: 0.12rem;
          color: #818487;
          margin-top: 0.03rem;
          @include TextEllipsis(2);
        }
      }
    }
  }
</style>
