<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content page-bg-white settings-profile">
        <ul class="setting-list">
          <li class="item feedback" @click="toFormPage(0)">
            <div class="title">基本信息</div>
            <div class="content">修改</div>
            <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
          </li>
          <li class="item feedback" @click="toFormPage(1)">
            <div class="title">授课信息</div>
            <div class="content">修改</div>
            <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
          </li>
          <li class="item feedback" @click="toFormPage(2)">
            <div class="title">教练资质</div>
            <div class="content">修改</div>
            <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
          </li>
        </ul>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const toFormPage = (stepsNum) => {
    router.push({
      name: 'coachApplyForm',
      query: {
        channel: 'coachUpdateList',
        stepsNum: stepsNum,
        isCoachIdentity: true,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .setting-list {
    padding: 0 0.1rem;

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.18rem 0.02rem 0.18rem 0.05rem;
      border-bottom: 1px solid #eeeeee;
      max-height: 0.55rem;

      .title {
        flex: 1;
        color: #1a1b1d;
        font-weight: 600;
        position: relative;
        padding-left: 0.1rem;

        &:before {
          content: '';
          width: 0.03rem;
          height: 0.14rem;
          background: #ff9b26;
          border-radius: 0.03rem;
          position: absolute;
          left: 0;
          top: 0.03rem;
        }
      }

      .content {
        flex: 1;
        text-align: right;
        color: #b2b1b7;
      }

      .icon {
        margin-left: 0.02rem;
      }
    }
  }
</style>
