<template>
  <div class="verify-notify">
    <img class="notify-icon" src="../../../assets/images/icon/icon-wait.png" alt="" />
    <div class="notify-content">
      <p class="text1">{{ title }}</p>
      <p class="text2">
        爱教练将于3个工作日内完成审核，并通过公众号或短信通知审核结果，请耐心等候！
      </p>
      <slot name="desc"></slot>
    </div>
  </div>
</template>

<script setup>
  defineProps({
    title: {
      type: String,
      default: '你已注册，等待后台审核…',
    },
  })
</script>

<style lang="scss" scoped>
  .verify-notify {
    padding: 0.36rem 0 0.32rem 0;
    background-color: #fff;
    text-align: center;

    .notify-icon {
      width: 0.36rem;
      height: 0.36rem;
      display: inline-block;
    }

    .text1 {
      margin-top: 0.15rem;
      font-size: 0.16rem;
      font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
      font-weight: 600;
      color: #1a1b1d;
    }

    .text2 {
      margin-top: 0.1rem;
      padding: 0 0.64rem;
      font-size: 0.12rem;
      color: #666666;
    }
  }
</style>
