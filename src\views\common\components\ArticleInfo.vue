<template>
  <div class="train-box news-box mt10">
    <div class="train-header flex">
      <div class="train-header-l flex">
        <p
          :class="{ 'train-title': true, 'train-title-select': articleParams.type == 1 }"
          @click="articleSelct(1)"
        >
          教学文章
        </p>
        <div class="split"></div>
        <p
          class="train-title"
          :class="{ 'train-title': true, 'train-title-select': articleParams.type == 2 }"
          @click="articleSelct(2)"
        >
          教学视频
        </p>
        <div class="split"></div>
        <p
          class="train-title"
          :class="{ 'train-title': true, 'train-title-select': articleParams.type == 3 }"
          @click="articleSelct(3)"
        >
          培训信息
        </p>
      </div>
    </div>
    <div class="news" @scroll="scroll">
      <template v-if="formData.newsList.length > 0">
        <div class="news-item flex" v-for="(item, index) in formData.newsList" :key="index">
          <div
            v-if="[1, 2].includes(articleParams.type)"
            class="other-news"
            @click="articleDetails(item)"
          >
            <div class="news-item-l">
              <p class="news-title">
                {{ item.title }}
              </p>
              <div class="flex news-info">
                <div class="flex">
                  <span :class="articleParams.type === 1 ? 'type-label' : 'type-video'">
                    {{ articleParams.type === 1 ? '文章' : '视频' }}
                  </span>
                  <span v-if="item.realName" class="author">{{ ellipsis(item.realName) }}</span>
                  <span v-if="item.releaseTime" class="news-time">{{
                    item.releaseTime?.split(' ')[0]
                  }}</span>
                  <span v-if="item.hits < 999" class="look-num">{{ item.hits }}浏览</span>
                  <span v-else class="look-num">999+浏览</span>
                </div>
              </div>
            </div>
            <div class="news-item-r">
              <div v-if="articleParams.type === 1">
                <div v-if="item.imageList?.length > 0" class="cover-img-box">
                  <img :src="ossURLJoin(item.imageList[0])" v-error-img />
                </div>
              </div>
              <div v-else>
                <div class="cover-img flex">
                  <img
                    v-if="item.coverImage?.length > 0"
                    class="videoImg"
                    :src="ossURLJoin(item.coverImage[0])"
                    alt=""
                  />
                  <img v-else src="../../../assets/images/logo.png" alt="" />
                </div>
                <div class="play-btn flex">
                  <img src="../../../assets/images/icon/play-btn.png" alt="" />
                </div>
              </div>
            </div>
          </div>
          <div v-if="articleParams.type === 3 && formData.newsList" class="train-box">
            <div class="train-item" @click="trainDetail(item)">
              <h3>{{ item.title }}</h3>
              <div class="intro">{{ item.description }}</div>
              <div class="train-foot flex">
                <div class="foot-l">
                  <span>{{ item.thirdlyCategoriesName }}</span>
                  <span v-if="item.cityName">{{ item.cityName }}{{ item.countyName }}</span>
                </div>
                <div class="foot-r">
                  <span>培训时间：{{ item.updateTime?.split(' ')[0] }}</span>
                  <span>{{ item.readCount }}浏览</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <empty top="1.2rem" description="暂无发布记录 ~" />
      </template>
      <div v-if="isExitScroll" class="foot-shadow"></div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, watch, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { ossURLJoin } from '@/common'
  import Empty from '@/components/empty'

  const router = useRouter()

  const props = defineProps({
    videoList: {
      type: Array,
      default: () => [],
    },
    articleList: {
      type: Array,
      default: () => [],
    },
    trainList: {
      type: Array,
      default: () => [],
    },
    cityLabel: {
      type: String,
      default: '',
    },
  })
  const articleParams = reactive({
    type: 1,
  })

  const formData = reactive({
    newsList: [],
  })
  const isExitScroll = ref(false)
  const scroll = (e) => {
    isExitScroll.value = e.target.scrollTop > 0 ? true : false
    console.log(e.target.scrollTop)
  }

  const newVideo = ref([])
  watch(
    () => props.videoList,
    (newVal) => {
      if (newVal.length > 0) {
        newVideo.value = newVal.map((item) => item)
      }
    },
    {
      immediate: true,
    },
  )
  const newTrain = ref([])
  watch(
    () => props.trainList,
    (newVal) => {
      if (newVal.length > 0) {
        newTrain.value = newVal.map((item) => item)
      }
    },
    {
      immediate: true,
    },
  )

  const newArticle = ref([])
  watch(
    () => props.articleList,
    (newVal) => {
      if (newVal.length > 0) {
        newArticle.value = newVal.map((item) => item)
        if (articleParams.type === 1) {
          formData.newsList = newVal.map((item) => item)
        }
      }
    },
    {
      immediate: true,
    },
  )

  const trainDetail = (item) => {
    router.push({
      path: `/train-details/${item.trainsId}`,
    })
  }
  // 切换文章
  const articleSelct = (index) => {
    let dom = document.querySelector('.news')
    dom.scrollTop = 0
    articleParams.type = index
    if (index === 2) {
      console.log(newVideo.value, 'newVideo.value')
      formData.newsList = newVideo.value
    } else if (index === 1) {
      formData.newsList = newArticle.value
    } else {
      formData.newsList = newTrain.value
    }
  }

  const ellipsis = (item) => {
    if (!item) return
    if (item.split('').length > 5) {
      return item.substring(0, 5) + '...'
    } else {
      return item
    }
  }
  // 文章详情
  const articleDetails = (item) => {
    if (articleParams.type === 1) {
      router.push({
        path: `/news-details/${item.id}`,
      })
    } else {
      router.push({
        path: `/video/details/${item.id}`,
      })
    }
  }
</script>

<style scoped lang="scss">
  // 公共样式
  $color1: #1f1f1fff;
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .train-box {
    // padding: 0.1rem 0.13rem;
    background-color: #fff;
  }
  .train-header {
    margin-bottom: 0.16rem;
    .train-header-l {
      img {
        width: 0.16rem;
        height: 0.16rem;
        margin-right: 0.03rem;
      }
      .title {
        font-size: 0.16rem;
        font-weight: 600;
        color: #1a1b1d;
      }
    }
    .train-header-r {
      p {
        font-size: 0.12rem;
        color: #b2b1b7;
        line-height: 0.17rem;
      }
      img {
        width: 0.1rem;
        height: 0.1rem;
        margin-left: 0.02rem;
      }
    }
  }
  .news-box {
    position: relative;
    // padding: 0.1rem 0;
    padding-top: 0.1rem;
    .train-header {
      padding: 0 0.15rem;
    }
    .foot-shadow {
      position: absolute;
      bottom: 0rem;
      width: 3.75rem;
      height: 0.1rem;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
      opacity: 0.04;
    }
  }
  .news {
    background-color: #fff;
    height: 3.8rem;
    overflow-y: scroll;

    .news-item {
      padding: 0.14rem 0.15rem;
      // box-shadow: 0rem 0rem 0.02rem 0rem rgba(0, 0, 0, 0.12);
      border-bottom: 0.01rem solid #eee;
      &:last-child {
        border: 0;
      }
      &:first-child {
        padding-top: 0;
      }

      .news-item-r {
        position: relative;
        margin-left: 0.05rem;
        // width: 1rem;
        // height: 0.7rem;
        flex-shrink: 0;
        .play-btn {
          position: absolute;
          top: 0;
          width: 1rem;
          height: 0.7rem;
          // background-color: rgba($color: rgb(0, 0, 0), $alpha: 0.2);
          img {
            display: block;
            width: 0.26rem;
            height: 0.26rem;
            margin: 0 auto;
          }
        }
        .cover-img-box {
          width: var(--i-article-cover-w);
          height: var(--i-article-cover-h);
          border-radius: 0.04rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 0.04rem;
            border: 0.01rem solid #eee;
          }
        }
        .cover-img {
          width: var(--i-article-cover-w);
          height: var(--i-article-cover-h);
          background: #f6f6f6;
          border-radius: 0.04rem;
          img {
            display: block;
            margin: 0 auto;
            width: 0.42rem;
            height: 0.48rem;
            object-fit: cover;
            border-radius: 0.04rem;
            border: 0.01rem solid #eee;
          }
          .videoImg {
            width: 100%;
            height: 100%;
            border-radius: 0.04rem;
          }
        }
      }

      .news-item-l {
        width: 100%;
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        .news-title {
          font-size: 0.15rem;
          color: #414141;
          line-height: 0.23rem;
          margin-bottom: 0.08rem;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }

        .news-img-title {
          height: 0.4rem;
          font-size: 0.15rem;
          // margin-bottom: 0;
        }
      }
      .first-news {
        position: relative;
        width: 100%;
        border-radius: 0.04rem;
        overflow: hidden;
        .first-cover-img,
        .play-btn {
          width: 100%;
          height: 2.02rem;
          border-radius: 0.04rem;
        }
        .first-cover-img {
          background: #f6f6f6;
          img {
            display: block;
            margin: 0 auto;
            width: 1rem;
            height: 1.18rem;
          }
        }
        .author-box {
          display: flex;
          margin-top: 0.04rem;
        }
        .play-btn {
          position: absolute;
          top: 0;
          // background-color: rgba($color: rgb(0, 0, 0), $alpha: 0.2);
          img {
            display: block;
            width: 0.5rem;
            height: 0.5rem;
            margin: 0 auto;
            object-fit: cover;
          }
        }
      }
      .other-news {
        width: 100%;
        display: flex;
      }
      .type-label,
      .type-video {
        margin-right: 0.1rem;
        padding: 0.01rem 0.04rem;
        background-color: #ededed;
        font-size: 0.1rem;
        color: #616568;
        border-radius: 0.02rem;
      }
      .type-video {
        background-color: #dbedfe;
        color: #0083fc;
      }
      .author,
      .look-num,
      .news-time {
        font-size: 0.1rem;
        color: #b2b1b7;
        margin-right: 0.1rem;
      }
      .first-news-bot {
        position: absolute;
        padding: 0 0.15rem;
        padding-top: 0.45rem;
        bottom: 12%;
        width: 100%;
        color: #fff;
        width: 3.45rem;
        height: 0.79rem;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);

        background: linear-gradient(
          180deg,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0.1) 17%,
          rgba(0, 0, 0, 0.5) 100%
        );
        border-radius: 0 0 0.04rem 0.04rem;
        overflow: hidden;
        .author {
          color: #fff;
          margin-right: 0;
        }
        .news-title {
          font-size: 0.15rem;
          width: 2rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .news-time {
          margin-right: 0;
        }
        .look-num {
          color: #fff;
        }
      }
      .news-info {
        width: 100%;
        display: flex;
        align-items: flex-end;
      }
      .icon-eye2 {
        margin-right: 0.03rem;
      }
    }
  }
  .split {
    margin: 0 0.14rem;
    width: 0.01rem;
    height: 0.15rem;
    background: rgba(178, 177, 183, 0.4);
  }
  .train-title {
    // width: 0.8rem;
    height: 0.2rem;
    font-size: 0.16rem;
    font-weight: 600;
    color: #b2b1b7;
    line-height: 0.2rem;
  }
  .train-title-select {
    font-weight: 600;
    color: #1a1b1d;
  }

  .train-box {
    flex-wrap: wrap;
  }
  .train-item {
    // padding: 0.15rem 0.12rem;
    width: 3.45rem;
    flex-shrink: 0;
    background-color: #fff;
    // border-bottom: 0.01rem solid #f7f7f7;
    h3 {
      font-size: 0.15rem;
      color: $color1;
      font-weight: 400;
    }
    .intro {
      padding-top: 0.04rem;
      margin-bottom: 0.12rem;
      max-height: 0.37rem;
      font-size: 0.13rem;
      color: #616568;
      line-height: 0.17rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .train-foot {
      .foot-l span {
        padding: 0.01rem 0.02rem;
        border: 0.01rem solid #979797;
        border-color: rgba($color: #979797, $alpha: 0.8);
        border-radius: 0.02rem;
        font-size: 0.1rem;
        color: #979797;
        margin-right: 0.04rem;
      }
      .foot-r span {
        font-size: 0.1rem;
        color: #979797;
        &:first-child {
          margin-right: 0.1rem;
        }
      }
    }
  }
  :deep(.van-empty) {
    padding: 1.2rem 0;
  }
</style>
