<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page" @pageshow="onPageShow">
    <template #page>
      <div class="container">
        <div class="operating" v-if="list.length > 0">
          <button class="all-read-btn feedback" @click="handledAllRead">一键已读</button>
        </div>
        <div class="list">
          <van-list
            ref="listRef"
            v-model:loading="loading"
            :finished="finished"
            :finished-text="list.length > 0 ? '-没有更多了-' : null"
            @load="getMessageList"
          >
            <div v-for="item in list" :key="item.msgId" class="message" @click="toMessageURL(item)">
              <div class="message-title">
                <div>{{ item.title }}</div>
                <div class="arrow">
                  <van-icon name="arrow" size="0.16rem" color="rgb(185,184,184)" />
                </div>
              </div>
              <div class="message-desc">
                <div class="text omit">
                  {{ item.content }}
                </div>
                <div v-if="item.state === 0" class="message-unread"></div>
              </div>
              <div class="message-time">{{ item.sendTime }}</div>
            </div>
          </van-list>
          <empty v-if="emptyShow" top="2.41rem" description="暂无消息" />
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { baseURL } from '@/config'
  import { Toast } from 'vant'
  import Empty from '@/components/empty'
  import { isIOS } from '@/utils'
  import {
    reqCoachMessageList,
    reqMarkMessageRead,
    reqMarkALLMessageRead,
  } from '@/api/coach-worktable'

  const router = useRouter()

  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const emptyShow = ref(false)
  const listRef = ref(null)
  const pagination = {
    pageNum: 0,
    pageSize: 10,
  }

  const getMessageList = () => {
    pagination.pageNum += 1
    let params = { ...pagination }
    reqCoachMessageList(params).then((res) => {
      const { data } = res
      list.value = list.value.concat(data)

      if (list.value.length === 0) {
        emptyShow.value = true
      }

      // 加载结束
      loading.value = false

      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        finished.value = true
      }
    })
  }

  const handledAllRead = () => {
    let toast = Toast.loading({
      duration: 0,
      message: '请稍等...',
      forbidClick: true,
    })
    reqMarkALLMessageRead().then(() => {
      list.value = []
      pagination.pageNum = 0
      loading.value = false
      finished.value = false
      listRef.value?.check()
      toast.clear()
    })
  }

  const toMessageURL = async (message) => {
    // 标记消息已读
    await reqMarkMessageRead({ informId: message.informId })

    if (!message.url) return
    let chars = message.url?.split(baseURL)
    if (chars.length === 2) {
      router.push(chars[1] || '/')
    } else {
      window.location.href = message.url
    }
  }

  const onPageShow = (event) => {
    if (isIOS()) {
      if (event.persisted) {
        window.location.reload()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .container {
    padding: 0 0.15rem;
    //height: 100%;
  }

  .operating {
    padding: 0.06rem 0;
    text-align: right;

    .all-read-btn {
      padding: 0.05rem 0.14rem;
      font-size: 0.12rem;
      color: #616568;
      border-radius: 0.23rem;
      border: 1px solid #dddddd;
    }
  }

  .message {
    background: #fff;
    padding: 0.06rem 0 0.12rem 0.15rem;
    margin-bottom: 0.08rem;
    border-radius: 0.06rem;

    .message-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.15rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .arrow {
      width: 0.32rem;
      height: 0.32rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .message-desc {
      font-size: 0.13rem;
      color: #616568;
      display: flex;
      align-items: center;

      .text {
        width: 3.01rem;
      }
    }

    .message-unread {
      width: 0.07rem;
      height: 0.07rem;
      border-radius: 50%;
      background: #ff6445;
      margin-left: 0.06rem;
      margin-right: 0.16rem;
    }

    .message-time {
      margin-top: 0.1rem;
      font-size: 0.12rem;
      color: #b2b1b7;
    }
  }
</style>
