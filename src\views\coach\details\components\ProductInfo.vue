<template>
  <div class="product-info">
    <Skeleton :loading="loading" animated>
      <template #skeleton>
        <SkeletonRow width="100%" />
        <SkeletonRow width="40%" />
        <SkeletonRow width="65%" />
        <SkeletonRow width="100%" />
      </template>
      <template #content>
        <Row class="price-container" justify="space-between" align="center">
          <Col>
            <div class="product-info__name">
              <div>{{ name }}</div>
            </div>
          </Col>
          <Col>
            <ProductPrice :price="price" />
          </Col>
        </Row>
        <div class="product-info__desc">{{ desc }}</div>
        <div class="product-info__tags">
          <span v-for="tag in tags" :key="tag.name" class="product-info__tags-item">
            {{ tag.name }}
          </span>
        </div>
        <div class="product-info__city-region">
          <Unfold> 上课区域：{{ cityRegion }} </Unfold>
        </div>
      </template>
    </Skeleton>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import ProductPrice from '@/views/coach/details/components/ProductPrice'
  import { Row, Col } from 'vant'
  import Unfold from '@/components/unpack/unfold'
  import { Skeleton, SkeletonRow } from '@/components/skeleton'

  const props = defineProps({
    dataSource: { type: Object, default: null },
    loading: Boolean,
  })

  const name = computed(() => {
    return props.dataSource?.coachName
  })

  const desc = computed(() => {
    return props.dataSource?.teachTitle
  })

  const tags = computed(() => {
    return props.dataSource?.coachLabels
  })

  const price = computed(() => {
    return props.dataSource?.price
  })

  const cityRegion = computed(() => {
    return props.dataSource?.teachingDistricts?.map((item) => item.districtName).join('、')
  })
</script>

<style scoped lang="scss">
  .product-info {
    position: relative;
    z-index: 2;
    border-radius: 0.16rem 0.16rem 0 0;
    background: #fff;
    margin-top: -0.14rem;
    padding: 0.15rem;
  }

  .product-info__name {
    font-size: 0.18rem;
    font-weight: bold;
    line-height: 0.25rem;
    letter-spacing: 0.18px;
    display: flex;
    align-items: center;
  }

  .product-info__desc {
    margin-top: 0.04rem;
    line-height: 0.2rem;
    color: #b0b0b0;
  }

  .product-info__tags {
    margin-top: 0.1rem;
  }

  .product-info__tags-item {
    height: auto;
    display: inline-flex;
    align-items: center;
    padding: 0 0.04rem;
    font-size: 0.12rem;
    border-radius: 0.04rem;
    background: #ff9b26;
    color: #fff;
    margin-right: 0.06rem;
  }

  .product-info__city-region {
    margin-top: 0.1rem;
    font-size: 0.12rem;
  }
</style>
