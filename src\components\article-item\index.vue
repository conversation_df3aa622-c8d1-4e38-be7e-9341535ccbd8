<template>
  <div class="article">
    <div class="article__desc">
      <div class="article__title">{{ article.title }}</div>
      <div class="article__publication">
        <div>
          <span class="article__author omit">{{ article.realname }}</span>
          <span class="article__time">{{ article.updateTime.substring(0, 10) }}</span>
        </div>
        <div class="article__publication__num">
          <span class="num">{{ article.hits }}浏览</span>
        </div>
      </div>
    </div>
    <div class="article__cover" v-if="article.imageList.length > 0">
      <img
        v-error-img
        class="article__cover--img"
        :src="ossURL + '/' + article.imageList[0]"
        alt=""
      />
    </div>
  </div>
</template>

<script setup>
  import { ossURL } from '@/config'
  defineProps({
    article: {
      type: Object,
      default: () => {},
    },
  })
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: middle;
    margin-right: 0.06rem;
  }
  .article {
    display: flex;
  }

  .article__desc {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .article__title {
    //height: 0.44rem;
    margin-bottom: 0.08rem;
  }

  .article__cover {
    margin-left: 0.12rem;
  }

  .article__publication {
    display: flex;
    justify-content: space-between;
    font-size: 0.12rem;
    color: #b2b1b7;
    margin-bottom: 0.04rem;
  }

  .article__cover--img {
    width: 0.97rem;
    height: 0.7rem;
    object-fit: cover;
  }

  .article__title {
    font-size: 0.14rem;
    font-weight: 500;
    color: #414141;
  }

  .article__author {
    max-width: 0.74rem;
    display: inline-block;
    vertical-align: top;
  }

  .article__time {
    margin-left: 0.1rem;
    display: inline-block;
    vertical-align: top;
  }

  .num {
    color: #b2b1b7;
    vertical-align: middle;
  }
</style>
