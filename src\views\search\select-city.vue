<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="header">
        <div class="fixed">
          <div class="header-search">
            <div class="search-input-left-icon">
              <i class="icon-search"></i>
            </div>
            <div class="search-input">
              <input
                ref="inputRef"
                v-model="searchValue"
                @keyup.enter="onSearch"
                @input="onSearch"
                placeholder="搜索城市名、拼音或首字母查询"
                maxlength="20"
              />
            </div>
          </div>
        </div>

        <div class="placeholder"></div>
      </div>
      <div class="search-result" v-show="searchResultShow">
        <van-cell
          class="city"
          v-for="(item, index) in searchResult"
          :key="index"
          :title="item.adName"
          @click="toggleCity(item)"
        />
        <div v-if="searchResult.length === 0" class="search-result-empty">无结果</div>
      </div>

      <div v-show="mainShow" class="main">
        <div class="select-city">已选：{{ selectedGeolocation.city }}</div>
        <div class="current-city">
          <p>当前定位</p>
          <div class="tags">
            <span
              :class="selectedClassName(curGeolocation.adcode)"
              @click="toggleCity({ adName: curGeolocation.city, adCode: curGeolocation.adcode })"
            >
              <van-icon name="location-o" />{{ curGeolocation.city }}
            </span>
            <span
              :class="selectedClassName('000000')"
              @click="toggleCity({ adName: '全国', adCode: '000000' })"
            >
              全国
            </span>
          </div>
        </div>

        <van-index-bar :sticky="false" highlight-color="white" :sticky-offset-top="44">
          <template v-for="(value, key) in groupingCity" :key="key">
            <van-index-anchor :index="key" />
            <van-cell
              v-for="city in value"
              :key="city.cityCode"
              :title="city.adName"
              :class="indexBarActiveClassName(city.adCode)"
              @click="toggleCity(city)"
            />
          </template>
        </van-index-bar>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { reqAreaOptions } from '@/api/common'
  import { localProxyStorage } from '@/utils/storage'
  import { useParent } from '@/use/useRelation'

  const { parent: App } = useParent('IjlAPP')
  const router = useRouter()
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const searchResultShow = ref(false)
  const mainShow = ref(true)
  const searchValue = ref('')
  const searchResult = ref([])
  const cityOptions = ref([])
  const groupingCity = reactive({})
  const curGeolocation = computed(() => App.geo.value)
  const selectedGeolocation = ref({})

  const selectedClassName = (adcode) => {
    return {
      'selected-city': selectedGeolocation.value.adcode === adcode,
    }
  }

  const indexBarActiveClassName = (adcode) => {
    return {
      'activity-city': selectedGeolocation.value.adcode === adcode,
    }
  }

  //模糊查询
  function fuzzyQuery(list, keyWord) {
    if (!keyWord.trim()) return []
    let reg = new RegExp(keyWord)
    let arr = []
    for (let i = 0; i < list.length; i++) {
      if (reg.test(list[i].adName) || reg.test(list[i].label)) {
        arr.push(list[i])
      }
    }
    return arr
  }

  const onSearch = () => {
    if (searchValue.value.trim() === '') {
      searchResultShow.value = false
      mainShow.value = true
    } else {
      // 滚动条回到顶部，避免内容被遮挡
      document.documentElement.scroll(0, 0)

      mainShow.value = false
      searchResultShow.value = true

      // 模糊检索
      searchResult.value = fuzzyQuery(cityOptions.value, searchValue.value)
    }
  }

  const toggleCity = (city) => {
    selectedGeolocation.value = {
      city: city.adName,
      adcode: city.adCode,
    }
    localProxyStorage.selectedGeolocation = selectedGeolocation.value

    router.go(-1)
  }

  const getAreaOptions = () => {
    reqAreaOptions().then((res) => {
      const { data } = res
      data.forEach((item) => {
        cityOptions.value = cityOptions.value.concat(item.children)
      })

      alphabet.split('').forEach((letter) => {
        groupingCity[letter] = []
      })

      cityOptions.value.forEach((item) => {
        let key = item.label.substring(0, 1).toLocaleUpperCase()
        groupingCity[key].push(item)
      })
    })
  }

  const initialize = () => {
    selectedGeolocation.value = localProxyStorage.selectedGeolocation
      ? localProxyStorage.selectedGeolocation
      : curGeolocation.value

    getAreaOptions()
  }

  initialize()
</script>

<style lang="scss" scoped>
  .header {
    .fixed {
      position: fixed;
      left: var(--window-left);
      right: var(--window-right);
      top: var(--nav-bar-height);
      z-index: 99;
      background-color: #fff;
      padding: 0.08rem 0.15rem;
    }

    .placeholder {
      height: 0.5rem;
    }
  }

  .header-search {
    flex: 1;
    background: #f3f3f3;
    border-radius: 0.17rem;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0.02rem 0;

    .search-input-left-icon {
      width: 0.18rem;
      height: 0.18rem;
      margin-left: 0.1rem;
    }

    .icon-search {
      width: 0.18rem;
      height: 0.18rem;
      display: block;
      background: url('../../assets/images/icon/icon-search.png') no-repeat;
      background-size: 100% 100%;
    }

    .search-input {
      margin-left: 0.08rem;
      flex: 1;
      padding-right: 0.55rem;

      input {
        width: 100%;
        height: 0.3rem;
        color: #232326;
        border: none;
        font-size: 0.12rem;
        background: transparent;

        &::placeholder {
          color: #b2b1b7;
        }
      }
    }
  }

  .select-city {
    background-color: #fff;
    padding: 0.1rem 0.15rem;
    font-size: 0.12rem;
    color: #606368;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background-color: rgba(238, 238, 238, 0.33);
      transform: scaleY(0.5);
    }
  }

  .current-city {
    padding: 0.1rem 0.15rem;
    font-size: 0.13rem;
    color: #606368;
    background-color: #fff;

    .tags {
      margin-top: 0.08rem;

      span {
        display: inline-block;
        background: #f8f7f7;
        color: #616568;
        font-size: 0.12rem;
        padding: 0.06rem 0.12rem;
        vertical-align: top;
        border-radius: 0.04rem;

        .van-icon {
          margin-right: 0.03rem;
        }
      }

      span + span {
        margin-left: 0.1rem;
      }

      .selected-city {
        color: #ff6445;
        background: #ffefec;
      }
    }
  }

  .search-result {
    width: 100%;
    height: 100vh;
    background-color: #ffffff;

    .city {
      cursor: pointer;
      padding: 0.1rem 0.15rem;
    }

    .search-result-empty {
      text-align: center;
      color: #606368;
      padding: 0.2rem 0;
    }
  }

  .activity-city {
    color: #ff6445;
    font-weight: 600;
  }

  :deep(.van-index-bar__index--active) {
    width: 0.16rem;
    height: 0.16rem;
    line-height: 0.16rem;
    background-color: #ff9b26ff;
    border-radius: 50%;
    text-align: center;
  }

  :deep(.van-index-bar__index) {
    width: 0.16rem;
    padding: 0;
    margin-right: 0.08rem;
  }

  :deep(.van-index-bar__sidebar) {
    right: var(--window-right);
  }
</style>
