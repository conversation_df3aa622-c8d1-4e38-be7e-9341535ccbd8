<template>
  <div class="post-state">
    <van-icon name="info" color="#E02525" size="0.14rem" />
    <span class="f11">
      此内容已被爱教练平台禁用，详情请<a @click.stop="open" href="javascript:">咨询客服</a>
    </span>
  </div>
</template>

<script setup>
  import gm from '@/components/gm-popup'

  const open = () => {
    gm.open({
      title: '联系客服',
      desc: '添加企业微信，在线联系客服',
    })
  }
</script>

<style lang="scss" scoped>
  .post-state {
    display: flex;
    align-items: center;
    margin-top: 0.08rem;

    .van-icon {
      margin-right: 0.03rem;
    }

    span {
      display: inline-block;
      transform-origin: left;
    }

    a {
      color: #ff9b26;
    }
  }
</style>
