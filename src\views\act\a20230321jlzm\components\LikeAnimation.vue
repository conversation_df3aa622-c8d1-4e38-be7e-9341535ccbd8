<template>
  <div ref="lottieBox" class="lottieBox"></div>
</template>

<script setup>
  import { ref, onUnmounted } from 'vue'
  import lottie from 'lottie-web'
  import giveLike from '../give-like.json'

  const lottieBox = ref(null)
  const anim = ref(null)

  onUnmounted(() => anim.value?.destroy())

  const play = () => {
    if (lottieBox.value) {
      anim.value = lottie.loadAnimation({
        container: lottieBox.value,
        renderer: 'svg', // 渲染方式:svg：支持交互、不会失帧、canvas、html：支持3D，支持交互
        loop: false, // 循环播放，默认：true
        autoplay: true, // 自动播放 ，默认true
        animationData: giveLike, //本地路径，优先级更高
      })

      anim.value.addEventListener('complete', () => {
        anim.value?.destroy()
      })
    }
  }

  defineExpose({ play })
</script>

<style lang="scss" scoped>
  .lottieBox {
    width: 3.75rem;
    position: fixed;
    top: -1.5rem;
    z-index: 99;
  }
</style>
