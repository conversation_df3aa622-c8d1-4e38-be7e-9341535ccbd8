<template>
  <div class="panel">
    <div class="panel__hd">
      <div class="panel__hd-title">
        <slot name="title">
          {{ title }}
        </slot>
      </div>
    </div>
    <div class="panel__bd">
      <slot />
    </div>
    <div class="panel__ft">
      <slot name="bottom" />
    </div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'Panel',

    props: {
      title: String,
    },

    setup() {},
  })
</script>

<style scoped lang="scss">
  .panel {
    background-color: #fff;
    position: relative;
    overflow: hidden;

    .panel__hd {
      padding: 0.16rem 0.16rem 0.13rem;
      color: rgba(0, 0, 0, 0.9);
      font-size: 0.16rem;
      font-weight: 500;
      position: relative;

      &:before {
        content: ' ';
        position: absolute;
        left: 0.16rem;
        top: 50%;
        transform: translateY(-50%);
        width: 0.03rem;
        height: 0.16rem;
        background: linear-gradient(360deg, #ff6445 0%, #ff9b26 100%);
        border-radius: 1px;
      }

      &:after {
        content: ' ';
        position: absolute;
        left: 0.16rem;
        bottom: 0;
        right: 0.16rem;
        height: 1px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: rgba(0, 0, 0, 0.1);
        transform-origin: 0 100%;
        transform: scaleY(0.5);
      }
    }

    .panel__hd-title {
      padding-left: 0.08rem;
    }

    .panel__bd {
    }

    .panel__ft {
      position: relative;

      &:before {
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        height: 1px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        color: rgba(0, 0, 0, 0.1);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        transform: scaleY(0.5);
      }
    }
  }
</style>
