<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content container">
        <div class="order-state">
          <p>
            <span class="label">订单状态：</span>
            <span class="value">{{ orderDetail.orderStatus?.statusName }}</span>
          </p>
          <p class="order-tip">
            <!-- 待上课 -->
            <template v-if="orderDetail.orderStatus?.status === 'PAID'">
              学员已购买课时，请及时联系学员预约上课
            </template>
            <!-- 上课中 -->
            <template v-if="orderDetail.orderStatus?.status === 'PART_CONSUMED'">
              <span v-if="noSchoolDays(orderDetail.lastConsumeTime) >= 3">
                学员已经{{
                  noSchoolDays(orderDetail.lastConsumeTime)
                }}天没有上课啦，记得联系学员上课哦～
              </span>
              <span v-else>
                已核销 {{ orderDetail.consumedClassesQuantity }} 个课时，剩余
                {{ orderDetail.showRemainQuantity || 0 }} 个课时
              </span>
            </template>
            <!-- 交易完成 -->
            <template v-if="orderDetail.orderStatus?.status === 'FINISH'">
              <span v-if="orderDetail.refundedQuantity > 0">
                已核销{{ orderDetail.consumedClassesQuantity }}个课时，完成退款{{
                  orderDetail.refundedQuantity
                }}个课时
              </span>
              <span v-else
                >学员已上完{{ orderDetail.consumedClassesQuantity }}个课时，交易完成</span
              >
            </template>
            <!-- 交易关闭 -->
            <template v-if="orderDetail.orderStatus?.status === 'CANCELED'">
              学员申请退款完成
            </template>
          </p>
        </div>
        <div class="order-goods-details">
          <van-image
            class="goods-images"
            round
            fit="cover"
            width="0.74rem"
            height="0.74rem"
            :src="getOssURL(orderDetail.orderItemList?.[0]?.imageUrl)"
          />
          <div class="goods-info">
            <div>
              <van-row justify="space-between" align="center">
                <van-col class="goods-name omit">
                  {{ orderDetail.orderItemList?.[0]?.spuName }} ｜
                  {{ orderDetail.orderItemList?.[0]?.skuName }}
                </van-col>
                <van-col class="buy-price"
                  >¥{{ orderDetail.orderItemList?.[0]?.totalAmount }}</van-col
                >
              </van-row>
            </div>
            <div class="goods-spec">
              授课方式：{{ orderDetail.orderItemList?.[0]?.teachingWay?.typeName }}
            </div>
            <div class="buy-number">
              课时数：{{ orderDetail.orderItemList?.[0]?.quantity }}个课时
            </div>
            <div
              class="buy-state"
              v-if="orderDetail.orderItemList?.[0]?.afterSaleStatus?.status !== 'NONE'"
              @click="toRefundDetails"
            >
              {{ orderDetail.orderItemList?.[0]?.afterSaleStatus?.statusName }}
            </div>
          </div>
        </div>
        <div class="residue">
          <div>
            <span class="label">已核销课时：</span>
            <span class="value">{{ orderDetail.consumedClassesQuantity || 0 }}个</span>
          </div>
          <div>
            <span class="label">已授课分成：</span>
            <span class="value">{{ orderDetail.coachRealIncome || 0 }}元</span>
          </div>
        </div>
        <div class="student-info">
          <van-row justify="space-between" align="center">
            <van-col class="title">学员信息</van-col>
            <van-col @click="toStudentsDetails">
              上课详情<van-icon name="arrow" size="0.1rem" color="#626669" />
            </van-col>
          </van-row>
          <van-row justify="space-between" align="center">
            <van-col class="flex">
              <div class="head-portrait">
                <img :src="getOssURL(orderDetail.studentInfo?.avatarUrl)" alt="" />
              </div>
              <div class="username">{{ orderDetail.studentInfo?.studentName }}</div>
            </van-col>
            <van-col>
              <!-- <a :href="'tel:' + orderDetail.studentInfo?.mobile">
                <button class="call-phone">联系学员</button>
              </a> -->
            </van-col>
          </van-row>
        </div>
        <div class="order-info">
          <div class="title">订单信息</div>
          <div class="cell">
            <div class="label">实付款</div>
            <div class="value money">¥{{ numberRP(orderDetail.paymentAmount) }}</div>
          </div>
          <!-- 支付明细 -->
          <div
            v-if="orderDetail.balancePaymentAmount || orderDetail.thirdPaymentAmount"
            class="payment-details"
          >
            <div v-if="orderDetail.balancePaymentAmount > 0" class="payment-item">
              <div class="payment-item__label">钱包支付</div>
              <div class="payment-item__value">¥{{ orderDetail.balancePaymentAmount }}</div>
            </div>
            <div v-if="orderDetail.thirdPaymentAmount > 0" class="payment-item">
              <div class="payment-item__label">微信支付</div>
              <div class="payment-item__value">¥{{ orderDetail.thirdPaymentAmount }}</div>
            </div>
          </div>
          <div class="cell">
            <div class="label">支付方式</div>
            <div class="value">{{ orderDetail.paymentType?.typeName }}</div>
          </div>
          <div
            v-if="
              orderDetail?.cashbackType !== 0 &&
              (orderDetail?.cashbackState === 1 || orderDetail?.cashbackState === 2)
            "
            class="cell"
          >
            <div class="label">平台补贴学员</div>
            <div v-if="orderDetail.cashbackType === 1" class="value">
              完课返现￥{{ orderDetail.totalCashbackAmount }}
            </div>
            <div v-if="orderDetail.cashbackType === 2" class="value">
              续费返现￥{{ orderDetail.totalCashbackAmount }}
            </div>
          </div>
          <!-- <div class="cell">
            <div class="label">手机号</div>
            <div class="value">{{ encryptTel(orderDetail.contactMobile) }}</div>
          </div> -->
          <div class="cell">
            <div class="label">订单编号</div>
            <div class="value">
              <span>{{ orderDetail.id }} I </span>
              <copy-text class="copy" :text="orderDetail.id" @success="copySuccess">
                复制
              </copy-text>
            </div>
          </div>
          <div class="cell">
            <div class="label">创建时间</div>
            <div class="value">{{ orderDetail.createTime }}</div>
          </div>
          <div class="cell">
            <div class="label">付款时间</div>
            <div class="value">{{ orderDetail.paymentTime }}</div>
          </div>
        </div>
        <div class="order-info income-info">
          <div class="title">授课分成</div>
          <div class="cell">
            <div class="label">订单类型</div>
            <div v-if="orderDetail.incomeOrderType === 1" class="value">
              学员首单 <van-icon name="warning-o" @click="firstTipShow = true" />
            </div>
            <div v-if="orderDetail.incomeOrderType === 2" class="value">学员复购</div>
          </div>
          <div class="cell">
            <div class="label">分成模式</div>
            <div v-if="orderDetail.incomeRatioType === 1" class="value">比例分成</div>
            <div v-if="orderDetail.incomeRatioType === 2" class="value">金额分成</div>
          </div>
          <div class="cell">
            <div class="label">授课分成</div>
            <div v-if="orderDetail.incomeRatioType === 1" class="value">
              {{ orderDetail.coachIncomeRatio * 100 + '%' }}
            </div>
            <div v-if="orderDetail.incomeRatioType === 2" class="value">
              ￥{{ orderDetail.coachIncomeRatio }}/课时
            </div>
          </div>
        </div>
      </div>
      <van-popup
        v-model:show="firstTipShow"
        round
        position="bottom"
        closeable
        @close="firstTipShow = false"
        :safe-area-inset-bottom="true"
      >
        <div>
          <div class="first-tip-header">首单规则</div>
          <div class="first-tip-content">新学员首次成交您课程，该订单即被定义为首单</div>
        </div>
      </van-popup>
    </template>
  </page>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { Toast } from 'vant'
  import CopyText from '@/components/copy-text'
  import { reqOrderDetail } from '@/api/coach-worktable'
  import { getOssURL } from '@/common'
  import { numberRP } from '@/utils'
  import { getDateTime } from '@/utils/day'

  const route = useRoute()
  const router = useRouter()
  const orderId = route.query.orderId
  const orderDetail = ref({})
  const firstTipShow = ref(false)

  const getOrderDetail = () => {
    let params = { id: orderId }
    reqOrderDetail(params).then((res) => {
      const { data } = res
      orderDetail.value = data
    })
  }

  const copySuccess = () => {
    Toast('复制成功')
  }

  const toStudentsDetails = () => {
    router.push({
      name: 'myWorktableStudentsDetails',
      query: {
        studentUserId: orderDetail.value.studentInfo.studentUserId,
      },
    })
  }

  const toRefundDetails = () => {
    let orderId = orderDetail.value.orderItemList[0].afterSaleId
    router.push({
      name: 'myWorktableOrderRefundDetails',
      query: {
        orderId,
      },
    })
  }

  // 计算学员连续多少天未上课
  const noSchoolDays = (lastSchoolDay) => {
    let ms = getDateTime() - getDateTime(lastSchoolDay)
    return Math.round(ms / 86400000)
  }

  getOrderDetail()
</script>

<style lang="scss" scoped>
  .container {
    background: #fff;
    min-height: 100vh;
  }

  .order-state {
    padding: 0.12rem 0.15rem;
    border-bottom: 1px solid #eeeeee;
    background: #fff;

    .label {
      color: #616568;
    }

    .value {
      font-size: 0.18rem;
      font-weight: bold;
      color: #1a1b1d;
    }
  }

  .order-tip {
    margin-top: 0.06rem;
    color: #616568;
  }

  .order-goods-details {
    display: flex;
    padding: 0.11rem 0.15rem 0 0.15rem;
    background: #fff;

    .goods-images {
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      text-align: right;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }
  }

  .residue {
    background: #fff;
    padding: 0.2rem 0.15rem 0.14rem 0.15rem;
    font-size: 0.16rem;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      color: #1a1b1d;
    }

    .value {
      color: #ff6445;
    }
  }

  .student-info {
    border-top: 0.08rem solid #f7f7f7;
    border-bottom: 0.08rem solid #f7f7f7;
    padding: 0 0.15rem 0.12rem 0.15rem;
    background: #fff;

    .flex {
      display: flex;
      align-items: center;
    }

    .title {
      padding: 0.12rem 0 0.08rem 0;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .head-portrait {
      width: 0.36rem;
      height: 0.36rem;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .username {
      font-size: 0.14rem;
      color: #1f1f1f;
      margin-left: 0.1rem;
    }

    .call-phone {
      padding: 0.04rem 0.12rem;
      border-radius: 16px;
      border: 1px solid #ff9b26;
      color: #ff9b26;
    }
  }

  .order-info {
    padding: 0 0.15rem;
    background: #fff;

    .title {
      padding: 0.12rem 0 0.15rem 0;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .cell {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.14rem;
      margin-bottom: 0.13rem;

      .label {
        color: #1a1b1d;
      }

      .value {
        color: #b2b1b7;
      }

      .money {
        font-size: 0.16rem;
        color: #ff6445;
      }

      .copy {
        color: #1a1b1d;
      }
    }
  }

  .payment-details {
    background: #f7f8f8;
    border-radius: 0.06rem;
    margin-bottom: 0.14rem;
    padding: 0.1rem 0.12rem;

    .payment-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.12rem;
      line-height: 0.17rem;
      margin-bottom: 0.1rem;

      &:last-child {
        margin-bottom: 0;
      }

      .payment-item__label {
        color: #606266;
      }

      .payment-item__value {
        color: #ff6445;
      }
    }
  }

  .income-info {
    border-top: 0.08rem solid #f7f7f7;
    background: #fff;
    padding-bottom: 0.15rem;
  }
  .first-tip-header {
    font-weight: 600;
    font-size: 0.16rem;
    color: #1a1b1d;
    line-height: 0.22rem;
    padding: 0.14rem 0;
    text-align: center;
  }
  .first-tip-content {
    font-size: 0.14rem;
    color: #616568;
    line-height: 0.22rem;
    text-align: center;
    padding: 0.37rem 0;
  }
</style>
