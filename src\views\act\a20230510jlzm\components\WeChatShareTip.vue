<template>
  <van-popup v-model:show="show" @click="onClose">
    <div class="WeChatShareTip" @click.stop>
      <div class="arrows"></div>
      <div class="steps">
        <div class="step">
          <div class="number">1</div>
          <div class="step-title">复制文案</div>
          <div class="step-content">
            <div>{{ shareText }}</div>
            <textarea id="shareText" :value="shareText" readonly class="textarea" />
            <button
              ref="copyBtnRef"
              class="copy-btn"
              data-clipboard-action="copy"
              data-clipboard-target="#shareText"
            >
              {{ copyBtnText }}
            </button>
          </div>
        </div>
        <div class="step">
          <div class="number">2</div>
          <div class="step-title">
            点击右上角<img class="wx-more" src="../images/wx-more.png" />「分享到朋友圈」
          </div>
          <div class="step-content" style="border: none; padding-bottom: 0">
            <img
              style="width: 2.17rem; height: 0.66rem; margin-top: 0.04rem"
              src="../images/wx-share.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
    <div class="close" @click="onClose" />
  </van-popup>
</template>

<script setup>
  import { ref, watch, computed, nextTick } from 'vue'
  import Clipboard from 'clipboard'
  import { useParent } from '@vant/use'
  import setWxShare from '@/utils/weChat/share'
  import { getOssURL } from '@/common'
  import { baseURL } from '@/config'
  import { reqUserShareStats } from '../api'

  const { parent } = useParent('321ACT')

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const show = ref(props.modelValue || false)
  const clipboard = ref(null)
  const copyBtnRef = ref(null)
  const copyBtnText = ref('复制')

  const isSelf = computed(() => {
    return parent.actInfo.value.loginUserId === parent.coachShareInfo.value.coachUserId
  })

  const shareText = computed(() => {
    let name = parent.coachShareInfo.value.coachName
    if (isSelf.value) {
      return `我是${name}教练，我在参加爱教练明星教练点赞活动，诚邀各位亲朋好友为我点赞助力，每天可点赞一次，您的每一次点赞，都将助力我获得更多流量曝光，而您也将获得一份神秘礼物~`
    } else {
      return '我的教练在参加爱教练明星教练点赞活动，你点赞后即可获得神秘一份神秘礼物'
    }
  })

  const onClose = () => {
    emit('update:modelValue', false)
  }

  const handelOpen = (newVal) => {
    show.value = newVal
    if (newVal) {
      initialize()
    }
  }

  const setWeChatShareConfig = () => {
    const title = isSelf.value
      ? '我在参加爱教练明星教练点赞活动，诚邀各位亲朋好友为我点赞助力'
      : '帮我的教练点赞，你也可以获得神秘礼物喔~'

    setWxShare(
      {
        title: title,
        desc: '我在参与明星教练招募计划，谢谢你的一臂之力。',
        link: baseURL + '/act/a20230510jlzm?shareUserId=' + parent.coachShareInfo.value.coachUserId,
        imgUrl: getOssURL(parent.coachShareInfo.value.coachImage),
      },
      () => {
        onClose()
        let params = { coachUserId: parent.coachShareInfo.value.coachUserId }
        reqUserShareStats(params)
        parent.openContactPopup()
      },
    )
  }

  function initialize() {
    copyBtnText.value = '复制'

    setWeChatShareConfig()

    nextTick(() => {
      if (!clipboard.value) {
        clipboard.value = new Clipboard(copyBtnRef.value)
        clipboard.value.on('success', () => {
          copyBtnText.value = '复制成功'
        })
      }
    })
  }

  watch(() => props.modelValue, handelOpen)
</script>

<style lang="scss" scoped>
  .WeChatShareTip {
    width: 3rem;
    border-radius: 0.12rem;
    background: #ffffff;

    .steps {
      padding: 0.15rem 0.1rem;

      .step {
        padding-left: 0.21rem;
        position: relative;
      }

      .step + .step {
        margin-top: 0.1rem;
      }

      .number {
        width: 0.17rem;
        height: 0.17rem;
        line-height: 0.17rem;
        position: absolute;
        left: 0;
        top: 0;
        font-size: 0.12rem;
        color: #ffffff;
        text-align: center;
        background: #f45015;
        border-radius: 50%;
        margin-right: 0.04rem;
      }

      .step-title {
        font-size: 0.14rem;
        color: #1f1f1f;
      }

      .step-content {
        margin-top: 0.04rem;
        font-size: 0.14rem;
        color: #616568;
        border-bottom: 1px solid #eeeeee;
        padding-bottom: 0.15rem;
      }
    }

    .copy-btn {
      width: 100%;
      height: 0.35rem;
      line-height: 0.35rem;
      display: block;
      margin: 0.1rem auto 0 auto;
      background: #f33207;
      border-radius: 0.14rem;
      color: #ffffff;
      cursor: pointer;
    }

    .wx-more {
      width: 0.26rem;
      height: 0.13rem;
      margin: 0 0.02rem;
      vertical-align: text-top;
    }

    .textarea {
      width: 100%;
      position: absolute;
      left: -999999px;
    }
  }

  .close {
    width: 0.6rem;
    height: 0.6rem;
    margin: 0 auto;
    background: url('../images/active-popup-close.png') no-repeat;
    background-size: 100% 100%;
  }
</style>
