<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div v-if="orderDetail" class="container">
        <div class="order-state">
          <p>
            <span class="label">退款状态：</span>
            <span class="value">{{ refundStatus }}</span>
          </p>
          <p class="order-tip">
            <!-- 待处理 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'APPLIED'">
              还有<van-count-down class="count-down" :time="time" format="HH时mm分钟" />自动同意退款
            </template>
            <!-- 教练同意退款 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'AGREED'">
              你已同意学员的退款申请
            </template>
            <!-- 退款关闭 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'CANCELED'">
              学员已取消退款申请
            </template>
            <!-- 教练拒绝退款 -->
            <template v-if="orderDetail.afterSaleStatus.status === 'REFUSED'">
              你的拒绝理由：{{ orderDetail.refuseExplain }}
            </template>
          </p>
        </div>
        <div class="refund-details">
          <div class="title">退款信息</div>
          <div class="goods-details">
            <van-image
              class="goods-images"
              round
              fit="cover"
              width="0.74rem"
              height="0.74rem"
              :src="getOssURL(orderDetail.orderItem.imageUrl)"
            />
            <div class="goods-info">
              <div>
                <van-row justify="space-between" align="center">
                  <van-col class="goods-name omit">
                    {{ orderDetail.orderItem.spuName }}｜{{ orderDetail.orderItem.skuName }}
                  </van-col>
                  <van-col class="buy-price">¥{{ orderDetail.orderItem.totalAmount }}</van-col>
                </van-row>
              </div>
              <div class="goods-spec">
                授课方式：{{ orderDetail.orderItem.teachingWay.typeName }}
              </div>
              <div class="buy-number">课时数：{{ orderDetail.orderItem.quantity }}个课时</div>
            </div>
          </div>
          <div class="order-detail">
            <div class="cell">
              <div class="label">退款原因</div>
              <div class="value">{{ orderDetail.refundReason?.typeName }}</div>
            </div>
            <div class="cell">
              <div class="label">申请课时</div>
              <div class="value">{{ orderDetail.refundQuantity }}个课时</div>
            </div>
            <div class="cell">
              <div class="label">退款金额</div>
              <div class="value money">¥{{ orderDetail.refundAmount }}</div>
            </div>
            <div class="cell">
              <div class="label">退款描述</div>
              <div class="value">
                <span v-if="isDesc" class="black" @click="descShow = !descShow">
                  {{ descShow ? '收起' : '查看' }}
                  <van-icon class="arrow" :name="descShow ? 'arrow-down' : 'arrow'" />
                </span>
                <span v-else>无</span>
              </div>
            </div>
            <div v-show="descShow" class="refund-desc">
              <p>{{ orderDetail.refundExplain }}</p>
              <image-preview-wrapper>
                <div class="images">
                  <img
                    v-for="imgUrl in orderDetail.mediaUrls"
                    :key="imgUrl"
                    :src="getOssURL(imgUrl)"
                    alt=""
                  />
                </div>
              </image-preview-wrapper>
            </div>
            <div class="cell">
              <div class="label">申请时间</div>
              <div class="value">{{ orderDetail.applyTime }}</div>
            </div>
            <div class="cell">
              <div class="label">退款编号</div>
              <div class="value">
                <span>{{ orderDetail.id }} I </span>
                <copy-text class="copy" :text="orderDetail.id" @success="copySuccess">
                  复制
                </copy-text>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isOrderPending" class="footer">
          <!-- <a :href="'tel:' + orderDetail.studentInfo.mobile">
            <div class="tab-bar">
              <div class="icon" />
              <div class="label">联系学员</div>
            </div>
          </a> -->
          <button class="btn" @click="show = true">处理退款</button>
        </div>

        <audit-refund v-model="show" :data="refundForm" @agree="init" @reject="init" />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { Toast } from 'vant'
  import CopyText from '@/components/copy-text'
  import AuditRefund from './components/AuditRefund'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import { reqRefundOrderDetail } from '@/api/coach-worktable'
  import { getOssURL } from '@/common'
  import { getDateTime } from '@/utils/day'
  import { REFUND_STATUS } from '@/common/enum'

  const route = useRoute()
  const orderId = route.query.orderId || ''
  const descShow = ref(false)
  const show = ref(false)
  const orderDetail = ref(null)
  const time = ref(0)
  const refundForm = ref({})

  // 退款状态
  const refundStatus = computed(() => {
    if (!orderDetail.value) return ''
    return REFUND_STATUS[orderDetail.value.afterSaleStatus.status]
  })

  const copySuccess = () => {
    Toast('复制成功')
  }

  const setCountDown = () => {
    let pastTime = getDateTime() - getDateTime(orderDetail.value.applyTime)
    time.value = 86400000 - pastTime
  }

  const setRefundForm = () => {
    refundForm.value = {
      orderId: orderDetail.value.id,
      applyTime: orderDetail.value.applyTime,
      refundAmount: orderDetail.value.refundAmount,
      refundQuantity: orderDetail.value.refundQuantity,
      studentName: orderDetail.value.studentInfo.studentName,
    }
  }

  // 订单是否待处理
  const isOrderPending = computed(() => {
    if (!orderDetail.value) return false
    return orderDetail.value.afterSaleStatus.status === 'APPLIED'
  })

  // 是否有退款描述
  const isDesc = computed(() => {
    if (!orderDetail.value) return false
    return orderDetail.value.refundExplain || orderDetail.value.mediaUrls.length > 0
  })

  const getRefundOrderDetail = () => {
    return new Promise((resolve, reject) => {
      let params = { id: orderId }
      reqRefundOrderDetail(params)
        .then((res) => {
          resolve(res.data)
          orderDetail.value = res.data
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const init = async () => {
    orderDetail.value = await getRefundOrderDetail()
    if (isOrderPending.value) {
      setCountDown()
      setRefundForm()
    }
  }

  init()
</script>

<style lang="scss" scoped>
  .container {
    background: #fff;
    padding-bottom: 0.8rem;
  }

  .order-state {
    padding: 0.12rem 0.15rem;
    border-bottom: 1px solid #eeeeee;

    .order-tip {
      margin-top: 0.06rem;
      color: #616568;
    }

    .label {
      color: #616568;
    }

    .value {
      font-weight: bold;
      font-size: 0.18rem;
      color: #1a1b1d;
    }

    .time {
      font-size: 0.16rem;
      color: #ff6445;
    }
  }

  .refund-details {
    padding: 0 0.15rem;

    .title {
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
      padding: 0.11rem 0 0.08rem 0;
    }

    .goods-details {
      display: flex;
      background: #fff;

      .goods-images {
        border-radius: 0.06rem;
      }

      .goods-info {
        margin-left: 0.1rem;
        flex: 1;
      }

      .goods-name {
        width: 1.8rem;
        font-size: 0.14rem;
        color: #1a1b1d;
      }

      .buy-price {
        font-size: 0.14rem;
        color: #1a1b1d;
      }

      .goods-spec,
      .buy-number {
        font-size: 0.12rem;
        color: #616568;
        margin-top: 0.03rem;
      }
    }

    .order-detail {
      margin-top: 0.13rem;

      .refund-desc {
        background: #f3f3f3;
        border-radius: 0.06rem;
        padding: 0.1rem;
        color: #616568;
        margin-bottom: 0.13rem;

        .images {
          margin-top: 0.12rem;

          img {
            width: 0.72rem;
            height: 0.72rem;
            object-fit: cover;
            margin-bottom: 0.1rem;

            &:not(:nth-child(4n + 0)) {
              margin-right: 0.1rem;
            }
          }
        }
      }

      .arrow {
        margin-left: 0.06rem;
      }

      .black {
        color: #1a1b1d;
      }

      .cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 0.14rem;
        margin-bottom: 0.13rem;

        .label {
          color: #1a1b1d;
        }

        .value {
          color: #b2b1b7;
        }

        .money {
          font-size: 0.16rem;
          color: #ff6445;
        }

        .copy {
          color: #1a1b1d;
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    background-color: #fff;
    width: 3.75rem;
    height: 0.6rem;
    padding: 0 0.15rem;
    text-align: center;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    align-items: center;

    .tab-bar {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-right: 0.15rem;

      .label {
        font-size: 0.12rem;
        color: #1a1b1d;
        margin-top: 0.02rem;
      }

      .icon {
        width: 0.24rem;
        height: 0.24rem;
        background: url('../../../assets/images/coach-worktable/icon-phone-call-o.png') no-repeat;
        background-size: 100% 100%;
      }
    }

    .btn {
      flex: 1;
      padding: 0.09rem 0;
      font-size: 0.16rem;
      color: #ffffff;
      height: 0.4rem;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.2rem;
    }
  }

  .count-down {
    font-size: 0.16rem;
    color: #ff6445;
    display: inline-block;
    margin: 0 0.02rem;

    :deep(.van-count-down) {
      font-size: 0.14rem;
    }
  }
</style>
