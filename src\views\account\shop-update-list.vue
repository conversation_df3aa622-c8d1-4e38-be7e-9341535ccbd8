<template>
  <div class="coach-update-list">
    <ul class="setting-list">
      <li class="item feedback" @click="toShopForm(0)">
        <div class="title">基本信息</div>
        <div class="content">修改</div>
        <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
      </li>
      <li class="item feedback" @click="toShopForm(1)">
        <div class="title">资质信息</div>
        <div class="content">修改</div>
        <van-icon class="icon" name="arrow" color="#b2b1b7" size="0.16rem" />
      </li>
    </ul>
  </div>
</template>

<script setup>
  import {} from 'vue'
  import { useRouter } from 'vue-router'
  const router = useRouter()

  const toShopForm = (item) => {
    router.push({
      name: 'shopApplyForm',
      query: {
        channel: 'shopUpdateList',
        stepsNum: item,
      },
    })
  }
</script>

<style scoped lang="scss">
  .coach-update-list {
    height: 100vh;
    background-color: #fff;
    padding-bottom: 0.65rem;

    .setting-list {
      padding: 0 0.15rem;

      .item {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.18rem 0.02rem 0.18rem 0.1rem;
        border-bottom: 1px solid #eeeeee;
        max-height: 0.55rem;
        &::before {
          position: absolute;
          content: '';
          left: 0;
          width: 0.03rem;
          height: 0.14rem;
          background: #ff9b26;
          border-radius: 0.03rem;
        }

        .title {
          flex: 1;
          font-size: 0.14rem;
          font-weight: 600;
          color: #1a1b1d;
        }

        .content {
          flex: 1;
          font-size: 0.14rem;
          text-align: right;
          color: #b2b1b7;
        }

        .icon {
          margin-left: 0.02rem;
        }
      }
    }

    .fixed-page-footer {
      background-color: #fff;

      .logout-button {
        width: 3.45rem;
        height: 0.4rem;
        border-radius: 0.22rem;
        border: 1px solid #ff6445;
        margin: 0.1rem 0.15rem;
        color: #ff6445;
      }
    }
  }
</style>
