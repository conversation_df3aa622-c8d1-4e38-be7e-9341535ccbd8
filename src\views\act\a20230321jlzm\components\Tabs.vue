<template>
  <div class="tabs">
    <div class="tabs-header">
      <div
        class="tabs-item tabs-intro"
        :class="{ 'tabs-intro-active': modelValue === 0 }"
        @click="toggleTab(0)"
      />
      <div
        class="tabs-item tabs-top-n"
        :class="{ 'tabs-top-n-active': modelValue === 1 }"
        @click="toggleTab(1)"
      />
    </div>
    <div class="tabs-content">
      <div v-if="tabs[0]" v-show="modelValue === 0" class="tab-panel">
        <slot name="intro" />
      </div>
      <div v-if="tabs[1]" v-show="modelValue === 1" class="tab-panel">
        <slot name="top" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, watch } from 'vue'

  const props = defineProps({
    modelValue: {
      type: Number,
      default: 0,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const tabs = reactive({})

  const updateTabs = (value) => {
    tabs[value] = true
  }

  watch(() => props.modelValue, updateTabs, { immediate: true })

  const toggleTab = (index) => {
    tabs[index] = true
    emit('update:modelValue', index)
  }
</script>

<style lang="scss" scoped>
  .tabs {
    .tabs-header {
      width: 100%;
      height: 0.54rem;
      position: relative;
    }

    .tabs-item {
      width: 1.82rem;
      height: 0.4rem;
      cursor: pointer;
      position: absolute;
      bottom: 0;
      transition: 0.1s;
    }

    .tabs-intro {
      left: 0;
      background: url('../images/tab-1.png') no-repeat;
      background-size: 100% 100%;
    }

    .tabs-intro-active {
      width: 2.24rem;
      height: 0.54rem;
      z-index: 3;
      background: url('../images/tab-1-hl.png') no-repeat;
      background-size: 100% 100%;
    }

    .tabs-top-n {
      right: 0;
      background: url('../images/tab-2.png') no-repeat;
      background-size: 100% 100%;
    }

    .tabs-top-n-active {
      width: 2.24rem;
      height: 0.54rem;
      z-index: 3;
      background: url('../images/tab-2-hl.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
