// # 通用服务接口

import http from '@/utils/axios'

// 上传文件，接口返回临时地址
// notLogin 免登录
export const uploadFile = (params, notLogin) => {
  let api = `/generic-server/api/fileUpload/v1/tempFileUpload${notLogin ? '/nL' : ''}`
  return http.post(api, params, {
    timeout: 180000,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 直接上传文件到 oss文件服务
export const uploadFileOss = (params) => {
  return http.post('/generic-server/api/fileUpload/v1/fileUpload', params, {
    timeout: 180000,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 通过userid获取用户文章 用于教练/场馆账号
export const getUserArticles = (params) => {
  return http.post('/generic-server/api/articles/v1/getArticleByUserId/nL', params)
}

// 获取用户发布视频信息列表
export const getVideosByUserId = (params) => {
  return http.post('/generic-server/api/videos/v1/getVideosByUserId/nL', params)
}

// 通过userId查询培训信息列表
export const getTrainsByUserId = (params) => {
  return http.post('/generic-server/api/trains/v1/getTrainsByUserId/nL', params)
}
// 获取推荐文章列表
export const getRecommendArticleList = (params) => {
  return http.post('/generic-server/api/articles/v1/getRecommendArticleList/nL', params)
}

// 文章观看次数加 1
export const viewsPlusOne = () => {
  return http.post('/generic-server/api/articles/v1/addHits/nL')
}

// 获取文章详情
export const getArticleDetails = (params) => {
  return http.post('/generic-server/api/articles/v1/getArticleDetail/nL', params)
}

// 获取最新资讯/新闻列表
export const getLatestNewsList = (params) => {
  return http.post('/generic-server/api/articles/v1/getLastList/nL', params)
}

// 获取微信sdk授权
export const getWeChatSign = (params) => {
  return http.get('/generic-server/api/weChat/v1/jsapiSign/nL', { params })
}

// 获取视频文章列表
export const getVideoList = (params) => {
  return http.post('/generic-server/api/videos/v1/getLastList/nL', params)
}

// 获取视频文章详情信息
export const getVideoDetails = (params) => {
  return http.post('/generic-server/api/videos/v1/getVideoDetail/nL', params)
}

// 获取视频推荐列表
export const getVideoRecommendList = (params) => {
  return http.post('/generic-server/api/videos/v1/getRecommendVideoList/nL', params)
}

// 获取培训信息列表
export const getTrainsList = (params) => {
  return http.post('/generic-server/api/trains/v1/getTrainsList/nL', params)
}

// 获取培训信息详情
export const getTrainsDetail = (params) => {
  return http.get('/generic-server/api/trains/v1/getTrainsDetail/nL', { params })
}

// 获取教练招聘列表
export const getJobsList = (params) => {
  return http.post('/generic-server/api/jobs/v1/getJobsList/nL', params)
}

// 获取教练招聘详情
export const getJobsDetail = (params) => {
  return http.get('/generic-server/api/jobs/v1/getJobsDetail/nL', { params })
}

// 获取教练发布的培训信息帖子列表
export const getUserTrainsList = (params) => {
  return http.post('/generic-server/api/trains/v1/getUserTrainsList', params)
}

// 发布培训信息帖子
export const addUserTrains = (params) => {
  return http.post('/generic-server/api/trains/v1/addUserTrains', params)
}

// 更新帖子
export const updateUserTrains = (params) => {
  return http.post('/generic-server/api/trains/v1/updateUserTrains', params)
}

// 删除培训帖子
export const delUserTrains = (trainsId) => {
  return http.post('/generic-server/api/trains/v1/delUserTrains?trainsId=' + trainsId)
}

// 获取教学文章
export const getUserArticleList = (params) => {
  return http.post('/generic-server/api/articles/v1/getUserArticleList', params)
}

// 获取视频文章列表
export const getUserVideosList = (params) => {
  return http.post('/generic-server/api/videos/v1/getUserVideosList', params)
}

// 添加文章
export const addUserArticle = (params) => {
  return http.post('/generic-server/api/articles/v1/addUserArticle', params)
}

// 编辑文章
export const updateUserArticle = (params) => {
  return http.post('/generic-server/api/articles/v1/updateUserArticle', params)
}

// 删除文章
export const delUserArticle = (id) => {
  return http.post('/generic-server/api/articles/v1/delUserArticle?id=' + id)
}

// 发布视频文章
export const addUserVideos = (params) => {
  return http.post('/generic-server/api/videos/v1/addUserVideos', params)
}

// 更新视频文章
export const updateUserVideos = (params) => {
  return http.post('/generic-server/api/videos/v1/updateUserVideos', params)
}

// 删除视频文章
export const delUserVideos = (id) => {
  return http.post('/generic-server/api/videos/v1/delUserVideos?id=' + id)
}

// 获取推荐的教练招聘文章列表
export const getJobsRecommendList = (params) => {
  return http.post('/generic-server/api/jobs/v1/getRecommendList/nL', params)
}

// 获取推荐的培训文章列表
export const getTrainsRecommendList = (params) => {
  return http.post('/generic-server/api/trains/v1/getRecommendList/nL', params)
}

// 用户发布反馈意见
export const reqUserFeedback = (params) => {
  return http.post('/generic-server/api/opinionCollection/v1/release/nL', params)
}

// 获取体育分类
export const reqSportsClassify = () => {
  return http.get('/generic-server/api/categories/v1/getPolymerizationList/nL')
}

// 获取banner列表
export const getBanner = (params) => {
  return http.post('/generic-server/api/banner/v1/getHomePageBannerList/nL', params)
}

// 获取首页金刚区信息
export const getInletDiamondRegion = (params) => {
  return http.post('/generic-server/api/InletDiamondRegion/v1/adminList/nL', params)
}

// 获取首页金刚区信息
export const getTeachingContent = (params) => {
  return http.post('/generic-server/api/teachingContent/v1/list/nL', params)
}
// 获取协议
export const getAgreement = (params) => {
  return http.get('/generic-server/api/agreement/v1/getAgreementByTitle/nL', { params })
}
