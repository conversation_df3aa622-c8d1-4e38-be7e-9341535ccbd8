<template>
  <div class="wrapper">
    <steps v-if="!isStepForm" :active="stepsNum" class="mb05">
      <step>基本信息</step>
      <step>资质信息</step>
      <step>提交审核</step>
    </steps>
    <!-- 步骤二才会出现 -->
    <verify-notify
      v-if="shopFormData.checkStatus === 0 && stepsNum === 2"
      title="已提交申请，等待后台审核…"
      class="mt05"
    >
      <template #desc>
        <div class="again-edit" @click="againEdit">修改资料</div>
        <div class="gohome" @click="tohome">去首页</div>
      </template>
    </verify-notify>
    <register-status
      v-if="shopFormData.checkStatus === 0 && stepsNum === 2"
      class="mt05"
      :isShowTop="false"
      :show-cound-down="true"
    />

    <register-status
      v-if="shopFormData.checkStatus === 1 && stepsNum === 2"
      class="mt05"
      title="恭喜！你的申请已通过"
      icon="success"
      :show-cound-down="true"
    >
      <template #desc>
        <div class="again-edit" @click="toDetails">查看详情</div>
      </template>
    </register-status>

    <register-status
      v-if="shopFormData.checkStatus === 2 && stepsNum === 2"
      class="mt05"
      title="很遗憾～你的申请未能通过"
      icon="error"
      button-text="重新申请"
      @button-click="reapply"
    >
      <template #desc>
        <p class="register-failed">未通过原因：{{ shopFormData.checkRemark }}</p>
      </template>
    </register-status>

    <div class="form-wrapper" v-show="isHaveResult">
      <!-- 步骤二才会出现 -->
      <shop-basic-form v-show="stepsNum === 0" ref="basicFormRef" />
      <shop-specialty-form v-show="stepsNum === 1" :shopTitle="shopTitle" ref="specialtyFormRef" />
    </div>

    <div v-if="isStepForm" class="fixed-button" @click="submit">
      <button class="button">保存</button>
    </div>
    <div
      v-if="!isStepForm && shopFormData.checkStatus === 3"
      class="fixed-button"
      @click="nextStep"
    >
      <button class="button">下一步（{{ stepsNum + 1 }}/3）</button>
    </div>
  </div>
</template>

<script>
  export default { name: 'shopApplyForm' }
</script>

<script setup>
  import { ref, nextTick, onMounted } from 'vue'
  import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
  import { Step, Steps } from '@/components/steps'
  import VerifyNotify from '@/views/login/components/VerifyNotify'
  // import ApplyForm from "./components/ApplyShopAccountForm";
  import shopBasicForm from './components/shopBasicInfo.vue'
  import shopSpecialtyForm from './components/shopSpecialty.vue'
  import RegisterStatus from '@/views/common/components/register-status'
  import { getVenuesCheckRecord } from '@/api/coach-server'
  import { Toast } from 'vant'
  import { ossURLJoin } from '@/common'
  import { getVenuesIdByUserId, addVenuesCheckByExtendsLastRecord } from '@/api/coach-server'
  import useKeepAliveStore from '@/store/keepAlive'

  const router = useRouter()
  const route = useRoute()
  const basicFormRef = ref(null)
  const specialtyFormRef = ref(null)

  const stepsNum = ref(0)
  const shopFormData = ref({
    checkStatus: 0,
    checkRemark: null,
  })

  // 是否从个人中心跳转
  const isStepForm = ref(false)
  if (route.query.channel === 'shopUpdateList') {
    isStepForm.value = true
    stepsNum.value = +route.query.stepsNum
  }
  const submit = () => {
    // form.value.submit((valid, res) => {
    //   if (valid) {
    //     if (shopFormData.value.checkStatus === null) {
    //       shopFormData.value.checkStatus = 0;
    //     }
    //     stepsNum.value = 1;
    //     form.value.formData.id = res.data;
    //     Toast("提交成功");
    //   }
    // });
    if (stepsNum.value === 0) {
      tempRef.value = basicFormRef.value
    } else if (stepsNum.value === 1) {
      tempRef.value = specialtyFormRef.value
    }
    tempRef.value.formData.checkId = checkId.value
    tempRef.value.submit((valid) => {
      if (valid) {
        // 分步修改
        Toast('提交成功')
        setTimeout(() => {
          router.push({ name: 'shopUpdateList' })
        }, 500)
        return
      }
    })
  }

  const keepAliveStore = useKeepAliveStore()
  onBeforeRouteLeave((to) => {
    let pages = ['help']
    if (!pages.includes(to.name)) {
      // 卸载缓存
      keepAliveStore.removeKeepAlive('shopApplyForm')
    }
  })
  // // 基本资料回显
  const setBaseInfoForm = (data) => {
    nextTick(() => {
      basicFormRef.value.formData.longitude = data.longitude
      basicFormRef.value.formData.latitude = data.latitude
      // #场馆名称
      basicFormRef.value.formData.venueName = data.venueName
      //# 场馆电话
      basicFormRef.value.formData.venueMobile = data.venueMobile
      // #详细地址
      basicFormRef.value.formData.location = data.location
      // 省市区
      if (Array.isArray(data.areaCodes)) {
        let address = ''
        data.areaCodes.forEach((item) => {
          basicFormRef.value.formData.areaCodes.push(item.label)
          address += item.name + '/'
        })
        // 地址拼接
        basicFormRef.value.addressTxt = data.areaCodes[1]?.name + data.areaCodes[2]?.name
        basicFormRef.value.formatData.address = address.substring(0, address.length - 1)
        basicFormRef.value.formatData.lastAreaCode =
          data.areaCodes?.[data.areaCodes.length - 1]?.label
      }
      // #经营类型
      if (Array.isArray(data.categoriesDOList)) {
        let typeStr = ''
        let selectArr = []
        data.categoriesDOList.forEach((children) => {
          let arr = []
          children.forEach((item) => {
            arr.push(item.id)
          })

          selectArr.push(children[2])
          basicFormRef.value.formatData.skillArr = selectArr
          typeStr += children?.[2]?.name + '/'
          basicFormRef.value.formData.levelList.push(arr)
          basicFormRef.value.formatData.categoriesRequests[children?.[2]?.id] = children
        })

        basicFormRef.value.formatData.skillType = typeStr.substring(0, typeStr.length - 1)

        const skillList = typeStr.substring(0, typeStr.length - 1).split('/')
        // 标题拼接
        if (skillList.length === 1) {
          basicFormRef.value.trainTxt = skillList[0] + '场'
        } else if (skillList.length > 1) {
          basicFormRef.value.trainTxt = '体育馆'
        } else {
          basicFormRef.value.trainTxt = ''
        }
      }
      // #经营时间
      basicFormRef.value.formData.teachingTimeRange = data.teachingTimeRange.filter((n) => n)
      // #经营工作日
      if (data.openTimeWeek) {
        basicFormRef.value.formData.openTimeWeek = data.openTimeWeek.split(',')
      }
      // #负责人姓名
      basicFormRef.value.formData.contactName = data.contactName
      // #负责人电话
      basicFormRef.value.formData.contactMobile = data.contactMobile
      //# 场馆图片
      data.imageUrls &&
        data.imageUrls.forEach((url) => {
          basicFormRef.value.formData.imageUrls.push({
            url: ossURLJoin(url),
            path: url,
          })
        })

      //# 场馆视频
      data.videoUrl &&
        data.videoUrl.forEach((url) => {
          basicFormRef.value.formData.videoUrls.push({
            url: ossURLJoin(url),
            path: url,
          })
        })
    })
  }
  // // 场馆资质回显
  const setSpecialtyForm = (data) => {
    nextTick(() => {
      // #标题
      specialtyFormRef.value.formData.title = data.title
      // #场馆描述
      specialtyFormRef.value.formData.metaTitle = data.metaTitle

      //# 营业执照
      data.businessLicenseUrl &&
        data.businessLicenseUrl.forEach((url) => {
          specialtyFormRef.value.formData.bsUrls.push({
            url: ossURLJoin(url),
            path: url,
          })
        })
      // #场馆描述
      specialtyFormRef.value.formData.content = data.content
    })
  }

  const checkId = ref(null)
  const shopResulInfo = ref({})
  const venuesIdUserId = ref(null)
  const isHaveResult = ref(false)
  // 从注册/入驻场馆进入
  const initFormSingUp = () => {
    getVenuesCheckRecord().then((res) => {
      let { data } = res
      if (data) {
        isHaveResult.value = true
        shopResulInfo.value = data
        checkId.value = data.checkId
        //待审核
        // data.checkStatus = 2;
        if (data.checkStatus === 0) {
          stepsNum.value = 2
        }
        // 审核成功
        if (data.checkStatus === 1) {
          stepsNum.value = 2
          getVenuesIdByUserId().then((res) => {
            venuesIdUserId.value = res.data
          })
        }
        // 审核不通过
        if (data.checkStatus === 2) {
          stepsNum.value = 2
        }
        shopFormData.value.checkStatus = data.checkStatus
        shopFormData.value.checkRemark = data.checkRemark || ''
        if (data.checkStatus === 3 && !isStepForm.value) {
          stepsNum.value = 0
          setBaseInfoForm(data.venuesCheckRecordBaseInfoVO)
          setSpecialtyForm(data.venuesCheckRecordQualificationVO)
        }
      }
    })
  }
  // 从场馆资料列表进入
  const initFormUserInfo = () => {
    getVenuesCheckRecord().then((res) => {
      let { data } = res
      if (data) {
        isHaveResult.value = true
        shopResulInfo.value = data
        checkId.value = data.checkId
        if (route.query.channel === 'shopUpdateList') {
          if (stepsNum.value === 0) {
            basicFormRef.value.formatData.isShowTitle = false
            setBaseInfoForm(data.venuesCheckRecordBaseInfoVO)
          } else if (stepsNum.value === 1) {
            specialtyFormRef.value.formatData.isShowTitle = false
            setSpecialtyForm(data.venuesCheckRecordQualificationVO)
          }
          return
        }
      }
    })
  }

  // 重新申请
  const reapply = () => {
    stepsNum.value = 0
    shopFormData.value.checkStatus = 3
    setBaseInfoForm(shopResulInfo.value.venuesCheckRecordBaseInfoVO)
    setSpecialtyForm(shopResulInfo.value.venuesCheckRecordQualificationVO)
    addVenuesCheckByExtendsLastRecord().then((res) => {
      checkId.value = res.data.checkId
    })
  }

  // init();
  onMounted(() => {
    if (route.query.channel === 'shopUpdateList') {
      initFormUserInfo()
    } else {
      initFormSingUp()
    }
  })

  const tempRef = ref(null)
  const shopTitle = ref(null)
  // 入驻申请
  const nextStep = () => {
    if (stepsNum.value === 0) {
      tempRef.value = basicFormRef.value
      if (basicFormRef.value.formData.title) {
        shopTitle.value = basicFormRef.value.formData.title || ''
      } else {
        shopTitle.value = specialtyFormRef.value.formData.title
      }
    } else if (stepsNum.value === 1) {
      tempRef.value = specialtyFormRef.value
    }
    tempRef.value.formData.checkId = checkId.value

    tempRef.value.submit((valid) => {
      if (valid) {
        // 最后一步提交，进入待审核
        if (stepsNum.value === 1) {
          shopFormData.value.checkStatus = 0
        }
        stepsNum.value++
      }
    })
  }

  const tohome = () => {
    router.push({ name: 'home' })
  }
  const againEdit = () => {
    router.push({ name: 'shopUpdateList' })
  }
  const toDetails = () => {
    router.push({
      path: `/shop/details/${venuesIdUserId.value}`,
    })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/fixed-button.scss';
  .wrapper {
    padding-bottom: 0.8rem;
    .mt05 {
      margin-top: 0.05rem;
    }
    .mb05 {
      margin-bottom: 0.05rem;
    }

    .form-wrapper {
      background: #fff;

      .tip {
        text-align: center;
        // color: #ffa524;
        padding-top: 0.18rem;
      }
    }

    .register-failed {
      margin-top: 0.06rem;
      font-size: 0.12rem;
      color: #616568;
    }

    :deep(.van-steps--horizontal) {
      padding: 0.2rem 0.36rem;
    }

    :deep(.van-step__circle-container) {
      top: 0.3rem;
    }
    :deep(.van-step--horizontal .van-step__line) {
      top: 0.3rem;
    }
  }
  .gohome,
  .again-edit {
    display: inline-block;
    margin: 0.1rem auto 0;
    width: 0.96rem;
    height: 0.32rem;
    line-height: 0.3rem;
    text-align: center;
    background: #ffffff;
    color: #ff9b26;
    font-size: 0.14rem;
    border-radius: 0.16rem;
    border: 0.01rem solid #f28d00;
  }
  .again-edit {
    margin-right: 0.1rem;
  }
  .gohome {
    background: #ff9b26;
    color: #ffffff;
    border: none;
  }
</style>
