<template>
  <div>
    <div v-if="formatData.isShowTitle" class="title-box">
      <div class="item-title">基本信息</div>
      <div class="form-tip">
        说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
        表示审核必须完善的资料
      </div>
    </div>

    <div v-else class="form-tip" style="padding: 0.08rem 0 0 0.2rem; margin-bottom: 0">
      说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
      表示审核必须完善的资料
    </div>

    <van-form class="form">
      <van-field
        v-model="formData.venueName"
        required
        label="场馆名称"
        placeholder="请输入场馆名称"
      />
      <!-- <van-field
      v-model="formatData.skillType"
      @click="skillPickerShow = true"
      required
      readonly
      is-link
      label="经营类型"
      placeholder="请选择科目分类（可多选）"
    /> -->
      <div class="skillType">
        <div class="type-label">经营类型</div>
        <div class="skill-box">
          <div
            class="skill-tip"
            v-if="formatData.skillArr.length === 0"
            @click="skillPickerShow = true"
          >
            请选择科目分类（可多选）
          </div>
          <div v-for="item in formatData.skillArr.slice(0, 9)" :key="item" class="skill-item">
            {{ item.name }}
            <i class="icon icon-close2" @click="skillDelect(item)" />
          </div>
        </div>
        <van-icon
          class="skill-arrow"
          color="#999"
          size=".16rem"
          name="arrow"
          @click="skillPickerShow = true"
        />
      </div>
      <van-field
        v-model="formData.venueMobile"
        type="number"
        :maxlength="20"
        required
        label="场馆电话"
        placeholder="请输入场馆联系电话"
        @blur="moblieCheck"
      />
      <ErrorTip v-if="isShowAccountTip" :tipTxt="tipNameTxt" />
      <week-picker
        class="green-require"
        v-model="formData.openTimeWeek"
        label="营业时间"
        placeholder="请选择营业工作日"
        is-link
      />
      <hours-picker label=" " placeholder="请选择营业时间" v-model="formData.teachingTimeRange" />

      <van-field
        class="area green-require"
        v-model="formatData.address"
        is-link
        readonly
        @click="distPickerShow = true"
        label="所在地区"
        placeholder="请选择地区"
      />
      <div class="adress">
        <van-field
          v-model="formData.location"
          label=" "
          placeholder="请输入详细地址"
          @keyup="locationInput"
          @focus="adressFocus"
          @blur="adressBlur"
        >
        </van-field>
        <!-- <van-overlay :show="isShowAdress" @click="isShowAdress = false"> -->
        <van-overlay :show="isShowAdress" @click="isShowAdress = false"> </van-overlay>
        <div v-show="isShowAdress" class="adress-list">
          <div
            v-for="(item, index) in locationInfo"
            :key="index"
            class="adress-item"
            @click="selectAdress(item)"
          >
            <h3>{{ item.name }}</h3>
            <p>{{ item.address }}</p>
          </div>
        </div>
      </div>
      <div class="form-upload">
        <div class="label">
          <div class="label-title"><span class="green">*</span>场馆照片</div>
          <span class="desc">最多9张，&lt;10M/张，jpg/jpeg/png/gif格式</span>
        </div>
        <div class="value">
          <upload-file v-model="formData.imageUrls" preview-size="0.8rem" :max-count="9" />
        </div>
      </div>
      <div class="form-upload">
        <div class="label">
          <div class="label-title">视频</div>
          <span class="desc">仅1个，&lt;50M，mp4/mov格式</span>
        </div>
        <div class="value">
          <upload-file
            v-model="formData.videoUrls"
            preview-size="0.8rem"
            :max-count="1"
            accept="video/*"
          />
        </div>
      </div>
      <van-field
        v-model="formData.contactName"
        required
        label="负责人姓名"
        placeholder="请输入负责人姓名"
      />
      <van-field
        v-model="formData.contactMobile"
        required
        :maxlength="20"
        label="联系电话"
        placeholder="请输入负责人联系电话"
      />
      <!-- 地址选择器 -->
      <dist-picker
        :default-selected="formatData.lastAreaCode"
        v-model:show="distPickerShow"
        @finish="handledAddressFinish"
      />
      <!-- 技能选择器 -->
      <skill-type
        :selectedDefault="formatData.categoriesRequests"
        :maxSelectNum="9"
        v-model:show="skillPickerShow"
        @confirm="handleSkillConfirm"
      />
    </van-form>
  </div>
</template>

<script setup>
  import ErrorTip from './ErrorTip.vue'
  import { Toast } from 'vant'
  import DistPicker from '@/components/dist-picker'
  import WeekPicker from '@/components/form/week-picker'
  import HoursPicker from '@/components/form/hours-picker'
  import SkillType from '@/components/skill-type'
  import { getUserLocation, getLocationInfo } from '@/common'
  import { checkEmpty, validate } from '@/utils/validate'
  import Schema from 'async-validator'
  import { editVenuesBaseInfo } from '@/api/coach-server'
  import UploadFile from '@/components/upload-file'

  const distPickerShow = ref(false)
  const skillPickerShow = ref(false)

  const isShowAdress = ref(false)
  import { ref, reactive, onMounted } from 'vue'
  const formData = reactive({
    venueName: '', // 场馆名称
    venueMobile: '', // 场馆联系电话
    location: '', // 详细地址
    longitude: '', // 经度
    latitude: '', // 纬度
    areaCodes: [], //省市区显
    levelList: [], // 经营类型
    openTimeWeek: [], // 工作日
    teachingTimeRange: [], // 工作日时间范围
    contactName: '', //负责人姓名
    contactMobile: '', // 负责人电话
    imageUrls: [], // 场馆照片
    videoUrls: [], // 视频
    title: '',
  })

  // 格式化后的展示数据
  const formatData = reactive({
    address: '',
    lastAreaCode: '',
    skillType: '', // 经营类型
    categoriesRequests: {},
    skillArr: [],
    isShowTitle: true,
  })

  // 校验必填项
  const isShowAccountTip = ref(false)
  const tipNameTxt = ref('请输入场馆电话')
  const moblieCheck = () => {
    if (!formData.venueMobile) {
      isShowAccountTip.value = true
      tipNameTxt.value = '请输入场馆电话'
    } else if (
      !validate('landline', formData.venueMobile) &&
      !validate('mobile', formData.venueMobile)
    ) {
      isShowAccountTip.value = true
      tipNameTxt.value = '请输入正确的场馆电话'
    } else {
      isShowAccountTip.value = false
    }
  }
  const validator = new Schema({
    venueName: [
      { message: '请输入场馆名称', validator: checkEmpty },
      { message: '场馆名不能超过20个字符', max: 20 },
    ],
    venueMobile: { message: '请输入场馆联系电话', validator: checkEmpty },
    levelList: {
      message: '请选择经营类型',
      validator: function (rule, value) {
        if (!value) return false
        return value.length !== 0
      },
    },
    contactName: [
      { message: '请输入负责人姓名', validator: checkEmpty },
      { message: '姓名不能超过20个字符', max: 20 },
    ],
    contactMobile: { message: '请输入负责人电话', validator: checkEmpty },
    // address: { message: "请输入负责人电话", validator: checkAddress },
  })

  const skillTreeArr = ref([])

  // const skillArr = ref([]);

  const locationInfo = ref([])
  const cityName = ref([])
  onMounted(async () => {
    await getUserLocation().then((res) => {
      cityName.value = res.data?.city?.split('市')[0]
    })
  })

  const adressBlur = () => {
    // document.getElementsByClassName("form")[0].scrollIntoView();
  }

  const adressFocus = () => {
    document.getElementsByClassName('adress')[0].scrollIntoView()
    if (formData.location) {
      isShowAdress.value = true
    }
    // document.getElementsByClassName("area")[0].scrollIntoView();
    // document.getElementsByClassName("adress")[0].previousSibling.scrollIntoView();
  }
  const timer = ref(null)
  const locationInput = (e) => {
    // console.log(e.target.value);
    if (timer.value) {
      clearTimeout(timer.value)
    }
    timer.value = setTimeout(() => {
      const params = {
        keywords: e.target.value || '',
        city: cityName.value || '',
      }
      if (selectedCity.value) {
        params['city'] = selectedCity.value
      }
      getLocationInfo(params).then((res) => {
        locationInfo.value = []
        if (res.status === 200) {
          res.data.tips.map((item) => {
            const obj = {}
            obj['name'] = item.name
            // obj["address"] = item.pname + item.cityname + item.adname + item.address;
            obj['address'] = item.district + item.address
            // obj["addrDetail"] = item.address;
            obj['addrDetail'] = item.name
            obj['adcode'] = item.adcode
            obj['district'] = item.district
            if (item.location.length > 0) {
              obj['longitude'] = item.location.split(',')[0] //经度
              obj['latitude'] = item.location.split(',')[1] //纬度
            }
            locationInfo.value.push(obj)
          })
          isShowAdress.value = locationInfo.value.length === 0 ? false : true
        }
      })
      timer.value = undefined
    }, 300)
  }

  // 智能提示选择地址
  const selectAdress = (item) => {
    formData.areaCodes = []
    const province = item.district.split('省')[0] + '省'
    const city = item.district.split('省')[1].split('市')[0] + '市'
    const area = item.district.split('省')[1].split('市')[1]
    formatData.address = province + '/' + city + '/' + area

    formData.location = item.addrDetail
    formData.longitude = +item.longitude
    formData.latitude = +item.latitude
    formatData.lastAreaCode = item.adcode
    formData.title = item.district.split('省')[1]
    if (trainTxt.value) {
      formData.title = addressTxt.value + trainTxt.value
    }

    formData.areaCodes.push(item.adcode.substring(0, 2) + '0000')
    formData.areaCodes.push(item.adcode.substring(0, 4) + '00')
    if (item.adcode.substring(4) !== '00') {
      formData.areaCodes.push(item.adcode)
    }

    isShowAdress.value = false
  }

  // 处理地址选择完毕
  const selectedCity = ref('')
  const addressTxt = ref('')
  const handledAddressFinish = (params) => {
    selectedCity.value = params.selectedOptions[1].adName
    if (params) {
      formData.areaCodes = []
      let { selectedOptions } = params
      selectedOptions.forEach((item) => {
        formData.areaCodes.push(item.adCode)
      })
      formatData.address = params.address
      if (formData.areaCodes.length === 2) {
        formData.areaCodes.push('')
      }
      formData.location = ''
      formData.longitude = ''
      formData.latitude = ''
    }
    distPickerShow.value = false
    const addStr = formatData.address.split('/')
    if (addStr[2]) {
      addressTxt.value = addStr[1] + addStr[2]
    } else {
      addressTxt.value = addStr[1]
    }
    formData.title = addressTxt.value
    if (trainTxt.value) {
      console.log(trainTxt.value, 'trainTxt.value')
      formData.title = addressTxt.value + trainTxt.value
    }
    console.log(formData.title, 'addressTxt.value')
  }

  const trainTxt = ref(null)
  // 选择运动分类回调
  const handleSkillConfirm = ({ selected }) => {
    console.log(selected, 'selected')
    formatData.skillArr = []
    formData.levelList = []
    skillTreeArr.value = selected
    let typeStr = ''
    if (Array.isArray(selected)) {
      selected.forEach((children) => {
        let arr = []
        children.forEach((item) => {
          arr.push(item.id)
        })

        formData.levelList.push(arr)
        formatData.skillArr.push(children[2])
        // console.log(formatData.skillArr, "formatData.skillArr");
        typeStr += children[2].name + '/'
        console.log(typeStr)
      })
      if (formatData.skillArr.length === 1) {
        trainTxt.value = formatData.skillArr[0].name + '场'
      } else if (formatData.skillArr.length > 1) {
        trainTxt.value = '体育馆'
      } else {
        trainTxt.value = ''
      }
    }
    // 标题联动
    formData.title = trainTxt.value
    if (addressTxt.value) {
      formData.title = addressTxt.value + trainTxt.value
    }

    formatData.skillType = typeStr.substring(0, typeStr.length - 1)
    skillPickerShow.value = false
  }

  const handleFormData = () => {
    let form = JSON.parse(JSON.stringify(formData))
    form.openTimeWeek = form.openTimeWeek.join(',')
    form.imageUrls = form.imageUrls.map((file) => file.path)
    form.videoUrls = form.videoUrls.map((file) => file.path)
    return form
  }

  const tempIndex = ref(null)
  // 删除营业类型
  const skillDelect = (item) => {
    tempIndex.value = null
    // console.log(item);
    console.log(formData.levelList, 'formData.levelList')
    formData.levelList.forEach((cItem, index) => {
      console.log(cItem, 'cItem')
      console.log(cItem[2], item.id)
      if (cItem[2] === item.id) {
        tempIndex.value = index
      }
    })
    console.log(tempIndex.value, 'tempIndex')

    formatData.skillArr = formatData.skillArr.filter((skillItem) => {
      return skillItem.id !== item.id
    })

    if (formatData.skillArr.length === 1) {
      trainTxt.value = formatData.skillArr[0].name + '场'
    } else if (formatData.skillArr.length > 1) {
      trainTxt.value = '体育馆'
    } else {
      trainTxt.value = ''
    }

    // 标题联动
    formData.title = trainTxt.value
    if (addressTxt.value) {
      formData.title = addressTxt.value + trainTxt.value
    }

    if (tempIndex.value || tempIndex.value === 0) {
      formData.levelList.splice(tempIndex, 1)
      delete formatData.categoriesRequests[item.id]
      console.log('删除', formData.levelList)
    }
  }

  const submit = (callback) => {
    let form = handleFormData()
    const formCheckAllObj = Object.assign(form, formatData)
    // console.log(formCheckAllObj, "formCheckAllObj");
    validator
      .validate(formCheckAllObj)
      .then(() => {
        editVenuesBaseInfo(form)
          .then((res) => {
            callback(true, res)
          })
          .catch((error) => {
            callback(false, error)
          })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  defineExpose({
    formData,
    formatData,
    submit,
    addressTxt,
    trainTxt,
  })
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';

  @include Icon('close2', 0.11rem, 0.11rem);

  .title-box {
    border-bottom: 1px solid #eee;
    padding: 0 0.1rem;
  }

  .form-tip {
    padding-left: 0.12rem;
    font-size: 0.12rem;
    margin-bottom: 0.12rem;
  }

  .red {
    color: #ff6445;
  }

  .green {
    color: #0abb08;
  }

  .green-require {
    :deep(.van-field__label) {
      &::before {
        margin-right: 2px;
        color: #0abb08;
        content: '*';
      }
    }
  }

  .item-title {
    position: relative;
    padding: 0.12rem 0.12rem 0.06rem 0.12rem;
    font-size: 0.14rem;
    font-weight: 600;
    color: #616568;
    &::before,
    &::after {
      position: absolute;
      content: '';
      display: block;
    }

    //&::after {
    //  bottom: 0;
    //  left: -3%;
    //  width: 105.5%;
    //  height: 0.01rem;
    //  background-color: #eee;
    //}

    &::before {
      left: 0;
      top: 50%;
      transform: translate(0, -60%);
      width: 0.03rem;
      height: 0.14rem;
      background-color: #ff9b26;
      border-radius: 0.03rem;
    }
  }
  .divide {
    position: absolute;
    width: 105.5%;
    top: -12%;
    left: -3%;

    height: 0.05rem;
    background: #f8f7f7;
  }

  .form {
    padding: 0 0.1rem;

    :deep(.van-cell) {
      padding: 0.17rem 0;
      font-size: 0.14rem;
      border-bottom: 1px solid #eeeeee;
      //align-items: center;

      &:after {
        border: none;
      }
    }

    :deep(.van-field__label) {
      width: 1rem;
      padding-left: 0.1rem;
      color: #453938;
    }

    :deep(.van-radio--horizontal) {
      margin-right: 0.48rem;
    }

    //:deep(.van-uploader__upload) {
    //  width: 0.8rem;
    //  height: 0.8rem;
    //}
    //
    //:deep(.van-uploader__preview-image) {
    //  width: 0.8rem;
    //  height: 0.8rem;
    //}

    .textarea-wrapper {
      :deep(.van-cell__value) {
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 0.04rem;
        padding: 0.08rem;
      }

      :deep(.van-field__word-limit) {
        font-size: 0.12rem;
        color: #ccc;
      }

      :deep(.van-field__control--min-height) {
        min-height: 0.6rem;
      }
    }
  }
  .tips {
    width: 100%;
    flex-shrink: 0;
    font-size: 0.12rem;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 0.05rem;
  }
  :deep(.van-field__control--custom) {
    flex-wrap: wrap;
  }
  .form-upload {
    display: flex;
    flex-direction: column;
    padding: 0.17rem 0;
    font-size: 0.14rem;
    border-bottom: 1px solid #eeeeee;

    .label {
      display: flex;
      align-items: center;

      .label-title {
        width: 1rem;
        padding-left: 0.16rem;
      }
    }

    .desc {
      font-size: 0.1rem;
      color: #959595;
    }

    .value {
      margin-top: 0.05rem;
      padding-left: 1.12rem;
    }
  }
  .adress {
    position: relative;
    -webkit-overflow-scrolling: touch;
    .adress-list {
      position: absolute;
      padding: 0.12rem;
      z-index: 99;
      top: 0.58rem;
      right: 0rem;
      width: 2.44rem;
      height: 2rem;
      background-color: #fff;
      border-radius: 0.1rem;
      border: 0.01rem solid #eee;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(92, 92, 92, 0.1);
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      .adress-item {
        margin-top: 0.1rem;
        h3 {
          font-size: 0.14rem;
          color: #ff9b26;
        }
        p {
          font-size: 0.12rem;
          color: #999;
        }
        &:first-child {
          margin-top: 0;
        }
      }
    }
  }
  .adress :deep(.van-overlay) {
    // position: static;
    background-color: transparent;
  }
  .skillType {
    padding: 0.17rem 0;
    // padding-top: 0.17rem;
    // padding-bottom: 0.07rem;
    font-size: 0.14rem;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    .skill-arrow {
      vertical-align: middle;
    }
    .type-label {
      position: relative;
      padding-left: 0.15rem;
      flex-shrink: 0;
      width: 0.8rem;
      &::before {
        margin-right: 2px;
        position: absolute;
        content: '*';
        left: 0.08rem;
        top: 0.01rem;
        color: #ee2d2b;
      }
    }
    .skill-tip {
      color: #c8c9cc;
    }
    .skill-box {
      margin-left: 0.3rem;
      flex-shrink: 0;
      width: 2.28rem;
      display: flex;
      flex-wrap: wrap;
      .skill-item {
        margin-right: 0.06rem;
        margin-bottom: 0.1rem;
        padding: 0.04rem 0.08rem;
        background: rgba(255, 165, 36, 0.1);
        border-radius: 0.04rem;
        border: 0.01rem solid #ffa524;
        font-size: 0.13rem;
        color: #ffa524;
        i {
          vertical-align: middle;
          margin-bottom: 0.03rem;
        }
      }
    }
  }
  :deep(.error) {
    padding-left: 0.1rem;
  }
  :deep(.van-cell__right-icon) {
    font-size: 0.16rem;
  }
</style>
