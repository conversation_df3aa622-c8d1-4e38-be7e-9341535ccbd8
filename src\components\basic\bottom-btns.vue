<template>
  <div class="complete-bottom-btns">
    <button
      v-if="isOnlyOne == 'all' || isOnlyOne == 'left'"
      :class="[{ block: isOnlyOne == 'left', plain: isleftPlain }]"
      :disabled="leftDisabled"
      @click="leftClick"
    >
      {{ leftBtnText }}
    </button>
    <button
      v-if="isOnlyOne == 'all' || isOnlyOne == 'right'"
      :class="[{ block: isOnlyOne == 'right', plain: isRightPlain }]"
      :disabled="rightDisabled"
      @click="rightClick"
    >
      {{ rightBtnText }}
    </button>
  </div>
</template>

<script setup>
  const emit = defineEmits(['leftClick', 'rightClick'])
  const props = defineProps({
    leftBtnText: {
      type: String,
      default: '上一步',
    },
    rightBtnText: {
      type: String,
      default: '下一步',
    },
    isOnlyOne: {
      // 是否只有1个按钮
      type: String,
      default: 'all', // 传入left，则保留左边，传入right则保留右边
    },
    leftDisabled: {
      type: Boolean,
      default: false,
    },
    rightDisabled: {
      type: Boolean,
      default: false,
    },
    isleftPlain: {
      // 左朴素按钮
      type: Boolean,
      default: false,
    },
    isRightPlain: {
      // 右朴素按钮
      type: Boolean,
      default: false,
    },
  })
  console.log(props)
  const leftClick = () => {
    emit('leftClick')
  }

  const rightClick = () => {
    emit('rightClick')
  }
</script>

<style scoped lang="scss">
  .complete-bottom-btns {
    position: fixed;
    bottom: 0;
    left: var(--window-left);
    right: var(--window-right);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.1rem 0.15rem;
    background: #fff;

    button {
      background: #ff9b26;
      height: 0.4rem;
      width: 1.65rem;
      text-align: center;
      color: #fff;
      border: 0;
      border-radius: 100px;
      font-size: 0.15rem;
      font-weight: 600;
      &.block {
        width: 100%;
        display: block;
      }
      &:not(.block):first-child {
        margin-right: 0.08rem;
      }
      &:disabled {
        background: rgba(185, 180, 180, 1);
        opacity: 1;
      }
      &.plain {
        border: 1px solid #ff9b26;
        background: #fff;
        color: #ff9b26;
        &:disabled {
          opacity: 1;
          border: 1px solid #a29b9b;
          color: rgba(23, 6, 6, 0.4);
        }
      }
    }
  }
</style>
