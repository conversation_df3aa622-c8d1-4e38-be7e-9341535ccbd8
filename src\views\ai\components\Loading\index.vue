<template>
  <div class="loading-wrapper">
    <img class="loading-icon" :src="imageUrl('loading.gif')" alt="" />
    <img class="loading-header" :src="imageUrl('loading_header_icon.png')" alt="" />
    <p class="text">请稍等，正在为您输出中…</p>
    <p v-if="props.showBtn" class="cancel" @click="$emit('cancel')">取消</p>
  </div>
</template>

<script setup>
  import { ossURL } from '@/config/index.js'
  const props = defineProps({
    showBtn: {
      type: Boolean,
      default: false,
    },
  })
  const imageUrl = (url) => {
    return ossURL + '/h5-assets/ai/saleAssistant/' + url
  }
</script>

<style lang="scss" scoped>
  .loading-wrapper {
    position: fixed;
    z-index: 19;
    left: 50%;
    bottom: 20%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.14rem;
    color: #ffffff;
    line-height: 0.22rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.1rem;
    padding: 0.1rem 0.12rem;
    .loading-icon {
      width: 0.24rem;
      height: 0.24rem;
    }

    .loading-header {
      position: absolute;
      top: -0.37rem;
      left: 50%;
      transform: translateX(-50%);
      width: 0.62rem;
      height: 0.48rem;
    }
    .text {
      margin-left: 0.04rem;
      margin-right: 0.1rem;
      white-space: nowrap;
    }
    .cancel {
      cursor: pointer;
      white-space: nowrap;
    }
  }
</style>
