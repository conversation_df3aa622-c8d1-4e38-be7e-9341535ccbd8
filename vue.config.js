const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  productionSourceMap: process.env.VUE_APP_RUN_ENV !== 'production',
  devServer: {
    historyApiFallback: true,
    proxy: {
      '/proxy': {
        changeOrigin: true, //允许跨域
        // target: "http://127.0.0.1:4523/mock/1906337",
        // target: "http://127.0.0.1:4523/m1/1936526-0-default",
        // target: "http://10.0.0.25:9001", //钦浩
        target: 'http://35-ijiaolian-api.yzwill.cn', // 测试环境（35服务器）
        // target: "https://api.ijiaolian.com", // 正式环境
      },
      '^/api': {
        // target: 'http://192.168.137.74:8888',
        target: 'http://test-ai.ijiaolian.com',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api\/proxy/, '/api'),
      },
    },
  },
  chainWebpack: (config) => {
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap((options) => {
        options.compilerOptions = {
          ...options.compilerOptions,
          isCustomElement: (tag) => tag.startsWith('ijiaolian-') || tag.startsWith('wx-'),
        }
        return options
      })
  },
})
