<template>
  <div ref="previewBoxRef" class="image-preview-wrapper" @click="handleImgPreview">
    <slot />
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { ImagePreview } from 'vant'

  const props = defineProps({
    multiple: {
      type: Boolean,
      default: false,
    },
  })

  const previewBoxRef = ref(null)

  const handleMultiplePreview = (event) => {
    const elem = previewBoxRef.value
    if (!elem) return
    const imageElems = elem.querySelectorAll('img')
    const images = Array.prototype.map.call(imageElems, (elem) => elem.src)
    const curImgElemIndex = Array.prototype.findIndex.call(
      imageElems,
      (elem) => event.target === elem,
    )

    ImagePreview({
      images: images,
      closeable: true,
      startPosition: curImgElemIndex,
    })
  }

  const handleImgPreview = (event) => {
    let tagName = event.target.nodeName
    if (tagName === 'IMG') {
      // 多图预览模式
      if (props.multiple) {
        handleMultiplePreview(event)
        return
      }

      let img = event.target.src
      ImagePreview({
        images: [img],
        closeable: true,
      })
    }
  }
</script>
