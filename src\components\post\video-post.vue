<template>
  <div class="video-post feedback">
    <div class="video-time">
      <span class="f10">{{ sliceStr(post.releaseTime, 0, 16) || '-' }}</span>
    </div>
    <div class="content">
      <div class="video-info">
        <div class="video-title">{{ post.title }}</div>
        <div class="video-other">
          <div class="video-tag">
            <span class="f10">视频</span>
          </div>
          <div class="video-author">
            <span class="author-max-w f10 omit">{{ post.realName }}</span>
          </div>
          <div class="video-pageviews">
            <span class="f10">{{ formatPagesview(post.readCount) }}&nbsp;浏览</span>
          </div>
        </div>
      </div>
      <div class="video-cover">
        <div class="icon-play"></div>
        <img :src="coverImg" alt="封面" />
      </div>
    </div>
    <slot name="state"></slot>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { ossURLJoin } from '@/common'
  import { sliceStr } from '@/utils'

  let props = defineProps({
    post: Object,
  })

  // 浏览量
  const formatPagesview = (count) => {
    if (count > 999) {
      return '999+'
    }
    return count
  }

  // 取第一张封面图
  const coverImg = computed(() => {
    if (Array.isArray(props.post.coverImage)) {
      if (props.post.coverImage.length > 0) {
        return ossURLJoin(props.post.coverImage[0])
      }
    }
    return require('../../assets/images/default-img.png')
  })
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  .video-post {
    display: flex;
    flex-direction: column;
    padding: 0.1rem 0.12rem 0.1rem 0.15rem;
    border-bottom: 1px solid #f2f2f2;

    .content {
      display: flex;
    }

    .video-info {
      flex: 1;
      padding-right: 0.1rem;
    }

    .video-title {
      min-height: 0.44rem;
      font-size: 0.15rem;
      line-height: 0.24rem;
      color: #414141;
      @include TextEllipsis(2);
    }

    .video-other {
      margin-top: 0.12rem;
      color: #979797;

      span {
        display: inline-block;
      }
    }

    .video-tag {
      display: inline;
      border-radius: 0.03rem;
      background: rgba(#99cdfe, 0.34);
      padding: 0.01rem 0.04rem;
      color: #0083fc;
    }

    .video-author {
      display: inline;
      margin-left: 0.04rem;

      span {
        vertical-align: middle;
      }
    }

    .author-max-w {
      max-width: 0.6rem;
    }

    .video-pageviews {
      display: inline;
      margin-left: 0.06rem;
    }

    .video-time {
      margin-bottom: 0.04rem;
      span {
        color: #b2b1b7;
        font-size: 0.12rem;
        display: inline-block;
        transform: scale(0.92);
        transform-origin: left;
      }
    }

    .video-cover {
      width: var(--i-article-cover-w);
      height: var(--i-article-cover-h);
      object-fit: cover;
      border-radius: 0.04rem;
      border: 1px solid #ebebeb;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .icon-play {
        width: 0.3rem;
        height: 0.3rem;
        background: url('../../assets/images/icon/play-btn.png') no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
</style>
