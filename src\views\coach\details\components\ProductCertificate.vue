<template>
  <Panel class="product-certificate-panel" title="荣誉证书">
    <Skeleton class="skeleton" :loading="loading" animated :count="2">
      <template #skeleton>
        <SkeletonImage height="1.1rem" />
        <SkeletonRow width="80%" />
      </template>
      <template #content>
        <div class="product-certificate">
          <div
            v-for="(item, index) in certificate"
            :key="item.url"
            class="product-certificate__item"
          >
            <div class="product-certificate__item-image" @click="handelPreview(index)">
              <div
                :class="['product-certificate__item-image__banner', 'banner--color' + item.type]"
              >
                <span>{{ item.tagName }}</span>
              </div>
              <Image :src="item.url" width="100%" height="100%" fit="cover" block />
            </div>
            <div class="product-certificate__item-name">
              {{ item.name }}
            </div>
          </div>
          <Empty v-if="certificate.length === 0" description="暂无证书" />
        </div>
      </template>
    </Skeleton>
  </Panel>
</template>

<script setup>
  import { computed, unref } from 'vue'
  import { ImagePreview } from 'vant'
  import { Skeleton, SkeletonImage, SkeletonRow } from '@/components/skeleton'
  import Panel from './Panel'
  import Image from '@/components/image'
  import Empty from '@/components/empty'

  const props = defineProps({
    certificate: Array,
    loading: Boolean,
  })

  const images = computed(() => {
    return props.certificate.map((item) => item.url)
  })

  function handelPreview(index) {
    ImagePreview({
      images: unref(images),
      startPosition: index,
    })
  }
</script>

<style scoped lang="scss">
  .product-certificate {
    padding: 0.15rem;
  }

  .product-certificate__item {
    width: 50%;
    display: inline-flex;
    flex-direction: column;

    &:not(:nth-last-of-type(-n + 2)) {
      margin-bottom: 0.12rem;
    }

    &:nth-of-type(2n + 1) {
      padding-right: 0.06rem;
    }

    &:nth-of-type(2n) {
      padding-left: 0.06rem;
    }
  }

  .product-certificate__item-image {
    width: 100%;
    height: 1.1rem;
    border-radius: 0.04rem;
    overflow: hidden;
    position: relative;
  }

  .product-certificate__item-image__banner {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    border-radius: 0.04rem 0 0.08rem 0;
    color: #fff;
    text-align: center;
    background: linear-gradient(90deg, #81afff 0%, #4977fe 100%);

    span {
      display: inline-block;
      font-size: 0.12rem;
      transform: scale(0.84);
      transform-origin: center;
    }
  }

  .product-certificate__item-name {
    color: rgba(0, 0, 0, 0.5);
    margin-top: 0.08rem;
    font-size: 0.12rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-wrap: break-word;
    hyphens: auto;
  }

  .banner--color1 {
    background: linear-gradient(90deg, #81afff 0%, #4977fe 100%);
  }

  .banner--color2 {
    background: linear-gradient(90deg, #e1bb7c 0%, #cb9c4c 100%);
  }

  .banner--color3 {
    background: linear-gradient(90deg, #ff6e3a 0%, #ffa134 100%);
  }

  :deep(.skeleton) {
    .ijl-skeleton {
      padding: 0.15rem;
    }

    .ijl-skeleton-item {
      &:nth-of-type(2n + 1) {
        padding-right: 0.06rem;
      }

      &:nth-of-type(2n) {
        padding-left: 0.06rem;
      }
    }
  }
</style>
