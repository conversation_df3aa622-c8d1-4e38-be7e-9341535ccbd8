<template>
  <div class="post-manage">
    <button class="manage-button feedback" @click="show = true">管理</button>
  </div>
  <van-action-sheet
    class="action-sheet"
    teleport="#app"
    v-bind="attrs"
    v-model:show="show"
    :actions="actions"
    cancel-text="取消"
    close-on-click-action
  />
</template>

<script setup>
  import { ref, useAttrs } from 'vue'

  const attrs = useAttrs()
  const show = ref(false)
  const actions = [
    { name: '修改', code: 'edit' },
    { name: '删除', code: 'delete', color: 'red' },
  ]
</script>

<style lang="scss" scoped>
  .post-manage {
    width: 3.75rem;
    line-height: 0.56rem;
    background-color: #fff;
    box-shadow: 0px -0.02rem 0.04rem 0 rgba(0, 0, 0, 0.06);
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    text-align: right;
    padding: 0 0.15rem;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);

    .manage-button {
      width: 0.6rem;
      height: 0.36rem;
      font-size: 0.14rem;
      border-radius: 0.23rem;
      border: 1px solid #ff9b26;
      color: #ff9b26;
    }
  }
  .red {
    color: red;
  }
</style>
