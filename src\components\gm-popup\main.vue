<template>
  <Popup style="background: transparent" v-model:show="show">
    <div class="content">
      <div class="close" @click="show = false"><Icon name="cross" /></div>
      <div class="main">
        <div class="title">{{ title }}</div>
        <div class="desc">{{ desc }}</div>
        <div class="qrcode">
          <img src="../../assets/images/cs-qrcode.png" alt="" />
        </div>
        <div class="tip">长按识别二维码</div>
      </div>
    </div>
  </Popup>
</template>

<script setup>
  import { ref } from 'vue'
  import { Popup, Icon } from 'vant'
  const show = ref(true)

  defineProps({
    title: {
      type: String,
      default: '提示',
    },
    desc: {
      type: String,
      default: '立即购课与教练联系！\n购课咨询请扫客服企微！',
    },
  })

  const close = () => {
    show.value = false
  }

  defineExpose({
    close,
  })
</script>

<style lang="scss" scoped>
  .content {
    width: 2.5rem;
    height: 3.1rem;
    background: #fff;
    position: relative;
    border-radius: 0.05rem;
  }

  .main {
    text-align: center;
    // padding: 0.41rem 0.56rem 0.37rem 0.56rem;
    width: 2.2rem;
    margin: 0 auto;
    padding-top: 0.4rem;
  }

  .title {
    font-size: 0.18rem;
    font-weight: 600;
    color: #1f1f1f;
  }

  .desc {
    margin: 0.1rem 0;
    font-size: 0.14rem;
    color: #616568;
    white-space: pre-wrap;
  }

  .qrcode {
    margin: 0 auto;
    width: 1.36rem;
    height: 1.36rem;
    background: #ffffff;
    border: 1px solid #d8d8d8;

    img {
      width: 100%;
      height: 100%;
    }
  }
  .tip {
    margin-top: 0.06rem;
    font-size: 0.12rem;
    color: #616568;
  }
  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.32rem;
    height: 0.32rem;
    line-height: 0.32rem;
    font-size: 0.16rem;
    text-align: center;
    color: #bdb8b8;
  }
</style>
