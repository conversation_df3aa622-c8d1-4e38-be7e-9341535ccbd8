<template>
  <div class="order">
    <div class="goods-top">
      <van-row class="buy-other" justify="space-between" align="center">
        <van-col class="buy-time">{{ order.createTime }}</van-col>
        <van-col class="goods-state">{{ order.orderStatus.statusName }}</van-col>
      </van-row>
      <div class="buy-details">
        <van-image
          class="goods-images"
          round
          fit="cover"
          width="0.74rem"
          height="0.74rem"
          :src="getOssURL(order.orderItemList[0].imageUrl)"
        />
        <div class="goods-info">
          <div>
            <van-row justify="space-between" align="center">
              <van-col class="goods-name omit">
                {{ order.orderItemList[0].spuName }}｜{{ order.orderItemList[0].skuName }}</van-col
              >
              <van-col class="buy-price">¥{{ order.orderItemList[0].totalAmount }}</van-col>
            </van-row>
          </div>
          <div class="goods-spec">授课方式：{{ order.orderItemList[0].teachingWay.typeName }}</div>
          <div class="buy-number">课时数：{{ order.orderItemList[0].quantity }}个课时</div>
          <van-row
            v-if="order.orderItemList[0].afterSaleStatus.status !== 'NONE'"
            justify="end"
            align="center"
          >
            <div
              v-if="order.orderItemList[0].afterSaleStatus.status === 'REFUNDING'"
              class="pay-tips"
            >
              退款中
            </div>

            <div v-if="order.orderItemList[0].afterSaleStatus.status === 'CLOSE'" class="pay-tips">
              退款关闭
            </div>
            <div
              v-if="order.orderItemList[0].afterSaleStatus.status === 'SUCCESS'"
              class="pay-tips"
            >
              退款完成
            </div>
          </van-row>
        </div>
      </div>
    </div>
    <div class="goods-bottom">
      <van-row justify="end" align="center">
        <van-col>
          <div class="real-pay">
            <template
              v-if="
                order.orderStatus.status === 'OBLIGATION' ||
                (order.orderStatus.status === 'CANCELED' && order.closeType === 2)
              "
            >
              需付款：
            </template>
            <template v-else> 实付款： </template>

            <span>¥{{ order.paymentAmount }}</span>
          </div>
        </van-col>
      </van-row>
      <van-row class="operation" justify="end" align="center">
        <van-col v-if="order.orderStatus.status === 'OBLIGATION'">
          <!-- <van-count-down
            v-if="countDown(order.paymentDeadTime) > 0"
            :time="countDown(order.paymentDeadTime)"
            class="purchaser"
            format="还剩 mm 分钟"
          /> -->
          <van-count-down class="purchaser" :time="countDown(order.paymentDeadTime)">
            <template #default="timeData">
              <span v-if="timeData.minutes > 0 || timeData.seconds > 0">还剩</span>
              <span v-if="timeData.minutes > 0" class="block">{{ timeData.minutes }}分钟</span>
              <span v-if="timeData.minutes === 0 && timeData.seconds > 0" class="block"
                >{{ timeData.seconds }}秒</span
              >
            </template>
          </van-count-down>
        </van-col>
        <!-- <van-col
          :class="{ residue: true, plain: ['CANCELED', 'FINISH'].includes(state) }"
          @click.stop="$emit('refundBtnClick')"
        >
          {{ orderState(order.orderStatus.status) }}
        </van-col> -->
        <van-col
          v-if="['PAID', 'PART_CONSUMED'].includes(order.orderStatus.status)"
          class="residue"
        >
          <a :href="'tel:' + order.coachInfo.mobile" @click.stop="toAttentClass">我要上课</a>
          <!-- {{ orderState(order.orderStatus.status) }} -->
        </van-col>
        <van-col
          v-else
          :class="{
            residue: true,
            plain: ['CANCELED', 'FINISH'].includes(order.orderStatus.status),
          }"
          @click.stop="$emit('refundBtnClick')"
        >
          {{ orderState(order.orderStatus.status) }}
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { getOssURL } from '@/common'
  import { getDateTime } from '@/utils/day'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const props = defineProps({
    order: {
      type: Object,
      default: () => {},
    },
  })
  const countDown = computed(() => {
    return (state) => {
      let downTime
      // 超过15分钟隐藏
      // if (new Date().getTime() - new Date(state).getTime() < 15 * 60 * 1000) {
      if (getDateTime(state) - getDateTime()) {
        downTime = getDateTime(state) - getDateTime()
      } else {
        downTime = 0
      }
      return downTime
    }
  })
  const orderState = computed(() => {
    return (state) => {
      const obj = {
        OBLIGATION: '立即支付',
        PAID: '我要上课',
        PART_CONSUMED: '我要上课',
        FINISH: '再次购买',
        CANCELED: '重新下单',
      }
      return obj[state]
    }
  })

  // 上课
  const toAttentClass = () => {
    router.push({
      name: 'studentClassDetails',
      query: {
        coachUserId: props.order.sellerId,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .order {
    background: #fff;
    padding: 0 0.15rem;
    margin: 0.08rem 0;
    border-radius: 0.06rem;

    .buy-other {
      padding: 0.08rem 0;
    }

    .buy-time {
      font-size: 12px;
      color: #b2b1b7;
    }

    .goods-state {
      color: #ff9b26;
    }

    .buy-details {
      display: flex;
    }

    .goods-images {
      width: 0.74rem;
      height: 0.74rem;
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
      font-weight: bold;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }

    .real-pay {
      font-size: 0.14rem;
      color: #1a1b1d;
      span {
        font-weight: 600;
        font-size: 0.16rem;
        color: #ff6445;
      }
    }
    .pay-tips {
      font-size: 0.12rem;
      color: #ff6445;
    }

    .line {
      width: 1px;
      height: 0.12rem;
      background: #b2b1b7;
      display: inline-block;
      margin: 0.06rem 0.05rem 0.06rem 0.06rem;
      vertical-align: top;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      vertical-align: top;
      height: 0.24rem;
      line-height: 0.24rem;
    }

    .refund {
      color: #ff6445;
    }

    .refund-success {
      color: #ff9b26;
    }

    .goods-bottom {
      padding-bottom: 0.14rem;

      .purchaser {
        margin-right: 0.1rem;
        font-size: 0.14rem;
        color: #ff6445;
      }
      .operation {
        margin-top: 0.1rem;
        .state-box,
        .handle {
          display: flex;
          padding: 0.08rem 0.1rem;
          font-size: 0.13rem;
          border-radius: 0.04rem;
          background: #f7f7f7;
          .title {
            font-weight: 600;
            color: #1a1b1d;
          }
          .label {
            margin-left: 0.06rem;
            color: #979797;
          }
        }
        .handle {
          .title,
          .label {
            color: #ff6445;
          }
        }
      }
      .residue {
        width: 0.92rem;
        height: 0.32rem;
        line-height: 0.3rem;
        text-align: center;
        font-size: 0.14rem;
        border-radius: 0.16rem;
        color: #ff9b26;
        border: 0.01rem solid #ff9b26;
      }
      .plain {
        color: #616568;
        border: 0.01rem solid #dddddd;
      }
      a {
        color: #ff9b26;
      }
    }
  }
</style>
