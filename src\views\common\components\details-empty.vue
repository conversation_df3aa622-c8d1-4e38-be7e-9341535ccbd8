<template>
  <div class="page min-height-100">
    <div class="empty">
      <img class="empty-img" src="../../../assets/images/empty3.png" alt="" />
      <div class="empty-desc">信息已过期</div>
      <div class="empty-bottom">
        <button class="btn" @click="toHome">去首页({{ seconds }})</button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const seconds = ref(3)

  let timer = setInterval(() => {
    seconds.value = seconds.value - 1

    if (seconds.value === 0) {
      clearInterval(timer)
      timer = null
      router.push('/')
    }
  }, 1000)

  const toHome = () => {
    clearInterval(timer)
    timer = null
    router.push('/')
  }
</script>

<style lang="scss" scoped>
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 1.9rem;
  }

  .empty-img {
    width: 1.2rem;
    height: 1.2rem;
  }

  .empty-desc {
    margin-top: 0.07rem;
    font-size: 0.12rem;
    color: #b2b1b7;
  }

  .empty-bottom {
    margin-top: 0.4rem;
    text-align: center;

    .btn {
      width: 1.8rem;
      height: 0.4rem;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 1px rgba(245, 176, 76, 0.1);
      border-radius: 0.24rem;
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
    }
  }
</style>
