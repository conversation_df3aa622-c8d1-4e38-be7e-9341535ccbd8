<template>
  <div class="quick-nav">
    <router-link
      class="quick-nav-item"
      v-for="(item, index) in navs"
      :key="index"
      :to="getNavLink(item)"
    >
      <img class="quick-nav-icon" :src="getOssURL(item.imageUrl)" alt="" />
      <span class="quick-nav-name">{{ item.name }}</span>
    </router-link>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { getInletDiamondRegion } from '@/api/generic-server'
  import { getOssURL } from '@/common'

  const navs = ref([])

  onMounted(() => {
    getInletDiamondRegionList()
  })

  // 获取金刚区列表
  const getInletDiamondRegionList = () => {
    let params = { pageNum: 1, pageSize: 10, channel: 'H5' }
    getInletDiamondRegion(params).then((res) => {
      const { data } = res
      navs.value = data
    })
  }

  const getNavLink = (item) => {
    // id 1为全部分类
    if (item.id === '1') {
      return { name: 'category' }
    } else {
      return {
        name: 'searchResult',
        query: {
          classifyIds: `${item.firstCategoriesId},${item.secondCategoriesId},${item.classifyId}`,
        },
      }
    }
  }
</script>

<style lang="scss" scoped>
  .quick-nav {
    height: 1.68rem;
    padding: 0.08rem 0.05rem;
    overflow: hidden;
    background-color: #fff;

    .quick-nav-item {
      width: 20%;
      text-align: center;
      display: block;
      padding: 0.1rem 0;
      float: left;
    }

    .quick-nav-icon {
      width: 0.34rem;
      height: 0.34rem;
      vertical-align: top;
    }

    .quick-nav-name {
      display: block;
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.05rem;
    }
  }
</style>
