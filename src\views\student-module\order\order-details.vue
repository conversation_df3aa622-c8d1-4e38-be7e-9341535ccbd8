<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="container min-height-100">
        <div class="order-state">
          <p>
            <span class="label">订单状态：</span>
            <span class="value">{{ orderDetail.orderStatus?.statusName }}</span>
          </p>
          <p class="order-tip">
            <!-- 待支付 -->
            <template
              v-if="
                orderDetail.orderStatus?.status === 'OBLIGATION' &&
                countDown(orderDetail.paymentDeadTime) > 0
              "
            >
              需支付¥{{ numberRP(orderDetail.paymentAmount) }}， 剩
              <van-count-down
                v-if="countDown(orderDetail.paymentDeadTime) > 0"
                :time="countDown(orderDetail.paymentDeadTime)"
                class="purchaser"
                format="mm:ss"
                @finish="onFinish"
              />自动关闭
            </template>
            <!-- 待上课 -->
            <template v-if="orderDetail.orderStatus?.status === 'PAID'">
              教练将联系你预约上课，你也可以主动联系教练约课
            </template>
            <!-- 上课中 -->
            <template v-if="orderDetail.orderStatus?.status === 'PART_CONSUMED'">
              <span v-if="noSchoolDays(orderDetail.lastConsumeTime) > 5">
                你已经有{{ noSchoolDays(orderDetail.lastConsumeTime) }}天没有上课啦，教练都想你了
              </span>
              <span v-else>
                已核销{{ orderDetail.consumedClassesQuantity }}个课时，剩余{{
                  orderDetail.showRemainQuantity
                }}
                个课时
              </span>
            </template>
            <!-- 交易完成 -->
            <template
              v-if="
                orderDetail.orderStatus?.status === 'FINISH' &&
                orderDetail.afterSaleStatus?.status === 'SUCCESS'
              "
            >
              已核销{{ orderDetail.consumedClassesQuantity }}个课时，完成退款{{
                orderDetail.refundedQuantity
              }}个课时
            </template>
            <template
              v-if="
                orderDetail.orderStatus?.status === 'FINISH' &&
                orderDetail.afterSaleStatus?.status === 'NONE'
              "
            >
              你已上完{{ orderDetail.consumedClassesQuantity }}个课时，交易完成
            </template>
            <!-- 交易关闭 -->
            <template
              v-if="orderDetail.orderStatus?.status === 'CANCELED' && orderDetail.closeType === 2"
            >
              超时关闭
            </template>
            <template
              v-if="orderDetail.orderStatus?.status === 'CANCELED' && orderDetail.closeType === 3"
            >
              退款完成
            </template>
          </p>
        </div>
        <div class="order-goods-details">
          <van-image
            class="goods-images"
            round
            fit="cover"
            width="0.74rem"
            height="0.74rem"
            :src="getOssURL(orderDetail.orderItemList?.[0]?.imageUrl)"
          />
          <div class="goods-info">
            <div>
              <van-row justify="space-between" align="center">
                <van-col class="goods-name omit">
                  {{ orderDetail.orderItemList?.[0]?.spuName }} ｜
                  {{ orderDetail.orderItemList?.[0]?.skuName }}
                </van-col>
                <van-col class="buy-price"
                  >¥{{ orderDetail.orderItemList?.[0]?.totalAmount }}</van-col
                >
              </van-row>
            </div>
            <div class="goods-spec">
              授课方式：{{ orderDetail.orderItemList?.[0]?.teachingWay?.typeName }}
            </div>
            <div class="buy-number">
              课时数：{{ orderDetail.orderItemList?.[0]?.quantity }}个课时
            </div>
            <div
              class="buy-state"
              v-if="orderDetail.orderItemList?.[0]?.afterSaleStatus?.status !== 'NONE'"
              @click="toRefundDetail"
            >
              {{ orderDetail.orderItemList?.[0]?.afterSaleStatus?.statusName }}
            </div>
            <div
              v-if="
                orderDetail.orderItemList?.[0]?.afterSaleStatus?.status === 'NONE' &&
                ['PAID', 'PART_CONSUMED'].includes(orderDetail.orderStatus?.status)
              "
              class="refund"
              @click="appleRefund"
            >
              申请退款
            </div>
          </div>
        </div>
        <!-- <div class="residue">
      <span class="label">订单剩余课时：</span>
      <span class="value">10个</span>
    </div> -->

        <div class="order-info">
          <div class="title">订单信息</div>
          <div class="cell">
            <div class="label">
              <!-- 待支付/超时关闭 -->
              <template
                v-if="
                  orderDetail.orderStatus?.status === 'OBLIGATION' ||
                  (orderDetail.orderStatus?.status === 'CANCELED' && orderDetail.closeType === 2)
                "
              >
                需付款
              </template>
              <template v-else> 实付款 </template>
            </div>
            <div class="value money">¥{{ numberRP(orderDetail.paymentAmount) }}</div>
          </div>
          <div v-if="orderDetail.paymentType?.typeName" class="cell">
            <div class="label">支付方式</div>
            <div class="value">{{ orderDetail.paymentType?.typeName }}</div>
          </div>
          <div class="cell">
            <div class="label">联系人</div>
            <div class="value">{{ orderDetail.contactName }}</div>
          </div>
          <div class="cell">
            <div class="label">联系电话</div>
            <div class="value">{{ orderDetail.contactMobile }}</div>
          </div>
          <div class="cell">
            <div class="label">订单编号</div>
            <div class="value">
              <span>{{ orderDetail.id }} I </span>
              <copy-text class="copy" :text="orderDetail.id" @success="copySuccess">复制</copy-text>
            </div>
          </div>
          <div class="cell">
            <div class="label">创建时间</div>
            <div class="value">{{ orderDetail.createTime }}</div>
          </div>
          <div v-if="orderDetail?.paymentTime" class="cell">
            <div class="label">付款时间</div>
            <div class="value">{{ orderDetail?.paymentTime }}</div>
          </div>
        </div>
        <div class="tips">
          <h3>温馨提示</h3>
          <p>
            1、预约教练上课时，请尽量结伴而行<br />
            2、儿童上课时，务必由家长陪同 <br />
            3、上课地点请尽量预约在公共场所 <br />
            4、上课前请提前与教练沟通好上课细节<br />
          </p>
        </div>
        <div
          v-if="
            orderDetail.orderStatus?.status === 'OBLIGATION' &&
            countDown(orderDetail.paymentDeadTime) > 0
          "
          class="footer"
        >
          <!-- && countDown(orderDetail.createTime) > 0 -->
          <div class="pay-txt">
            距离支付结束还有：<span>
              <van-count-down
                v-if="countDown(orderDetail.paymentDeadTime) > 0"
                :time="countDown(orderDetail.paymentDeadTime)"
                class="purchaser"
                format="mm:ss"
              />
            </span>
          </div>
          <div class="pay-btn" @click="toPay">立即支付</div>
        </div>
        <!-- <BottomBtns
      v-if="['PAID', 'PART_CONSUMED'].includes(orderDetail.orderStatus?.status)"
      isOnlyOne="right"
      rightBtnText="我要上课"
      @rightClick="toAttentClass"
    /> -->
        <div
          v-if="['PAID', 'PART_CONSUMED'].includes(orderDetail.orderStatus?.status)"
          class="footer-btn"
        >
          <a :href="'tel:' + orderDetail.coachInfo.mobile" @click="toAttentClass">我要上课</a>
        </div>
        <BottomBtns
          v-if="orderDetail.orderStatus?.status === 'CANCELED'"
          isOnlyOne="right"
          rightBtnText="重新购买"
          @rightClick="againBuy"
        />
        <BottomBtns
          v-if="orderDetail.orderStatus?.status === 'FINISH'"
          isleftPlain
          leftBtnText="上课记录"
          rightBtnText="再次购买"
          @leftClick="classRecord"
          @rightClick="againBuy"
        />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { Toast } from 'vant'
  import { ref, computed } from 'vue'
  import CopyText from '@/components/copy-text'
  import BottomBtns from '@/components/basic/bottom-btns.vue'
  import { useRoute, useRouter } from 'vue-router'
  import { getStuOrderDetail } from '@/api/trade-server'
  import { numberRP } from '@/utils'
  import { getOssURL } from '@/common'
  import { getDateTime } from '@/utils/day'

  const route = useRoute()
  const router = useRouter()
  console.log(router)
  const orderId = route.query.orderId

  const orderDetail = ref({})
  // 获取订单详情
  const getOrderDetail = () => {
    let params = { id: orderId }
    getStuOrderDetail(params).then((res) => {
      const { data } = res
      orderDetail.value = data
    })
  }
  getOrderDetail()

  const copySuccess = () => {
    Toast('复制成功')
  }

  const toPay = () => {
    router.push({
      name: 'payCourse',
      query: {
        orderNo: orderDetail.value.id,
      },
    })
  }

  // 上课
  const toAttentClass = () => {
    console.log(orderDetail.value.orderItemList[0].spuId, '************')
    router.push({
      name: 'studentClassDetails',
      query: {
        coachUserId: orderDetail.value.sellerId,
      },
    })
  }
  // 上课记录
  const classRecord = () => {
    router.push({
      name: 'studentClassDetails',
      query: {
        coachUserId: orderDetail.value.sellerId,
      },
    })
  }
  const againBuy = () => {
    const spuId = orderDetail.value.orderItemList[0].spuId
    router.push({
      path: `/coach/details/${spuId}`,
      query: {
        id: spuId,
        isShowSku: true,
      },
    })
  }

  // 倒计时(到期时间 - 现在时间)
  const countDown = computed(() => {
    return (state) => {
      let downTime
      if (getDateTime(state) - getDateTime()) {
        downTime = getDateTime(state) - getDateTime()
      } else {
        downTime = 0
      }
      return downTime
    }
  })
  // 计算学员连续多少天未上课
  const noSchoolDays = (lastSchoolDay) => {
    let ms = getDateTime() - getDateTime(lastSchoolDay)
    return Math.round(ms / 86400000)
  }

  // 申请退款
  const appleRefund = () => {
    if (orderDetail.value.applyConsumeQuantity > 0) {
      Toast('订单存在未处理的课时核销申请，请处理后再退款')
    } else {
      router.push({
        name: 'studentOrderApplyRefund',
        query: {
          orderId: orderId,
          orderItemId: orderDetail.value.orderItemList[0].id,
        },
      })
    }
  }

  const toRefundDetail = () => {
    router.push({
      name: 'studentOrderRefundDetails',
      query: {
        orderId: orderDetail.value.orderItemList[0].afterSaleId,
      },
    })
  }
  const onFinish = () => {
    orderDetail.value = {}
    getOrderDetail()
  }
</script>

<style lang="scss" scoped>
  .container {
    background: #fff;
    padding-bottom: 0.65rem;
  }

  .order-state {
    padding: 0.12rem 0.15rem;
    border-bottom: 1px solid #eeeeee;
    background: #fff;

    .label {
      color: #616568;
    }
    .value {
      font-weight: 600;
      font-size: 0.18rem;
      color: #1a1b1d;
    }
  }

  .order-tip {
    display: flex;
    margin-top: 0.06rem;
    color: #616568;
    .purchaser {
      font-size: 0.14rem;
      color: #616568;
    }
    :deep(.van-count-down) {
      padding: 0 0.02rem;
      font-size: 0.14rem;
      color: #ff6445;
    }
  }

  .order-goods-details {
    display: flex;
    padding: 0.11rem 0.15rem 0 0.15rem;
    background: #fff;

    .goods-images {
      width: 0.74rem;
      height: 0.74rem;
      border-radius: 0.06rem;
    }

    .goods-info {
      position: relative;
      margin-left: 0.1rem;
      flex: 1;
      .refund {
        position: absolute;
        right: 0;
        bottom: 0;
        text-align: center;
        width: 0.75rem;
        height: 0.26rem;
        line-height: 0.26rem;
        border-radius: 0.23rem;
        font-size: 0.12rem;
        color: #616568;
        border: 0.01rem solid #dddddd;
      }
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      text-align: right;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }
  }

  .residue {
    background: #fff;
    padding: 0.2rem 0.15rem 0.14rem 0.15rem;
    font-size: 0.16rem;
    font-weight: 600;

    .label {
      color: #1a1b1d;
    }

    .value {
      color: #ff6445;
    }
  }

  .order-info {
    padding: 0 0.15rem;
    background: #fff;

    .title {
      padding: 0.12rem 0 0.15rem 0;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .cell {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.14rem;
      margin-bottom: 0.13rem;

      .label {
        color: #1a1b1d;
      }

      .value {
        color: #b2b1b7;
      }

      .money {
        font-size: 0.16rem;
        color: #ff6445;
      }

      .copy {
        color: #1a1b1d;
      }
    }
  }
  .tips {
    padding-top: 0.15rem;
    margin: auto 0.15rem;
    border-top: 0.01rem solid #eee;
    h3 {
      margin-bottom: 0.06rem;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
    }
    p {
      font-size: 0.14rem;
      color: #b2b1b7;
      line-height: 0.2rem;
    }
  }
  .footer {
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: var(--window-left);
    right: var(--window-right);
    z-index: 10;
    padding: 0.1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .pay-txt {
      display: flex;
      align-items: center;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1a1b1d;
      span {
        color: #ff6445;
      }
      :deep(.van-count-down) {
        font-size: 0.16rem;
        color: #ff6445;
      }
    }
    .pay-btn {
      width: 1.21rem;
      height: 0.4rem;
      line-height: 0.4rem;
      text-align: center;
      color: #ffffff;
      font-weight: bold;
      background: #ff9b26;
      border-radius: 0.23rem;
    }
  }
  .footer-btn {
    position: fixed;
    bottom: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.1rem 0.15rem;
    background: #fff;

    a {
      background: #ff9b26;
      height: 0.4rem;
      line-height: 0.4rem;
      width: 3.45rem;
      display: block;
      text-align: center;
      color: #fff;
      border-radius: 0.2rem;
      font-size: 0.15rem;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      font-weight: 600;
    }
  }
</style>
