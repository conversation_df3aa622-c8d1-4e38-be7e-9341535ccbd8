<template>
  <div class="page">
    <div class="content">
      <i class="icon-success"></i>
      <div class="content-text">
        <p class="pay-success">支付成功</p>
        <p class="tip">请手动切换软件回到微信，查看订单</p>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';

  @include Icon('success', 0.48rem, 0.48rem);

  .content {
    text-align: center;
    padding-top: 2rem;

    .pay-success {
      margin-top: 0.1rem;
      font-size: 0.16rem;
      color: #1f1f1f;
      font-weight: 600;
    }

    .tip {
      font-size: 0.16rem;
      color: #1f1f1f;
      margin-top: 0.15rem;
    }
  }
</style>
