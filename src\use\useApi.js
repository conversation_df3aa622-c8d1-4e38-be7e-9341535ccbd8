import { ref } from 'vue'

const useApi = (api) => {
  let loading = ref(false)
  let result = ref(null)
  let error = ref(null)

  const fetchResource = (param) => {
    loading.value = true

    return api(param)
      .then((res) => {
        result.value = res
      })
      .finally(() => {
        loading.value = false
      })
  }

  return {
    loading,
    result,
    error,
    fetchResource,
  }
}

export default useApi
