<template>
  <img
    :src="imageCaptchaURL"
    @click.self="refreshImageCaptcha"
    alt="验证码"
    crossorigin="anonymous"
  />
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { apiURL } from '@/config'
  import { graphicCaptchaURL } from '@/api/auth-server'
  import { uuid } from '@/utils'
  import { localProxyStorage } from '@/utils/storage'

  // 将生成的uuid存在本地
  if (!localProxyStorage.uuid) {
    localProxyStorage.uuid = uuid()
  }

  // 验证码URL
  const imageCaptchaURL = ref(null)

  const splicingParamsStr = () => {
    return '?v=' + new Date().getTime() + '&uid=' + localProxyStorage.uuid
  }

  const getCaptchaURLURL = () => apiURL + graphicCaptchaURL + splicingParamsStr()

  // 刷新图形验证码
  const refreshImageCaptcha = () => {
    imageCaptchaURL.value = getCaptchaURLURL()
  }

  onMounted(() => {
    imageCaptchaURL.value = getCaptchaURLURL()
  })
</script>

<style lang="scss" scoped></style>
