<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div>
        <div v-if="pageShow" class="wrapper min-height-100">
          <div class="title">{{ recruit.title }}</div>
          <div class="author-info">
            <div class="info-l">
              <span class="author" @click="toAuthorHome(recruit.mappingId, recruit.identityType)">
                {{ recruit.realName }}
              </span>
              <span> {{ sliceStr(recruit.releaseTime, 0, 10) }}</span>
            </div>
            <div class="info-r">
              <span>{{ recruit.readCount }}浏览</span>
            </div>
          </div>
          <image-preview-wrapper>
            <p>截止时间：{{ recruit.updateTime.split(' ')[0] }}</p>
            <p>招聘科目：{{ recruit.thirdlyCategoriesName }}</p>
            <p>招聘地点：{{ getArea(recruit) }}</p>
            <div class="split"></div>
            <div class="content" v-html="recruit.content" />
          </image-preview-wrapper>
        </div>
        <details-empty v-if="emptyShow"></details-empty>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import { getJobsDetail } from '@/api/generic-server'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { toAuthorHome } from '@/common'
  import DetailsEmpty from '@/views/common/components/details-empty'
  import { sliceStr } from '@/utils'

  const route = useRoute()
  const recruit = ref(null)
  const recruitId = +route.params.recruitId
  const pageShow = ref(false)
  const emptyShow = ref(false)

  const initialize = async () => {
    let { data } = await getJobsDetail({ jobsId: recruitId })
    document.title = data.title + '教练招聘,爱教练私教网'
    document
      .querySelector('meta[name="keywords"]')
      .setAttribute(
        'content',
        `${data.title},${data.location}${data.thirdlyCategoriesName}教练招聘,爱教练私教网`,
      )
    document
      .querySelector('meta[name="description"]')
      .setAttribute(
        'content',
        `${data.title},${data.location}${data.thirdlyCategoriesName}教练招聘,爱教练私教网囊括网球,羽毛球,乒乓球,游泳,武术,瑜伽,高尔夫,音乐,舞蹈,模特,艺术等各类培训信息`,
      )
    recruit.value = data

    if (recruit.value.publish) {
      pageShow.value = true
    } else {
      emptyShow.value = true
    }

    let desc = ''
    if (typeof data.description === 'string') {
      desc = data.description.replace(/(\r\n|\n|\t)/g, '')
    }

    setWxShare({
      title: '【爱教练】' + data.title,
      desc: desc,
      link: baseURL + '/recruit-details/' + recruitId,
      imgUrl: data.image ? data.image : ossURL + '/h5-assets/logo.png',
    })
  }

  // 获取地区名称
  const getArea = (item) => {
    let str = ''
    if (item.cityName) {
      str += item.cityName
    }

    if (item.countyName) {
      str += item.countyName
    }
    return str
  }
  initialize()
</script>
<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: text-top;
    margin-right: 0.06rem;
  }

  .wrapper {
    padding: 0.15rem;
    background-color: #fff;
  }

  .title {
    font-size: 0.17rem;
    font-weight: 600;
    margin-bottom: 0.08rem;
    @include TextEllipsis(2);
  }

  .author-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.12rem;
    color: #b2b1b7;
    line-height: 0.17rem;
    margin-bottom: 0.06rem;

    .author {
      margin-right: 0.1rem;
      color: var(--i-primary);
    }
  }
  .split {
    margin: 0.1rem 0;
    border-bottom: 0.01rem solid #e3e3e3;
  }
  :deep(.content) {
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }
</style>
