<template>
  <div class="module-entry">
    <router-link v-for="(item, index) in moduleEntry" :key="index" class="module" :to="item.path">
      <img :src="item.image" alt="" />
    </router-link>
  </div>
</template>

<script setup>
  import { moduleEntry } from '../home-config'
</script>

<style lang="scss" scoped>
  .module-entry {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 0.15rem;
    margin-top: 0.16rem;

    .module {
      width: 1.66rem;
      height: 0.68rem;
      margin-bottom: 0.1rem;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
