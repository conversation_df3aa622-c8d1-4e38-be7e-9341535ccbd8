<template>
  <button class="solid-button" :style="{ width, height, color, fontSize: size }">
    <span>
      <slot />
    </span>
    <img v-if="hand" class="hand" src="../images/click.gif" />
  </button>
</template>

<script setup>
  defineProps({
    width: {
      type: String,
      default: '3.46rem;',
    },
    height: {
      type: String,
      default: '0.55rem',
    },
    color: {
      type: String,
      default: '#000000',
    },
    size: {
      type: String,
      default: '0.26rem',
    },
    hand: {
      type: Boolean,
      default: true,
    },
  })
</script>

<style lang="scss" scoped>
  @import '../font/index.css';

  .solid-button {
    width: 100%;
    height: 0.55rem;
    padding-bottom: 0.06rem;
    display: inline-block;
    white-space: normal;
    cursor: pointer;
    background: #ffd600;
    border: 1.5px solid #7719be;
    transition: 0.1s;
    user-select: none;
    outline: none;
    border-radius: 0.08rem;
    position: relative;
    font-family: YouSheBiaoTiHei;

    .hand {
      width: 0.72rem;
      height: 0.72rem;
      margin-left: 0.08rem;
      position: absolute;
      right: 0.15rem;
      display: inline-block;
      z-index: 3;
      // animation: pulse 1s ease-in-out infinite;
    }

    @keyframes pulse {
      from {
        transform: scale3d(1, 1, 1);
      }

      50% {
        transform: scale3d(1.05, 1.05, 1.05);
      }

      to {
        transform: scale3d(1, 1, 1);
      }
    }

    &:before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: inherit;
      border-bottom: 0.06rem solid #ff8100;
      box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2), 0 2px 2px rgba(0, 0, 0, 0.14),
        0 3px 1px -2px rgba(0, 0, 0, 0.12);
    }

    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 0.06rem;
      background: transparent;
      border-radius: 0.16rem;
      position: absolute;
      bottom: 0.06rem;
      border-bottom: 1.5px solid #7719be;
    }

    &:active {
      transform: translateY(2px);
    }
  }
</style>
