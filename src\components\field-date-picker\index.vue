<template>
  <div class="date-picker">
    <van-field
      v-model="inputValue"
      :is-link="isLint"
      :label="label"
      @click="openPopup"
      readonly
      :disabled="disabled"
      :placeholder="placeholder"
    />
    <van-popup v-model:show="popupShow" round position="bottom">
      <van-datetime-picker
        v-bind="attrs"
        v-model="time"
        :min-date="minDate"
        :max-date="maxDate"
        :formatter="handleFormatter"
        @confirm="handleConfirm"
        @cancel="handleCancelDatePicker"
      />
    </van-popup>
  </div>
</template>

<script setup>
  import { ref, useAttrs, watch } from 'vue'
  import { dateFormat } from '@/utils/day'

  let props = defineProps({
    modelValue: {
      type: [String, Date],
      default: '',
    },
    // 使用format指定输入框的格式；使用value-format指定绑定值的格式
    format: {
      type: String,
      default: 'YYYY/MM/DD HH:mm:ss',
    },
    valueFormat: {
      type: String,
      default: 'YYYY/MM/DD HH:mm:ss',
    },
    minDate: {
      type: Date,
      default: () => new Date(1950, 0, 1),
    },
    maxDate: {
      type: Date,
      default: () => new Date(2100, 0, 1),
    },
    label: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '选择日期时间',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isLint: {
      type: Boolean,
      default: false,
    },
  })

  const attrs = useAttrs()

  const emit = defineEmits(['update:modelValue'])

  const inputValue = ref('')

  const popupShow = ref(false)

  let time = ref(new Date())

  watch(
    () => props.modelValue,
    (newVal) => {
      if (typeof newVal === 'object') {
        time.value = newVal
      } else if (typeof newVal === 'string' && attrs.type !== 'time') {
        let timeStrDate = newVal.replaceAll('-', '/')
        time.value = newVal ? new Date(timeStrDate) : new Date()
      } else if (typeof newVal === 'string' && attrs.type === 'time') {
        time.value = newVal
      } else {
        time.value = new Date()
      }

      if (typeof newVal === 'string' && attrs.type === 'time') {
        inputValue.value = newVal
      } else {
        inputValue.value = newVal ? dateFormat(time.value, props.format) : ''
      }
    },
    {
      immediate: true,
    },
  )

  const openPopup = () => {
    if (props.disabled) return
    popupShow.value = true
  }

  // 关闭弹窗
  const closePopup = () => {
    popupShow.value = false
  }

  const handleFormatter = (type, val) => {
    if (type === 'year') {
      return val + '年'
    }
    if (type === 'month') {
      return val + '月'
    }
    if (type === 'day') {
      return val + '日'
    }
    return val
  }

  const handleConfirm = (value) => {
    if (attrs.type === 'time') {
      inputValue.value = value
      emit('update:modelValue', value)
    } else {
      inputValue.value = dateFormat(value.getTime(), props.format)
      emit('update:modelValue', dateFormat(value.getTime(), props.valueFormat))
    }

    closePopup()
  }

  // 取消选择时间
  const handleCancelDatePicker = () => {
    closePopup()
  }
</script>

<style lang="scss" scoped>
  .date-picker {
    :deep(.van-picker__confirm) {
      font-size: 0.15rem;
      color: #f06e6c;
    }

    :deep(.van-picker__cancel) {
      font-size: 0.15rem;
    }
  }
</style>
