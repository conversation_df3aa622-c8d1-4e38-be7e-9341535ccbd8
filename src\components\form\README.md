## i-select.js

### 使用

```vue
<script>
  import { ref } from 'vue'
  import ISelect from "@/components/form/i-select";
  const value = ref('')
</script>

<template>
  <i-select
      title="选择"
      label="地区"
      placeholder="选择地区"
      v-model="value"
      :options="[
        { text: '选项一', id: '1' },
        { text: '选项二', id: '2' },
        { text: '选项三', id: '3' },
      ]"
  />
</template>

```