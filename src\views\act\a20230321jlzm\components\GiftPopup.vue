<template>
  <Teleport to="body">
    <div class="GiftPopup">
      <van-popup v-if="show" v-model:show="show" @close="onClose" :close-on-click-overlay="false">
        <div class="popup-wrap">
          <div class="close" @click="updateShow(false)" />
          <div class="title">
            <i class="star" />
            <span>谢谢你为我点赞</span>
            <i class="star" />
          </div>
          <div class="content">
            <p class="tip">送你体育私教福利包</p>
            <img class="gift-img" src="../images/welfare.png" alt="" />
            <div class="receive-award" @click="$router.push('/act/a20230321jlzm/collect-gift-bag')">
              <span class="btn-text">限时免费领取</span>
              <span class="count-down">
                <van-count-down :time="time" format="DD 天 HH : mm : ss " />
              </span>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import { getDateTime } from '@/utils/day'
  import { useParent } from '@vant/use'
  // import { localProxyStorage } from "@/utils/storage";

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])
  const { parent } = useParent('321ACT')

  watch(
    () => props.modelValue,
    (val) => (show.value = val),
  )

  const show = ref(props.modelValue || false)

  const time = computed(() => {
    const date = parent.actInfo.value.endTime.replaceAll('-', '/')
    return getDateTime(date) - getDateTime()
  })

  const updateShow = (state) => emit('update:modelValue', state)

  const onClose = () => {
    updateShow(false)
  }

  // const onNoPrompt = () => {
  //   localProxyStorage.isGiftPopup = true;
  //   updateShow(false);
  // };
</script>

<style lang="scss" scoped>
  .popup-wrap {
    width: 3.15rem;
    background: #ffffff;
    border-radius: 0.1rem;
    border: 2px solid #376cfe;
    padding-bottom: 0.1rem;
    position: relative;
    user-select: none;
  }

  .close {
    position: absolute;
    right: 0.12rem;
    top: 0.12rem;
    width: 0.12rem;
    height: 0.12rem;
    background: url('../images/popup-close.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  .title {
    margin-top: 0.25rem;
    margin-bottom: 0.1rem;
    text-align: center;

    span {
      margin: 0 0.05rem;
      font-size: 0.16rem;
      color: #1a1b1d;
      font-weight: 600;
    }
  }

  .star {
    width: 0.15rem;
    height: 0.1rem;
    display: inline-block;
    background: url('../images/star.png') no-repeat;
    background-size: 100% 100%;
  }

  .content {
    padding: 0 0.15rem;

    .tip {
      color: #616568;
      text-align: center;
      margin-bottom: 0.07rem;
    }

    .gift-img {
      width: 2.85rem;
      height: 1.74rem;
      display: block;
    }

    .receive-award {
      width: 1.83rem;
      margin: 0.2rem auto 0 auto;
      background: linear-gradient(270deg, #ff6445 0%, #ff9b26 100%);
      box-shadow: 0 0.02rem 0.08rem 0 rgba(255, 147, 42, 0.6);
      border-radius: 0.26rem;
      text-align: center;
      padding: 0.04rem 0;
      cursor: pointer;

      .btn-text {
        font-size: 0.14rem;
        color: #ffffff;
        font-weight: bold;
      }

      .count-down {
        .van-count-down {
          font-size: 0.12rem;
          color: #fff4cf;
          line-height: initial;
        }
      }
    }

    .dnd-btn {
      font-size: 0.12rem;
      color: #0083fc;
      display: block;
      margin: 0.1rem auto 0 auto;
      cursor: pointer;
    }
  }

  :deep(.van-popup) {
    background: transparent;
  }
</style>
