<template>
  <div ref="playerEl" class="player"></div>
</template>

<script setup>
  import { ref, watch, onMounted, onBeforeMount } from 'vue'
  import Player from 'xgplayer'

  const props = defineProps({
    url: String,
    poster: {
      type: String,
      default: null,
    },
    width: { type: [String, Number], default: '100%' },
    height: [String, Number],
    ignores: {
      type: Array,
      default: () => [],
    },
    fitVideoSize: {
      type: String,
      default: '',
    },
  })

  const playerEl = ref(null)
  let playerInstance = ref(null)

  //初始化播放器
  function initPlayer() {
    return new Player({
      el: playerEl.value,
      url: props.url,
      width: props.width,
      height: props.height,
      poster: props.poster || null,
      videoInit: true, // 初始化显示视频首帧
      'x5-video-player-type': 'h5',
      playsinline: false,
      ignores: props.ignores, // 关闭内置控件
      fitVideoSize: props.fitVideoSize,
    })
  }

  watch(
    () => props.url,
    (newVal, oldVal) => {
      if (newVal === oldVal) return
      if (playerInstance.value) {
        playerInstance.value.destroy()
        setTimeout(() => {
          playerInstance.value = initPlayer()
        })
      }
    },
  )

  onMounted(() => {
    playerInstance.value = initPlayer()
  })

  onBeforeMount(() => {
    playerInstance.value && playerInstance.value.destroy()
  })

  defineExpose({
    playerInstance,
  })
</script>

<style lang="scss" scoped></style>
