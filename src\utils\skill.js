import { getCategoriesList } from '@/api/coach-server'

export const getCategoriesLInfo = () => {
  // 运动分类
  return getCategoriesList()
    .then((res) => {
      const obj = {
        id: '0',
        text: '全部',
        name: '全部',
        childCategoriesVos: [],
      }
      res.data.unshift(obj)
      res.data.map((item) => {
        console.log(item)
        const obj = {
          id: '0',
          text: '全部',
          name: '全部',
          childCategoriesVos: [],
        }
        item['text'] = item.name
        if (item.childCategoriesVos) {
          item.childCategoriesVos.unshift(obj)
          item.childCategoriesVos?.map((childItem) => {
            const childObj = {
              id: '0',
              text: '全部',
              name: '全部',
              childCategoriesVos: [],
            }
            childItem['text'] = childItem.name

            if (childItem.childCategoriesVos && childItem.childCategoriesVos?.length !== 0) {
              childItem.childCategoriesVos.unshift(childObj)
            }
            if (!childItem.childCategoriesVos) {
              childItem.childCategoriesVos = []
              childItem.childCategoriesVos.unshift(childObj)
            }

            childItem.childCategoriesVos?.map((proItem) => {
              proItem['text'] = proItem.name
            })
          })
        }
      })
      // 过滤没有子数据的分类科目
      res.data = res.data.filter((item) => {
        return item.childCategoriesVos !== null
      })
      // formData.threeSelectList = res.data;
      // console.log(formData.threeSelectList, "formData.threeSelectList0809");
      // return formData.threeSelectList;
      return res.data
    })
    .catch(() => {})
}
