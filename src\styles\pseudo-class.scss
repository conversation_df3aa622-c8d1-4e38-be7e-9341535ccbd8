
// 按钮反馈样式
.i-button {
  position: relative;
  cursor: pointer;

  &:before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: #000;
    border-color: #000;
    border-radius: inherit;
    transform: translate(-50%,-50%);
    opacity: 0;
    content: " ";
  }

  &:active::before {
    opacity: 0.1;
  }
}

// 元素反馈样式
.feedback {
  &:active {
    background: var(--i-base-active-color) !important;
    background-color: var(--i-base-active-color) !important;
  }
}
