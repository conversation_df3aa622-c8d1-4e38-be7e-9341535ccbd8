<template>
  <div>
    <div v-if="formatData.isShowTitle" class="title-box">
      <div class="item-title">基本信息</div>
      <div class="form-tip">
        说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
        表示审核必须完善的资料
      </div>
    </div>
    <div v-else class="form-tip" style="padding: 0.08rem 0 0 0.2rem; margin-bottom: 0">
      说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
      表示审核必须完善的资料
    </div>
    <van-form class="form">
      <van-field
        v-model="formData.realName"
        required
        label="真实姓名"
        :maxlength="8"
        placeholder="请输入真实姓名，营造真实平台环境"
        @blur="realNameBlur"
      />
      <ErrorTip v-if="isShowNameTip" tipTxt="不可为空" />
      <van-field
        v-model="formData.idCard"
        required
        maxlength="18"
        label="身份证号"
        placeholder="仅用于实名认证，请放心填写"
        @blur="realIdCardBlur"
      />
      <ErrorTip v-if="isShowIdCardTip" :tipTxt="idTips" />
      <van-field name="radio" label="性别">
        <template #input>
          <van-radio-group v-model="formData.sex" direction="horizontal">
            <van-radio :name="1" shape="square" checked-color="#FF9B26">男</van-radio>
            <van-radio :name="2" shape="square" checked-color="#FF9B26">女</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <field-date-picker
        v-model="formData.birthdate"
        :isLint="true"
        placeholder="请输入日期"
        label="出生日期"
        :maxDate="new Date()"
        type="date"
        title="选择出生日期"
        format="YYYY年MM月DD日"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
      <van-field
        v-model="formatData.address"
        class="green-require"
        is-link
        readonly
        label="所在地区"
        placeholder="为方便更多学员找到您，请选择所在地区"
        @click="distPickerShow = true"
      ></van-field>
      <div class="adress">
        <!-- <van-field v-model="formData.location" label=" " placeholder="请输入详细地址"> </van-field> -->
        <van-field
          v-model="formData.location"
          label=" "
          placeholder="请选输入详细地址（选填）"
          @keyup="locationInput"
          @focus="adressFocus"
          @blur="adressBlur"
        >
        </van-field>
        <van-overlay :show="isShowAdress" @click="isShowAdress = false"> </van-overlay>
        <div v-show="isShowAdress" class="adress-list">
          <div
            v-for="(item, index) in locationInfo"
            :key="index"
            class="adress-item"
            @click="selectAdress(item)"
          >
            <h3>{{ item.name }}</h3>
            <p>{{ item.address }}</p>
          </div>
        </div>
      </div>
      <div class="form-item-block">
        <div class="label">
          <span class="label-title request">个人照片</span>
          <span class="desc">最多9张，&lt;10M/张，jpg/jpeg/png/gif格式</span>
        </div>
        <div class="value">
          <upload-file
            preview-size="0.7rem"
            file-type="jpg|png|gif|jpeg"
            :max-size="10240 * 1024"
            v-model="formData.coachImages"
            :max-count="9"
          />
        </div>
      </div>
      <div class="form-item-block">
        <div class="label">
          <div class="label-title">视频</div>
          <span class="desc">仅1个，&lt;50M，mp4/mov格式</span>
        </div>
        <div class="value">
          <upload-file v-model="formData.coachVideos" :max-count="1" accept="video/*">
            <template #preview-cover="{ url }">
              <video :src="url" style="width: 100%; height: 100%; object-fit: cover"></video>
            </template>
          </upload-file>
        </div>
      </div>
      <!-- 地址选择器 -->
      <dist-picker
        :default-selected="formatData.lastAreaCode"
        v-model:show="distPickerShow"
        @finish="handledAddressFinish"
      />
    </van-form>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { useRoute } from 'vue-router'
  import ErrorTip from './ErrorTip.vue'
  import FieldDatePicker from '@/components/field-date-picker'
  import { checkEmpty, validate } from '@/utils/validate'
  import { getLocationInfo, getUserLocation } from '@/common'
  import Schema from 'async-validator'
  import { Toast } from 'vant'
  import { updateCoachBaseInfo } from '@/api/coach-server'
  import UploadFile from '@/components/upload-file'
  import DistPicker from '@/components/dist-picker'

  const route = useRoute()
  const isCoachIdentity = route.query.isCoachIdentity // 是否已经是教练身份
  const distPickerShow = ref(false)
  const isShowAdress = ref(false)
  const formData = reactive({
    editType: 'add', // 编辑类型: 新增: add, 修改：update
    realName: '', // 姓名
    idCard: '', // 身份证
    sex: null, // 性别
    birthdate: '', // 出生日期
    areaCodes: [], //省市区乡
    location: '', // 详细地址
    teachYear: '', // 从业年限
    levelIds: [], //授课类型
    teachingWeek: [], // 教学工作日
    teachingTimeRange: [], // 教学时间范围
    teachTitle: '', //教练标题
    teachDescription: '', // 教练描述
    coachImages: [], // 教练图片
    coachVideos: [], // 教练视频
    checkId: null, //审核ID
    coachId: null, // 教练id
    longitude: '',
    latitude: '',
  })

  const formatData = reactive({
    address: '', // 省/市/区名称拼接
    lastAreaCode: '',
    skillType: '',
    selectedSkillType: {},
    isUpdateTeachTitle: true, //是否能更新教练标题字段
    isShowTitle: true, //是否来自注册入驻
  })

  const locationInfo = ref([])
  const cityName = ref([])

  onMounted(async () => {
    await getUserLocation().then((res) => {
      cityName.value = res.data?.city?.split('市')[0]
    })
  })
  const validator = new Schema({
    realName: [
      { message: '请输入姓名', validator: checkEmpty },
      { message: '姓名不能超过8个字符', max: 8 },
    ],
    idCard: {
      message: '请输入身份证',
      validator: function (rule, value) {
        if (!value) return false
        if (!validate('idCard', value)) {
          this.message = '请输入正确的身份证号'
          return false
        }
        return true
      },
    },
  })

  // 校验姓名
  const isShowNameTip = ref(false)
  const realNameBlur = () => {
    isShowNameTip.value = formData.realName ? false : true
  }
  // 校验身份证
  const idTips = ref(null)
  const isShowIdCardTip = ref(false)
  const realIdCardBlur = (item) => {
    const value = item.target.value
    if (!value) {
      isShowIdCardTip.value = true
      idTips.value = '不可为空'
    } else if (!validate('idCard', value)) {
      idTips.value = '请输入正确的身份证号'
      isShowIdCardTip.value = true
    } else {
      isShowIdCardTip.value = false
    }
  }

  const adressBlur = () => {
    // document.getElementsByClassName("form")[0].scrollIntoView();
  }

  const adressFocus = () => {
    document.getElementsByClassName('adress')[0].scrollIntoView()
    if (formData.location) {
      isShowAdress.value = true
    }
    // document.getElementsByClassName("area")[0].scrollIntoView();
    // document.getElementsByClassName("adress")[0].previousSibling.scrollIntoView();
  }

  const selectAdress = (item) => {
    console.log(item)
    formData.areaCodes = []
    const province = item.district.split('省')[0] + '省'
    const city = item.district.split('省')[1].split('市')[0] + '市'
    const area = item.district.split('省')[1].split('市')[1]
    formatData.address = province + '/' + city + '/' + area

    formData.location = item.name
    formData.longitude = +item.longitude
    formData.latitude = +item.latitude
    formatData.lastAreaCode = item.adcode
    formData.teachTitle = item.district.split('省')[1]

    formData.areaCodes.push(item.adcode.substring(0, 2) + '0000')
    formData.areaCodes.push(item.adcode.substring(0, 4) + '00')
    if (item.adcode.substring(4) !== '00') {
      formData.areaCodes.push(item.adcode)
    }

    isShowAdress.value = false
  }
  // 地址选择完毕hook
  const selectedCity = ref('')
  const handledAddressFinish = (params) => {
    console.log(params, 'params')
    selectedCity.value = params.selectedOptions[1].adName
    if (params) {
      formData.areaCodes = []
      let { selectedOptions } = params
      nextTick(() => {
        formData.teachTitle = selectedOptions[1].adName + selectedOptions[2]?.adName
        if (!selectedOptions[2]?.adName) {
          formData.teachTitle = selectedOptions[1].adName
        }
        console.log(formData.teachTitle, '地址选择完毕hook')
        selectedOptions.forEach((item) => {
          formData.areaCodes.push(item.adCode)
        })
        formatData.address = params.address
        if (formData.areaCodes.length === 2) {
          formData.areaCodes.push('')
        }
      })
      formData.location = ''
      formData.longitude = ''
      formData.latitude = ''
    }
    distPickerShow.value = false
  }

  const timer = ref(null)
  const locationInput = (e) => {
    // console.log(e.target.value);
    if (timer.value) {
      clearTimeout(timer.value)
    }
    timer.value = setTimeout(() => {
      const params = {
        keywords: e.target.value || '',
        city: cityName.value || '',
      }
      if (selectedCity.value) {
        params['city'] = selectedCity.value
      }

      getLocationInfo(params).then((res) => {
        locationInfo.value = []
        console.log(res, '高德')
        if (res.status === 200) {
          res.data.tips.map((item) => {
            const obj = {}
            obj['name'] = item.name
            // obj["address"] = item.pname + item.cityname + item.adname + item.address;
            obj['address'] = item.district + item.address
            obj['addrDetail'] = item.address
            obj['adcode'] = item.adcode
            obj['district'] = item.district
            // console.log(item, "item1");
            if (item.location.length > 0) {
              obj['longitude'] = item.location.split(',')[0] //经度
              obj['latitude'] = item.location.split(',')[1] //纬度
            }
            locationInfo.value.push(obj)
          })
          console.log(locationInfo.value, 'locationInfo.value')
          isShowAdress.value = locationInfo.value.length === 0 ? false : true
        }
      })
      timer.value = undefined
    }, 300)
    // console.log(formData.location, "formData.location");
  }

  const handleFormData = () => {
    let data = JSON.parse(JSON.stringify(formData))
    data.teachingWeek = data.teachingWeek.join(',')
    data.coachImages = data.coachImages.map((file) => file.path)
    data.coachVideos = data.coachVideos?.[0]?.path

    return data
  }
  const submit = (callback) => {
    let formData = handleFormData()
    validator
      .validate(formData)
      .then(() => {
        updateCoachBaseInfo(formData, isCoachIdentity)
          .then((res) => {
            callback(true, res)
          })
          .catch((error) => {
            callback(false, error)
          })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  defineExpose({
    formData,
    formatData,
    submit,
  })
</script>

<style scoped lang="scss">
  .form {
    margin: 0 0.2rem;
  }

  .title-box {
    border-bottom: 1px solid #eee;
    padding: 0 0.1rem;
  }

  .form-tip {
    padding-left: 0.12rem;
    font-size: 0.12rem;
    margin-bottom: 0.12rem;
  }

  .red {
    color: #ff6445;
  }

  .green {
    color: #0abb08;
  }

  .green-require {
    :deep(.van-field__label) {
      &::before {
        margin-right: 2px;
        color: #0abb08;
        content: '*';
      }
    }
  }

  .item-title {
    position: relative;
    //padding: 0.12rem;
    padding: 0.12rem 0.12rem 0.06rem 0.12rem;
    font-size: 0.14rem;
    font-weight: 600;
    color: #616568;
    //border-bottom: 1px solid #eee;

    &::before,
    &::after {
      position: absolute;
      content: '';
      display: block;
    }

    &::before {
      left: 0;
      top: 50%;
      transform: translate(0, -60%);
      width: 0.03rem;
      height: 0.14rem;
      background-color: #ff9b26;
      border-radius: 0.03rem;
    }
  }

  .form-item-block {
    display: flex;
    flex-direction: column;
    padding: 0.17rem 0;
    font-size: 0.14rem;
    border-bottom: 1px solid #eeeeee;

    .label {
      display: flex;
      align-items: center;

      .label-title {
        width: 1rem;
        // margin-right: 12px;
      }
      .request::before {
        margin-right: 2px;
        color: #0abb08;
        content: '*';
      }
    }

    .desc {
      margin-bottom: 0.04rem;
      font-size: 0.1rem;
      color: #959595;
    }

    .value {
      margin-top: 0.05rem;
      padding-left: 1rem;
    }
  }
  .adress {
    position: relative;
    -webkit-overflow-scrolling: touch;
    .adress-list {
      position: absolute;
      padding: 0.12rem;
      z-index: 99;
      top: 0.58rem;
      right: 0rem;
      width: 2.44rem;
      height: 2rem;
      background-color: #fff;
      border-radius: 0.1rem;
      border: 0.01rem solid #eee;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(92, 92, 92, 0.1);
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      .adress-item {
        margin-top: 0.1rem;
        h3 {
          font-size: 0.14rem;
          color: #ff9b26;
        }
        p {
          font-size: 0.12rem;
          color: #999;
        }
        &:first-child {
          margin-top: 0;
        }
      }
    }
  }
  .adress :deep(.van-overlay) {
    background-color: transparent;
  }
  // :deep(.error) {
  //   margin-left: 0.15rem;
  // }
  :deep(.van-cell) {
    padding-left: 0;
    padding-right: 0;
  }
  .date-picker {
    border-bottom: 0.01rem solid #f4f5f7;
  }
  :deep(.van-field__label),
  .label-title {
    color: #453938;
    font-size: 0.14rem;
  }
  :deep(.van-field__control) {
    font-size: 0.14rem;
  }
  :deep(.van-cell) {
    padding: 0.17rem 0;
  }
</style>
