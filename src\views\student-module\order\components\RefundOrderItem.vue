<template>
  <div class="order">
    <div class="goods-top">
      <van-row class="buy-other" justify="space-between" align="center">
        <van-col class="buy-time">{{ order.applyTime }}</van-col>
        <van-col class="goods-state">{{ order.afterSaleStatus.statusName }}</van-col>
      </van-row>
      <div class="buy-details">
        <van-image
          class="goods-images"
          round
          fit="cover"
          width="0.74rem"
          height="0.74rem"
          :src="getOssURL(order.orderItem.imageUrl)"
        />
        <div class="goods-info">
          <div>
            <van-row justify="space-between" align="center">
              <van-col class="goods-name omit">
                <span>{{ order.orderItem.spuName }}｜</span>
                <span>{{ order.orderItem.skuName }}</span>
              </van-col>
              <van-col class="buy-price">¥{{ order.orderItem.totalAmount }}</van-col>
            </van-row>
          </div>
          <div class="goods-spec">授课方式：{{ order.orderItem.teachingWay.typeName }}</div>
          <div class="buy-number">课时数：{{ order.orderItem.quantity }}个课时</div>
          <div class="refund-amount">
            <span class="label">申请退款：</span
            ><span class="value">¥{{ order.refundAmount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 倒计时 -->
    <!--    <div v-if="order.afterSaleStatus.status === 'APPLIED'" class="tip">-->
    <!--      <div class="tip-title down-title">待教练处理</div>-->
    <!--      <div>-->
    <!--        <van-count-down class="purchaser" :time="countDown(order.auditDeadTime)">-->
    <!--          <template #default="timeData">-->
    <!--            <span v-if="timeData.minutes > 0 || timeData.seconds > 0">还剩</span>-->
    <!--            <span v-if="timeData.hours > 0" class="block">{{ timeData.hours }}时</span>-->
    <!--            <span class="block">{{ timeData.minutes }}分钟</span>-->
    <!--          </template>-->
    <!--        </van-count-down>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <div v-else class="tip">-->
    <!--      <template-->
    <!--        v-if="order.afterSaleStatus.status === 'AGREED' && order.refundStatus === 'PROCESSING'"-->
    <!--      >-->
    <!--        <div class="tip-title">退款成功</div>-->
    <!--        <div>退款待到账</div>-->
    <!--      </template>-->
    <!--      <template-->
    <!--        v-if="order.afterSaleStatus.status === 'AGREED' && order.refundStatus === 'SUCCESS'"-->
    <!--      >-->
    <!--        <div class="tip-title">退款成功</div>-->
    <!--        <div>已退回到原账户：¥{{ order.refundAmount }}元</div>-->
    <!--      </template>-->
    <!--      <template-->
    <!--        v-if="order.afterSaleStatus.status === 'AGREED' && order.refundStatus === 'FAILURE'"-->
    <!--      >-->
    <!--        <div class="tip-title">退款成功</div>-->
    <!--        <div>到账失败，请联系平台客服</div>-->
    <!--      </template>-->
    <!--      <template v-if="order.afterSaleStatus.status === 'REFUSED'">-->
    <!--        <div class="tip-title">拒绝退款</div>-->
    <!--        <div>教练拒绝了你的退款申请</div>-->
    <!--      </template>-->
    <!--      <template v-if="order.afterSaleStatus.status === 'CANCELED'">-->
    <!--        <div class="tip-title">取消退款</div>-->
    <!--        <div>你已取消退款申请</div>-->
    <!--      </template>-->
    <!--    </div>-->

    <!--    <van-row-->
    <!--      v-if="order.afterSaleStatus.status !== 'AGREED'"-->
    <!--      class="operation"-->
    <!--      justify="end"-->
    <!--      align="center"-->
    <!--    >-->
    <!--      <van-col-->
    <!--        :class="{-->
    <!--          residue: true,-->
    <!--          plain: ['REFUSED', 'CANCELED'].includes(order.afterSaleStatus.status),-->
    <!--        }"-->
    <!--        @click.stop="$emit('refundBtnClick')"-->
    <!--      >-->
    <!--        {{ orderState(order.afterSaleStatus.status) }}-->
    <!--      </van-col>-->
    <!--    </van-row>-->
  </div>
</template>

<script setup>
  // import { computed } from "vue";
  import { getOssURL } from '@/common'
  // import { getDateTime } from "@/utils/day";
  // import { useRouter } from "vue-router";
  // const router = useRouter();

  // const orderState = computed(() => {
  //   return (state) => {
  //     const obj = {
  //       APPLIED: "取消退款",
  //       // AGREED: "我要上课",
  //       REFUSED: "客服介入",
  //       CANCELED: "重新申请",
  //     };
  //     return obj[state];
  //   };
  // });

  defineProps({
    order: {
      type: Object,
      default: () => {},
    },
  })

  // 倒计时(到期时间 - 现在时间)
  // const countDown = computed(() => {
  //   return (state) => {
  //     let downTime;
  //     if (getDateTime(state) - getDateTime()) {
  //       downTime = getDateTime(state) - getDateTime();
  //     } else {
  //       downTime = 0;
  //     }
  //     return downTime;
  //   };
  // });
</script>

<style lang="scss" scoped>
  .order {
    background: #fff;
    padding: 0 0.15rem 0.12rem 0.15rem;
    margin: 0.08rem 0;
    border-radius: 0.06rem;

    .operation {
      margin-top: 0.1rem;
      .residue {
        width: 0.92rem;
        height: 0.32rem;
        line-height: 0.3rem;
        text-align: center;
        font-size: 0.14rem;
        border-radius: 0.16rem;
        color: #ff9b26;
        border: 0.01rem solid #ff9b26;
      }
      .plain {
        color: #616568;
        border: 0.01rem solid #dddddd;
      }
    }
    // .goods-top {
    //   border-bottom: 1px solid #eeeeee;
    // }

    .buy-other {
      padding: 0.08rem 0;
    }

    .buy-time {
      font-size: 12px;
      color: #b2b1b7;
    }

    .goods-state {
      color: #ff9b26;
    }

    .buy-details {
      display: flex;
      padding-bottom: 0.1rem;
    }

    .goods-images {
      width: 0.74rem;
      height: 0.74rem;
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      //font-weight: bold;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
      //font-weight: bold;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }

    .real-pay {
      font-size: 0.14rem;
      span {
        font-size: 0.16rem;
        color: #1a1b1d;
      }
    }

    .line {
      width: 1px;
      height: 0.12rem;
      background: #b2b1b7;
      display: inline-block;
      margin: 0.06rem 0.05rem 0.06rem 0.06rem;
      vertical-align: top;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      vertical-align: top;
      height: 0.24rem;
      line-height: 0.24rem;
    }

    .refund {
      color: #ff6445;
    }

    .refund-success {
      color: #ff9b26;
    }

    .refund-amount {
      text-align: right;

      .label {
        font-size: 0.14rem;
        color: #1a1b1d;
      }
      .value {
        font-size: 0.16rem;
        color: #ff6445;
        font-weight: 600;
      }
    }

    .goods-bottom {
      padding: 0.1rem 0;

      .purchaser {
        display: flex;
        align-items: center;
      }

      .purchaser-head-portrait {
        width: 32px;
        height: 32px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .purchaser-name {
        margin-left: 0.07rem;
        color: #1a1b1d;
      }

      .icon-phone-call {
        width: 0.17rem;
        height: 0.16rem;
        background: url('../../../../assets/images/coach-worktable/icon-phone-call.png') no-repeat;
        background-size: 100% 100%;
        margin-left: 0.06rem;
      }

      .refund-btn {
        padding: 0.06rem 0.18rem;
        font-size: 0.14rem;
        color: #ff9b26;
        border-radius: 0.16rem;
        border: 1px solid #ff9b26;
      }
    }

    .count-down {
      text-align: right;
      font-size: 0.12rem;
      color: #ff6445;
    }

    .tip {
      display: flex;
      padding: 0.08rem 0.1rem;
      background: #f7f7f7;
      border-radius: 4px;
      font-size: 0.13rem;
      color: #979797;

      .tip-title {
        color: #1a1b1d;
        margin-right: 0.06rem;
      }
      .down-title {
        color: #ff6445;
        font-weight: 600;
      }
      .purchaser {
        color: #ff6445;
        .block:first-child {
          margin-left: -0.02rem;
        }
      }
    }
  }
</style>
