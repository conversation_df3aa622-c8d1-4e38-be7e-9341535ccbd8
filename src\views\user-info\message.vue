<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="container">
        <empty v-if="emptyShow" top="2.41rem" description="暂无消息" />
        <div>
          <div v-if="messageList.length > 0" class="readed-btn" @click="allRead">一键已读</div>
          <div v-if="messageList">
            <van-list
              ref="listRef"
              class="contain"
              v-model:loading="loading"
              :finished="finished"
              :finished-text="messageList.length > 0 ? '-没有更多了-' : null"
              @load="onLoad"
            >
              <div
                v-for="(item, index) in messageList"
                :key="index"
                class="news-item"
                @click="messageDetail(item)"
              >
                <div class="header flex">
                  <h3>{{ item.title }}</h3>
                  <i class="icon icon-arrow-right2" />
                </div>
                <div class="content flex">
                  <p class="news-txt">{{ item.content }}</p>
                  <div v-if="item.state === 0" class="read-print"></div>
                </div>
                <p class="update-time">{{ item.sendTime }}</p>
              </div>
            </van-list>
          </div>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { getUserMessageList, userOneKeyHasBeenRead, userHasBeenRead } from '@/api/user-server'
  import Empty from '@/components/empty'
  import { Toast } from 'vant'
  import { baseURL } from '@/config'

  const router = useRouter()
  const loading = ref(false)
  const finished = ref(false)
  const emptyShow = ref(false)
  const messageList = ref([])
  const listRef = ref(null)
  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  // 一键已读
  const allRead = () => {
    let toast = Toast.loading({
      duration: 0,
      message: '请稍等...',
      forbidClick: true,
    })
    userOneKeyHasBeenRead().then(() => {
      messageList.value = []
      pagination.pageNum = 0
      loading.value = false
      finished.value = false
      listRef.value?.check()
      toast.clear()
    })
  }

  const onLoad = () => {
    pagination.pageNum += 1
    getUserMessageList(pagination)
      .then((res) => {
        let { data } = res
        messageList.value = messageList.value.concat(data)
        loading.value = false
        emptyShow.value = messageList.value.length === 0
        if (data.length === 0 || data.length < pagination.pageSize) {
          finished.value = true
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true
      })
  }

  const messageDetail = async (message) => {
    message.state = 1
    // 标记已读
    await userHasBeenRead({ informId: message.informId })

    if (!message.url) return
    let chars = message.url?.split(baseURL)
    if (chars.length === 2) {
      router.push(chars[1] || '/')
    } else {
      window.location.href = message.url
    }
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';
  @include Icon('arrow-right2', 0.32rem, 0.32rem);
  .flex {
    display: flex;
    align-items: center;
  }

  .container {
    padding: 0.06rem 0.15rem;
  }

  .readed-btn {
    margin-left: 2.7rem;
    width: 0.75rem;
    height: 0.26rem;
    line-height: 0.24rem;
    text-align: center;
    color: #616568;
    font-size: 0.12rem;
    border: 0.01rem solid #dddddd;
    border-radius: 0.23rem;
  }
  .contain {
    padding-top: 0.08rem;
  }
  .news-item {
    margin-bottom: 0.06rem;
    padding: 0.12rem 0rem 0.12rem 0.15rem;
    background-color: #fff;
    border-radius: 0.06rem;
    .header {
      justify-content: space-between;
      h3 {
        font-size: 0.15rem;
        color: #1a1b1d;
        font-weight: 600;
      }
    }
    .content {
      justify-content: space-between;
      margin-top: 0.06rem;
      margin-bottom: 0.1rem;
      .news-txt {
        font-size: 0.13rem;
        color: #616568;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .read-print {
        margin: 0 0.16rem;
        width: 0.07rem;
        height: 0.07rem;
        background: #ff6445;
        border-radius: 50%;
      }
    }
    .update-time {
      font-size: 0.12rem;
      color: #b2b1b7;
    }
  }
  .null-data {
    margin: 30% auto 0;
    width: 2rem;
    height: 2rem;
    text-align: center;
    img {
      margin: 0 auto;
      display: block;
      width: 1.4rem;
      height: 1.4rem;
    }
    p {
      margin-top: 0.24rem;
      font-size: 0.14rem;
      color: #666666;
      line-height: 0.2rem;
    }
  }
</style>
