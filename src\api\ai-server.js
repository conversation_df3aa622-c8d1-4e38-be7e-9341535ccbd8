import http from '@/utils/ai-axios'
import { aiApiURL } from '@/config/index.js'
import axios from 'axios'
import { localProxyStorage } from '@/utils/storage'

export const getChat = (data) => {
  return http.post('/api/ai/chat', data)
}
export const getStreamChat = (data) => {
  const baseUrl = process.env.VUE_APP_RUN_ENV !== 'development' ? aiApiURL : ''
  const authToken = localProxyStorage.user.authToken || ''
  return axios({
    method: 'post',
    url: baseUrl + '/api/ai/stream/chat',
    data: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json', 'auth-token': authToken },
  })
}
export const getWorkChatSign = (data) => {
  return http.post('/api/auth/wework/signature', data)
}
// 创建会话
export const createChat = (data) => {
  return http.post('/api/conversation/create', data)
}
// 取消流式对话
export const cancelStreamChat = (params) => {
  return http.get('/api/ai/stream/chat/cancel', { params })
}
export const getAuthToken = (params) => {
  return http.get('/api/auth/wework/authenticate', { params })
}
// 获取会话列表
export const getConversationList = (params) => {
  return http.get('/api/conversation/list', { params })
}
// 获取会话消息列表
export const getMessagesList = (params) => {
  return http.get('/api/message/list', { params })
}
// 获取会话详情
export const getConversationRetrieve = (params) => {
  return http.get('/api/conversation/retrieve', { params })
}
// 地区字段配置
export const getAreaOptions = (params) => {
  return http.get('/api/tanma/getAreaOptions', { params })
}
// 选项字段配置
export const getFieldOptions = (params) => {
  return http.get('/api/tanma/getFieldOptions', { params })
}
// 同步客户字段
export const setCustomerFields = (data) => {
  return http.post('/api/tanma/syncCustomerFields', data)
}
// 消息打标
export const getMark = (data) => {
  return http.post('/api/message/mark', data)
}
// 获取会话最新一条消息
export const getConversationLast = (params) => {
  return http.get('/api/conversation/latest', { params })
}

// 测试专用-教练
export const getRecommendCoach = (data) => {
  return http.post('/api/public/recommendCoachs', data)
}
