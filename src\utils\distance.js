/*
 *经纬度距离计算
 *lng1,lat1 第一个经纬度
 *lng2，lat2 第二个经纬度
 */

export function calculateDiscount(lng1, lat1, lng2, lat2) {
  const radLat1 = (lat1 * Math.PI) / 180.0
  const radLat2 = (lat2 * Math.PI) / 180.0
  const a = radLat1 - radLat2
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
  let s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2),
      ),
    )
  s = s * 6378.137 // EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000
  s = s * 1000
  if (isNaN(s)) {
    return 0 + 'm'
  }
  if (s > 100000) {
    // 大于100Km时
    s = Math.floor((s / 1000) * 100) / 100
    return s.toFixed() + 'km'
  }
  if (s > 1000 && s < 100000) {
    // 大于1000米时
    s = Math.floor((s / 1000) * 100) / 100
    return s.toFixed(2) + 'km'
  }
  // 小于1000米直接返回
  return s.toFixed() + 'm'
}
