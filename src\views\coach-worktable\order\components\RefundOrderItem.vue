<template>
  <div class="order">
    <div class="goods-top">
      <van-row class="buy-other" justify="space-between" align="center">
        <van-col class="buy-time">{{ order.applyTime }}</van-col>
        <van-col class="goods-state">{{ order.afterSaleStatus.statusName }}</van-col>
      </van-row>
      <div class="buy-details">
        <van-image
          class="goods-images"
          round
          fit="cover"
          width="0.74rem"
          height="0.74rem"
          :src="getOssURL(order.orderItem.imageUrl)"
        />
        <div class="goods-info">
          <div>
            <van-row justify="space-between" align="center">
              <van-col class="goods-name omit">
                <span>{{ order.orderItem.spuName }}｜</span>
                <span>{{ order.orderItem.skuName }}</span>
              </van-col>
              <van-col class="buy-price">¥{{ order.orderItem.totalAmount }}</van-col>
            </van-row>
          </div>
          <div class="goods-spec">授课方式：{{ order.orderItem.teachingWay.typeName }}</div>
          <div class="buy-number">课时数：{{ order.orderItem.quantity }}个课时</div>
          <div class="refund-amount">
            <span class="label">申请退款：</span
            ><span class="value">¥{{ order.refundAmount }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="goods-bottom">
      <van-row justify="space-between" align="center">
        <van-col>
          <!-- <a @click.stop class="purchaser" :href="'tel:' + order.studentInfo.mobile">
            <div class="purchaser-head-portrait">
              <img :src="getOssURL(order.studentInfo.avatarUrl)" alt="" />
            </div>
            <div class="purchaser-name">{{ order.studentInfo.studentName }}</div>
            <div class="icon-phone-call"></div>
          </a> -->
        </van-col>
        <van-col>
          <button
            v-if="isOrderPending"
            class="refund-btn feedback"
            @click.stop="$emit('refundBtnClick')"
          >
            处理退款
          </button>
        </van-col>
      </van-row>
    </div>
    <!-- 倒计时 -->
    <div v-if="isOrderPending && time" class="count-down">
      还有<van-count-down :time="time" format="HH时mm分钟" />
    </div>
    <div v-else class="tip">
      <template v-if="order.afterSaleStatus.status === 'AGREED'">
        <div class="tip-title">退款成功</div>
        <div>你已同意退款申请：¥{{ order.refundAmount }}元</div>
      </template>
      <template v-if="order.afterSaleStatus.status === 'REFUSED'">
        <div class="tip-title">拒绝退款</div>
        <div>你已拒绝学员的退款申请</div>
      </template>
      <template v-if="order.afterSaleStatus.status === 'CANCELED'">
        <div class="tip-title">取消退款</div>
        <div>学员已取消退款申请</div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { getOssURL } from '@/common'
  import { getDateTime } from '@/utils/day'

  let props = defineProps({
    order: {
      type: Object,
      default: () => {},
    },
  })

  // 订单是否处于待处理状态
  const isOrderPending = computed(() => {
    return props.order.afterSaleStatus.status === 'APPLIED'
  })

  const time = ref(0)

  // 退款申请剩余处理时间
  if (props.order.afterSaleStatus.status === 'APPLIED') {
    let startTime = getDateTime(props.order.applyTime)
    let curTime = getDateTime()
    let timeRemaining = curTime - startTime
    time.value = 86400000 - timeRemaining
  }
</script>

<style lang="scss" scoped>
  .order {
    background: #fff;
    padding: 0 0.15rem 0.12rem 0.15rem;
    margin: 0.08rem 0;
    border-radius: 0.06rem;

    .goods-top {
      border-bottom: 1px solid #eeeeee;
    }

    .buy-other {
      padding: 0.08rem 0;
    }

    .buy-time {
      font-size: 0.12rem;
      color: #b2b1b7;
    }

    .goods-state {
      color: #ff9b26;
    }

    .buy-details {
      display: flex;
      padding-bottom: 0.1rem;
    }

    .goods-images {
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }

    .real-pay {
      font-size: 0.14rem;
      span {
        font-size: 0.16rem;
        color: #1a1b1d;
      }
    }

    .line {
      width: 1px;
      height: 0.12rem;
      background: #b2b1b7;
      display: inline-block;
      margin: 0.06rem 0.05rem 0.06rem 0.06rem;
      vertical-align: top;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      vertical-align: top;
      height: 0.24rem;
      line-height: 0.24rem;
    }

    .refund {
      color: #ff6445;
    }

    .refund-success {
      color: #ff9b26;
    }

    .refund-amount {
      text-align: right;

      .label {
        font-size: 0.14rem;
        color: #1a1b1d;
      }

      .value {
        font-size: 0.16rem;
        color: #ff6445;
      }
    }

    .goods-bottom {
      padding: 0.1rem 0;

      .purchaser {
        display: flex;
        align-items: center;
        background: #f7f7f7;
        border-radius: 0.16rem;
        padding-right: 0.14rem;
      }

      .purchaser-head-portrait {
        width: 32px;
        height: 32px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .purchaser-name {
        margin-left: 0.07rem;
        color: #1a1b1d;
      }

      .icon-phone-call {
        width: 0.17rem;
        height: 0.16rem;
        background: url('../../../../assets/images/coach-worktable/icon-phone-call.png') no-repeat;
        background-size: 100% 100%;
        margin-left: 0.06rem;
      }

      .refund-btn {
        padding: 0.06rem 0.18rem;
        font-size: 0.14rem;
        color: #ff9b26;
        border-radius: 0.16rem;
        border: 1px solid #ff9b26;
      }
    }

    .count-down {
      text-align: right;
      font-size: 0.12rem;
      color: #ff6445;

      .van-count-down {
        display: inline-block;
        font-size: 0.12rem;
        color: #ff6445;
      }
    }

    .tip {
      display: flex;
      padding: 0.08rem 0.1rem;
      background: #f7f7f7;
      border-radius: 4px;
      font-size: 0.13rem;
      color: #979797;

      .tip-title {
        color: #1a1b1d;
        margin-right: 0.06rem;
      }
    }
  }
</style>
