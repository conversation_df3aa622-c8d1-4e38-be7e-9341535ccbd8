<template>
  <div class="copy-wrap">
    <textarea id="copy-text-container" v-model="inputVal" readonly />
    <div class="copy-btn-container" data-clipboard-target="#copy-text-container">
      <slot />
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
  import Clipboard from 'clipboard'

  const inputVal = ref('')

  const props = defineProps({
    text: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['success', 'error'])

  let clipboard = null

  watch(
    () => props.text,
    (newVal) => (inputVal.value = newVal),
    { immediate: true },
  )

  onMounted(() => {
    clipboard = new Clipboard('.copy-btn-container')

    clipboard.on('success', (e) => {
      emit('success', e)
    })

    clipboard.on('error', (e) => {
      emit('error', e)
    })
  })

  onBeforeUnmount(() => {
    clipboard && clipboard.destroy()
  })
</script>

<style lang="scss" scoped>
  .copy-wrap {
    display: inline-block;
    textarea {
      position: absolute;
      left: -999999px;
    }
  }
</style>
