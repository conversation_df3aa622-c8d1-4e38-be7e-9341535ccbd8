<template>
  <div>
    <div v-if="formatData.isShowTitle" class="title-box">
      <div class="item-title">资质信息</div>
      <div class="form-tip">
        说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
        表示审核必须完善的资料
      </div>
    </div>

    <div v-else class="form-tip" style="padding: 0.08rem 0 0 0.2rem; margin-bottom: 0">
      说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
      表示审核必须完善的资料
    </div>

    <van-form class="form">
      <van-field class="descript" label="场馆标题" v-model="formData.title" disabled />
      <!-- <h3 class="cell-title">场馆标题</h3> -->
      <!-- <van-field v-model="formData.title" label="场馆标题" placeholder="请输入标题" /> -->
      <!-- <van-field
        v-model="formData.metaTitle"
        class="textarea-wrapper"
        rows="3"
        type="textarea"
        maxlength="30"
        show-word-limit
        label="场馆描述"
        placeholder="请输入场馆描述…"
      /> -->
      <h3 class="cell-title"><span class="green">*</span>场馆描述</h3>
      <van-field
        class="descript"
        v-model="formData.metaTitle"
        autosize
        rows="2"
        type="textarea"
        maxlength="30"
        show-word-limit
        placeholder="请输入场馆描述…"
      />
      <van-field name="uploader" label="营业执照">
        <template #input>
          <p class="tips">仅1张，&lt; 10M/张，jpg/jpeg/png/gif格式</p>
          <upload-file v-model="formData.bsUrls" :max-count="1" />
        </template>
      </van-field>
      <h3 class="cell-title">场馆说明</h3>
      <quill-editor
        v-model="formData.content"
        class="editor"
        height="2.7rem"
        @ready="editorReady"
        placeholder="请输入场馆说明"
      />
      <!-- <div class="rich-context" @click="contentFocus">
        <div class="rich-box" v-if="formData.content" v-html="formData.content"></div>
        <div class="tips" v-else>输入场馆说明</div>
      </div> -->
      <div v-if="formatData.isShowTitle" class="fixed-bottom">
        <Checkbox v-model="checked" checked-color="#FF9B26" shape="square">
          我已阅读并同意
          <span class="protocol" @click="$router.push({ path: '/help', query: { tabIndex: 6 } })">
            《服务协议》
          </span>
        </Checkbox>
      </div>
    </van-form>
  </div>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue'
  import { editVenuesQualificationInfo } from '@/api/coach-server'
  import UploadFile from '@/components/upload-file'
  import { Toast, Checkbox } from 'vant'
  import QuillEditor from '@/components/quill-editor'

  const props = defineProps({
    shopTitle: {
      type: String,
      default: '',
    },
  })

  const formData = reactive({
    title: '', // 标题
    metaTitle: '', // 场馆描述
    bsUrls: [], // 营业执照
    content: '', // 场馆说明
  })
  const formatData = reactive({
    isShowTitle: true,
  })
  watch(
    () => props.shopTitle,
    (newVal) => {
      formData.title = newVal
    },
  )
  const checked = ref(false)
  const submit = (callback) => {
    if (formatData.isShowTitle && !checked.value) {
      Toast('请阅读并勾选服务协议~')
      return
    }
    let form = JSON.parse(JSON.stringify(formData))
    form.bsUrls = form.bsUrls.map((file) => file.path)
    editVenuesQualificationInfo(form)
      .then((res) => {
        callback(true, res)
      })
      .catch((error) => {
        callback(false, error)
      })
  }

  defineExpose({
    formData,
    formatData,
    submit,
  })

  const editorReady = (editor) => {
    formData.content = editor
  }
</script>

<style scoped lang="scss">
  .van-form {
    padding-bottom: 0.14rem;
  }

  .title-box {
    border-bottom: 1px solid #eee;
    padding: 0 0.1rem;
  }

  .form-tip {
    padding-left: 0.12rem;
    font-size: 0.12rem;
    margin-bottom: 0.12rem;
  }

  .red {
    color: #ff6445;
  }

  .green {
    color: #0abb08;
  }

  .green-require {
    :deep(.van-field__label) {
      &::before {
        margin-right: 2px;
        color: #0abb08;
        content: '*';
      }
    }
  }

  .tips {
    width: 100%;
    flex-shrink: 0;
    font-size: 0.1rem;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 0.05rem;
  }
  :deep(.van-field__control--custom) {
    flex-wrap: wrap;
  }
  .item-title {
    position: relative;
    padding: 0.12rem 0.12rem 0.06rem 0.12rem;
    margin-left: 0.1rem;
    font-size: 0.14rem;
    font-weight: 600;
    color: #616568;
    &::before,
    &::after {
      position: absolute;
      content: '';
      display: block;
    }
    //border-bottom: 1px solid #eee;
    &::before {
      left: 0;
      top: 50%;
      transform: translate(0, -60%);
      width: 0.03rem;
      height: 0.14rem;
      background-color: #ff9b26;
      border-radius: 0.03rem;
    }
  }
  .textarea-wrapper {
    :deep(.van-cell__value) {
      background: #f5f5f5;
      border: 1px solid #e8e8e8;
      border-radius: 0.04rem;
      padding: 0.08rem;
    }

    :deep(.van-field__word-limit) {
      font-size: 0.12rem;
      color: #ccc;
    }

    :deep(.van-field__control--min-height) {
      min-height: 0.6rem;
    }
  }
  .cell-title {
    padding-top: 0.17rem;
    padding-left: 0.2rem;
    font-size: 0.14rem;
    color: #453938;
    font-weight: 400;
  }
  .textarea-wrapper :deep(.van-cell__value--alone) {
    background-color: #fafafa;
    border-radius: 0.05rem;
    padding: 0.12rem;
    border: 0.01rem solid #e8e8e8;
  }
  .textarea-wrapper:after {
    border-bottom: 0px solid #000;
  }
  :deep(.van-field__word-limit) {
    color: #b2b1b7;
  }
  .descript :deep(.van-field__word-limit) {
    position: absolute;
    right: 0;
    bottom: 0.04rem;
    right: 0.08rem;
  }
  .rich-context {
    overflow-y: scroll;
    height: 5rem;
    margin: 0 0.16rem;
    margin-top: 0.1rem;
    padding: 0.12rem;
    background: #fafafa;
    border-radius: 0.05rem;
    border: 0.01rem solid #e8e8e8;
    .tips {
      color: #b2b1b7;
    }
    :deep(img) {
      width: 100%;
    }
  }
  .fixed-bottom {
    // margin: 0.3rem 0.16rem 0rem;
    padding: 0 0.16rem;
    position: fixed;
    bottom: 0.75rem;
    width: 3.75rem;
    background: #fff;
    padding-top: 0.1rem;
    :deep(.van-checkbox__label) {
      color: #999999;
    }
  }
  .protocol {
    color: #e02525;
  }
  .editor {
    margin-top: 0.05rem;
    padding: 0 0.15rem;
  }
  :deep(.van-field__control),
  :deep(.van-field__label) {
    color: #453938;
    font-size: 0.14rem;
  }
  :deep(.van-cell) {
    padding: 0.17rem 0.2rem;
  }
</style>
