<template>
  <Teleport to="body">
    <div class="GiftPopup">
      <van-popup v-if="show" v-model:show="show" @close="onClose" :close-on-click-overlay="false">
        <div class="popup-wrap">
          <div class="close" @click="updateShow(false)" />
          <div class="title">
            <i class="star" />
            <span>谢谢你为我点赞</span>
            <i class="star" />
          </div>
          <div class="content">
            <p class="tip">送你体育私教福利包</p>
            <img class="gift-img" src="../images/welfare.png" alt="" />
            <div class="solid-button" @click="$router.push('/act/a20230510jlzm/collect-gift-bag')">
              <div class="btn-text">限时免费领取</div>
              <div class="count-down">
                <van-count-down :time="time" format="DD 天 HH : mm : ss " />
              </div>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import { getDateTime } from '@/utils/day'
  import { useParent } from '@vant/use'
  // import { localProxyStorage } from "@/utils/storage";

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])
  const { parent } = useParent('321ACT')

  watch(
    () => props.modelValue,
    (val) => (show.value = val),
  )

  const show = ref(props.modelValue || false)

  const time = computed(() => {
    const date = parent.actInfo.value.endTime.replaceAll('-', '/')
    return getDateTime(date) - getDateTime()
  })

  const updateShow = (state) => emit('update:modelValue', state)

  const onClose = () => {
    updateShow(false)
  }

  // const onNoPrompt = () => {
  //   localProxyStorage.isGiftPopup = true;
  //   updateShow(false);
  // };
</script>

<style lang="scss" scoped>
  .popup-wrap {
    width: 3.15rem;
    background: #ffffff;
    border-radius: 0.1rem;
    border: 2px solid #376cfe;
    padding-bottom: 0.1rem;
    position: relative;
    user-select: none;
  }

  .close {
    position: absolute;
    right: 0.12rem;
    top: 0.12rem;
    width: 0.12rem;
    height: 0.12rem;
    background: url('../images/popup-close.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }

  .title {
    margin-top: 0.25rem;
    margin-bottom: 0.1rem;
    text-align: center;

    span {
      margin: 0 0.05rem;
      font-size: 0.16rem;
      color: #1a1b1d;
      font-weight: 600;
    }
  }

  .star {
    width: 0.15rem;
    height: 0.1rem;
    display: inline-block;
    background: url('../images/star.png') no-repeat;
    background-size: 100% 100%;
  }

  .content {
    padding: 0 0.15rem;
    text-align: center;

    .tip {
      color: #616568;
      text-align: center;
      margin-bottom: 0.07rem;
    }

    .gift-img {
      width: 2.85rem;
      height: 1.74rem;
      display: block;
    }

    .solid-button {
      width: 1.84rem;
      height: 0.44rem;
      margin-top: 0.1rem;
      padding-bottom: 0.02rem;
      display: inline-block;
      white-space: normal;
      cursor: pointer;
      background: #ffd600;
      border: 1.5px solid #7719be;
      transition: 0.1s;
      user-select: none;
      outline: none;
      border-radius: 0.08rem;
      position: relative;
      text-align: center;
      //font-family: YouSheBiaoTiHei;

      &:before {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        border-radius: inherit;
        border-bottom: 0.02rem solid #ff8100;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2), 0 2px 2px rgba(0, 0, 0, 0.14),
          0 3px 1px -2px rgba(0, 0, 0, 0.12);
      }

      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 0.02rem;
        background: transparent;
        border-radius: 0.16rem;
        position: absolute;
        bottom: 0.02rem;
        border-bottom: 1.5px solid #7719be;
      }

      &:active {
        transform: translateY(2px);
      }

      .btn-text {
        font-size: 0.14rem;
        font-weight: 600;
        color: #313131;
      }

      .count-down {
        .van-count-down {
          font-size: 0.12rem;
          color: #6b5f23;
          line-height: initial;
        }
      }
    }
  }

  :deep(.van-popup) {
    background: transparent;
  }
</style>
