<template>
  <div class="order">
    <div class="goods-top">
      <van-row class="buy-other" justify="space-between" align="center">
        <van-col class="buy-time">{{ order.createTime }}</van-col>
        <van-col class="goods-state">{{ order.orderStatus.statusName }}</van-col>
      </van-row>
      <div class="buy-details">
        <van-image
          class="goods-images"
          round
          fit="cover"
          width="0.74rem"
          height="0.74rem"
          :src="getOssURL(order.orderItemList[0].imageUrl)"
        />
        <div class="goods-info">
          <div>
            <van-row justify="space-between" align="center">
              <van-col class="goods-name omit">
                <span>{{ order.orderItemList[0].spuName }}｜</span>
                <span>{{ order.orderItemList[0].skuName }}</span>
              </van-col>
              <van-col class="buy-price">¥{{ order.orderItemList[0].totalAmount }}</van-col>
            </van-row>
          </div>
          <div class="goods-spec">授课方式：{{ order.orderItemList[0].teachingWay.typeName }}</div>
          <div class="buy-number">课时数：{{ order.orderItemList[0].quantity }}个课时</div>
          <van-row justify="end" align="center">
            <van-col>
              <div class="real-pay">
                实付款：<span>¥{{ order.paymentAmount }}</span>
              </div>
            </van-col>
            <van-col v-if="order.afterSaleStatus.status !== 'NONE'">
              <div class="buy-state">
                <div class="line" />
                {{ order.afterSaleStatus.statusName }}
              </div>
            </van-col>
          </van-row>
        </div>
      </div>
    </div>
    <div class="goods-bottom">
      <van-row justify="space-between" align="center">
        <van-col>
          <!-- <a @click.stop class="purchaser" :href="'tel:' + order.studentInfo.mobile">
            <div class="purchaser-head-portrait">
              <img :src="getOssURL(order.studentInfo.avatarUrl)" alt="" />
            </div>
            <div class="purchaser-name">{{ order.studentInfo.studentName }}</div>
            <div class="icon-phone-call"></div>
          </a> -->
        </van-col>
        <van-col class="residue">
          剩余
          <span class="showy">{{ order.showRemainQuantity }}</span>
          个课时
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script setup>
  import { getOssURL } from '@/common'

  defineProps({
    order: {
      type: Object,
      default: () => {},
    },
  })
</script>

<style lang="scss" scoped>
  .order {
    background: #fff;
    padding: 0 0.15rem;
    margin: 0.08rem 0;
    border-radius: 0.06rem;

    .goods-top {
      border-bottom: 1px solid #eeeeee;
    }

    .buy-other {
      padding: 0.08rem 0;
    }

    .buy-time {
      font-size: 0.12rem;
      color: #b2b1b7;
    }

    .goods-state {
      color: #ff9b26;
    }

    .buy-details {
      display: flex;
      padding-bottom: 0.1rem;
    }

    .goods-images {
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }

    .real-pay {
      font-size: 0.14rem;
      span {
        font-size: 0.16rem;
        color: #1a1b1d;
      }
    }

    .line {
      width: 1px;
      height: 0.12rem;
      background: #b2b1b7;
      display: inline-block;
      margin: 0.06rem 0.05rem 0.06rem 0.06rem;
      vertical-align: top;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      vertical-align: top;
      height: 0.24rem;
      line-height: 0.24rem;
    }

    .refund {
      color: #ff6445;
    }

    .refund-success {
      color: #ff9b26;
    }

    .goods-bottom {
      padding: 0.1rem 0;

      .purchaser {
        display: flex;
        align-items: center;
        background: #f7f7f7;
        border-radius: 0.16rem;
        padding-right: 0.14rem;
      }

      .purchaser-head-portrait {
        width: 32px;
        height: 32px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .purchaser-name {
        margin-left: 0.07rem;
        color: #1a1b1d;
      }

      .icon-phone-call {
        width: 0.17rem;
        height: 0.16rem;
        background: url('../../../../assets/images/coach-worktable/icon-phone-call.png') no-repeat;
        background-size: 100% 100%;
        margin-left: 0.06rem;
      }

      .residue {
        font-size: 0.14rem;
        //font-weight: bold;
        color: #1a1b1d;

        .showy {
          color: #ff6445;
          font-weight: bold;
        }
      }
    }
  }
</style>
