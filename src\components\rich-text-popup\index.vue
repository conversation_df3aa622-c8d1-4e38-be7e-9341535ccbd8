<template>
  <van-popup class="popup-contain" teleport="#app" :overlay="false" position="top" duration="0">
    <!-- <i class="icon icon-close" @click="closePop" /> -->
    <div class="page">
      <quill-editor
        v-model="content"
        @focus="editorFocus"
        @blur="editorBlur"
        placeholder="请输入正文"
        @ready="onReady"
      />
    </div>
    <div class="fixed-bottom" v-show="footerShow" @click="save">
      <button class="i-button publish-btn">保存</button>
    </div>
  </van-popup>
</template>

<script setup>
  import { ref } from 'vue'
  import QuillEditor from '@/components/quill-editor'
  const content = ref('')
  let props = defineProps({
    vContent: {
      type: String,
      default: '',
    },
  })
  const emit = defineEmits(['finish', 'update:show'])

  const onReady = () => {
    content.value = props.vContent
  }

  const onClose = () => {
    emit('update:show', false)
  }

  const save = () => {
    emit('finish', content)
    onClose()
  }

  const footerShow = ref(true)
  const editorFocus = () => {
    footerShow.value = false
  }

  const editorBlur = () => {
    footerShow.value = true
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';
  @include Icon('close', 0.4rem, 0.4rem);
  .page {
    width: 100vw;
    padding: 0.15rem 0.2rem 0.5rem 0.2rem;
  }

  .popup-contain {
    position: relative;
    overflow: hidden !important;

    .icon {
      position: absolute;
      right: 0;
      top: -0.05rem;
    }
  }
  .save {
    position: fixed;
    bottom: 0;
  }

  .fixed-bottom {
    width: 3.75rem;
    height: 0.6rem;
    position: fixed;
    bottom: 0;
    text-align: center;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    background-color: #fff;

    .publish-btn {
      width: 3.45rem;
      height: 0.4rem;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.2rem;
      font-size: 0.16rem;
      color: #fff;
    }
  }
</style>
