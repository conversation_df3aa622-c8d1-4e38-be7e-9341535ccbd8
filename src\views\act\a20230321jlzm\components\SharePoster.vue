<template>
  <van-overlay z-index="3000" :show="show" @click="show = false">
    <div v-if="show" class="SharePoster" @click.stop>
      <div class="poster" ref="posterSourceRef">
        <div class="poster-content">
          <div class="user-info">
            <img class="avatar" :src="userAvatar" crossorigin="anonymous" v-default-avatar />
            <div>
              <p class="username">{{ coach.coachName }}</p>
              <p class="desc">{{ coach.coachTitle }}</p>
            </div>
          </div>
          <div class="tags">
            <span v-if="coach.teachYear">{{ coach.teachYear }}年经验</span>
            <span v-if="coach.teachArea">{{ coach.teachArea }}</span>
          </div>
          <img class="welfare" src="../images/welfare.png" />
          <div class="qr-code-wrap">
            <div class="left">
              <p>长按识别二维码</p>
              <p>给我点赞 >></p>
            </div>
            <div ref="qrCodeRef" class="qr-code"></div>
          </div>
        </div>
      </div>

      <div class="prod-poster">
        <p class="title">转发海报，获得更多点赞</p>
        <p class="tip">长按图片保存海报</p>
        <div class="poster-img">
          <img v-if="posterImage" :src="posterImage" alt="" />
          <van-loading class="poster-loading" v-else vertical color="#1989fa">
            海报生成中...
          </van-loading>
        </div>
        <div class="close-btn" @click="onClose"></div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
  import { ref, watch, nextTick, computed } from 'vue'
  import QRCode from 'qrcodejs2'
  import html2canvas from 'html2canvas'
  import { useParent } from '@vant/use'
  import { baseURL } from '@/config'
  import { localProxyStorage } from '@/utils/storage'
  import { getOssURL, isLogin } from '@/common'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const { parent } = useParent('321ACT')

  const coach = computed(() => {
    return parent.coachShareInfo.value
  })

  const userAvatar = computed(() => {
    return (
      getOssURL(coach.value.coachImage) +
      '?x-oss-process=image/resize,m_fill,h_480,w_480' +
      '&v=' +
      new Date().getTime()
    )
  })

  const show = ref(props.modelValue || false)
  const qrCodeRef = ref(null)
  const posterSourceRef = ref(null)
  const posterImage = ref(null)
  const userInfo = localProxyStorage.user || {}

  watch(
    () => props.modelValue,
    (val) => {
      show.value = val
      if (val) {
        initPoster()
      }
    },
  )

  const onClose = () => {
    emit('update:modelValue', false)
  }

  const generateQRCode = (elem, link) => {
    elem.innerHTML = ''
    let clientWidth =
      document.documentElement.clientWidth > 480 ? 480 : document.documentElement.clientWidth
    let long = clientWidth * (62 / 375)
    new QRCode(elem, {
      width: long,
      height: long,
      text: link,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correctLevel: QRCode.CorrectLevel.L,
    })
  }

  const generatePoster = () => {
    return new Promise((resolve, reject) => {
      let elem = posterSourceRef.value
      let devicePixelRatio = window.devicePixelRatio
      // 用getBoundingClientRect 是为了解决白边问题
      let width = elem.getBoundingClientRect().width * 2
      let height = elem.getBoundingClientRect().height * 2

      let canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height

      html2canvas(elem, {
        canvas: canvas,
        width: width,
        height: height,
        scale: 2,
        dpi: devicePixelRatio,
        useCORS: true,
      })
        .then((canvas) => {
          resolve(canvas.toDataURL())
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const initPoster = async () => {
    posterImage.value = null
    await nextTick()
    let link = `${baseURL}/act/a20230321jlzm?shareUserId=${coach.value.coachUserId}`

    if (isLogin()) {
      link += '&shareId=' + (userInfo.shareId || '')
    }

    generateQRCode(qrCodeRef.value, link)
    setTimeout(() => {
      generatePoster().then((dataURL) => {
        posterImage.value = dataURL
      })
    }, 1000)
  }
</script>

<style lang="scss" scoped>
  .poster {
    width: 3.15rem;
    height: 4.07rem;
    background: url('../images/poster-img.png') no-repeat;
    background-size: 100% 100%;
    margin: 1.28rem auto 0 auto;
    position: fixed;
    top: 0;
    left: -999999999px;

    .poster-content {
      position: absolute;
      left: 0.4rem;
      top: 0.4rem;
    }

    .user-info {
      display: flex;
      padding-left: 0.05rem;

      .avatar {
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
        margin-right: 0.1rem;
      }

      .username {
        font-size: 0.16rem;
        color: #1a1b1d;
        letter-spacing: 0.1px;
      }

      .desc {
        color: #616568;
      }
    }

    .tags {
      margin-top: 0.15rem;
      display: flex;

      span {
        height: 0.2rem;
        line-height: 0.2rem;
        font-size: 0.13rem;
        color: #ff6445;
        background: #fff3e5;
        border-radius: 0.02rem;
        padding: 0 0.06rem;
        margin-right: 0.1rem;
        letter-spacing: 0.1px;
      }
    }

    .welfare {
      width: 2.35rem;
      height: 1.42rem;
      margin-top: 0.15rem;
    }

    .qr-code-wrap {
      margin-top: 0.15rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        font-size: 0.12rem;
        color: #616568;
        line-height: 0.17rem;
        letter-spacing: 0.1px;
      }

      .qr-code {
        width: 0.62rem;
        height: 0.62rem;
      }
    }
  }

  .prod-poster {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 0.64rem;

    .title {
      font-size: 0.16rem;
      font-weight: 600;
      color: #ffffff;
    }

    .tip {
      font-size: 0.14rem;
      color: #ffffff;
      margin-top: 0.05rem;
      letter-spacing: 0.1px;
    }

    .poster-img {
      width: 3.15rem;
      height: 4.07rem;
      margin-top: 0.2rem;

      img {
        width: 100%;
      }
    }

    .poster-loading {
      text-align: center;
      padding-top: 1.6rem;
    }

    .close-btn {
      width: 0.6rem;
      height: 0.6rem;
      background: url('../images/active-popup-close.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
