<template>
  <div class="Marquee">
    <van-notice-bar class="marquee" v-if="marquee.length > 0" :delay="0">
      <li v-for="item in marquee" :key="item.id">
        <img
          :src="getOssURL(item.avatar + '?x-oss-process=image/resize,m_fill,h_480,w_480')"
          alt=""
        />
        <span>{{ item.message }}</span>
      </li>
    </van-notice-bar>
    <van-notice-bar v-if="marquee1.length > 0" class="marquee" :delay="0">
      <li v-for="item in marquee1" :key="item.id">
        <img
          :src="getOssURL(item.avatar + '?x-oss-process=image/resize,m_fill,h_480,w_480')"
          alt=""
        />
        <span>{{ item.message }}</span>
      </li>
    </van-notice-bar>
  </div>
</template>

<script setup>
  import { shallowRef } from 'vue'
  import { getOssURL } from '@/common'

  import { reqActMarquee } from '@/views/act/a20230321jlzm/api'

  const marquee = shallowRef([])
  const marquee1 = shallowRef([])

  const slicer = (data, len) => {
    if (!Array.isArray(data)) return []

    const arr = []
    for (let i = 0; i < data.length; i += len) {
      arr.push(data.slice(i, i + len))
    }
    return arr
  }

  const initMarquee = () => {
    reqActMarquee().then((res) => {
      const { data } = res
      const group = slicer(data, 15)
      marquee.value = group[0] || []
      marquee1.value = group[1] || []
    })
  }

  initMarquee()
</script>

<style lang="scss" scoped>
  .Marquee {
    position: absolute;
    width: 3.75rem;
    top: 0.08rem;
  }

  .marquee {
    //width: 100%;
    background: transparent;
    height: 0.24rem;
    padding: 0;
    line-height: 0.2rem;

    img {
      width: 0.2rem;
      height: 0.2rem;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 0.06rem;
    }

    li {
      display: inline-flex;
      background: rgba(0, 0, 0, 0.5);
      height: 0.2rem;
      align-items: center;
      margin-right: 0.5rem;
      border-radius: 0.2rem;
      padding-right: 0.1rem;
      font-size: 0.12rem;
      color: #fff;
    }
  }
</style>
