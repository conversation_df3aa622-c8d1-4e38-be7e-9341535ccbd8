<template>
  <div v-show="pageShow" class="container min-height-100">
    <div class="goods" v-for="item in orderDetail.orderItemList" :key="item.id">
      <van-image
        class="goods-images"
        round
        fit="cover"
        width="0.74rem"
        height="0.74rem"
        :src="getOssURL(item.imageUrl)"
      />
      <div class="goods-info">
        <div>
          <van-row justify="space-between" align="center">
            <van-col class="goods-name omit">
              <span>{{ item.spuName }}｜</span>
              <span>{{ item.skuName }}</span>
            </van-col>
            <van-col class="goods-price">¥{{ item.totalAmount }}</van-col>
          </van-row>
        </div>
        <div class="goods-spec">授课方式：{{ item.teachingWay.typeName }}</div>
        <div class="buy-number">课时数：{{ item.quantity }}个课时</div>
      </div>
    </div>
    <div class="form-wrap">
      <van-field
        class="input-wrap"
        v-model="form.contactName"
        :border="false"
        label="联系人"
        placeholder="请输入联系人名字"
      />
      <van-field
        class="input-wrap"
        v-model="form.contactMobile"
        :border="false"
        label="联系电话"
        placeholder="请输入联系人电话"
      />
      <div class="pay-select">
        <div class="label">支付方式</div>
        <div class="select">
          <div
            v-for="item in platform"
            :key="item.name"
            :class="['item', { selected: form.paymentType === item.code }]"
            @click="handledPlatformSelect(item.code)"
          >
            <div class="platform-wrap">
              <div class="platform-logo">
                <img :src="item.logo" alt="" />
              </div>
              <div class="platform-name">{{ item.name }}</div>
            </div>
            <div v-if="form.paymentType === item.code" class="icon-selected"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="tip">
      <span @click="dialogShow = true"><van-icon class="warning" name="warning-o" />退款说明</span>
    </div>
    <div class="pay-footer">
      <div>
        <span class="label">总计：</span>
        <span class="price-real">¥{{ orderDetail.paymentAmount }}</span>
        <span class="line-through">¥{{ orderDetail.totalAmount }}</span>
      </div>
      <button v-preventReClick class="pay-btn" @click="onLaunchPay">立即支付</button>
    </div>

    <i-dialog v-model:show="dialogShow" confirmButtonText="我知道了" title="退款说明">
      <div class="refund-tip">
        <!-- <p class="title">有关退款手续费：</p>-->
        <p>1、用户提交订单后，未上课（核销课时）申请退款可全额退回；</p>
        <p>
          2、已联系私教服务提供者并至少完成一节课时（核销一节课时），此时申请退款，爱教练平台将退回剩余未上课的课时对应的款项，已核销的课时对应的款项不予退回；
        </p>
      </div>
    </i-dialog>

    <!-- 给支付宝支付页面用 -->
    <router-view />
  </div>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import IDialog from '@/components/dialog'
  import alipay from '../../../public/static/alipay/ap'
  import weChatPay from '@/utils/weChat/pay'
  import { Dialog, Toast } from 'vant'
  import Schema from 'async-validator'
  import { validate } from '@/utils/validate'
  import { baseURL } from '@/config'
  import { payPlatform } from '@/common/enum'
  import { isWeChat } from '@/utils'
  import { getOssURL } from '@/common'
  import { sessionProxyStorage } from '@/utils/storage'
  import {
    reqCheckOrder,
    reqLaunchPay,
    reqModifyOrder,
    reqOrderDetails,
    reqSubmitOrder,
  } from '@/api/pay'

  const router = useRouter()
  const route = useRoute()
  const pageParams = route.query
  const dialogShow = ref(false)
  const orderNo = ref(pageParams.orderNo) // 存放订单号
  const pageShow = ref(true)
  const isWeChatEnv = ref(isWeChat()) // 是否微信环境
  const originalOrderData = ref(null) // 原订单数据
  const payLoading = ref(null)
  const orderDetail = ref({
    contactMobile: '', //联系人手机
    contactName: '', // 联系人姓名
    paymentAmount: 0, // 实付款金额
    totalAmount: 0, // 商品总金额
    orderItemList: [], //订单明细列表
  })
  const platform = reactive([
    {
      logo: require('../../assets/images/coach-worktable/icon-wx.png'),
      code: payPlatform.weChat,
      name: '微信支付',
    },
    {
      logo: require('../../assets/images/coach-worktable/icon-alipay.png'),
      code: payPlatform.alipay,
      name: '支付宝',
    },
  ])

  const form = reactive({
    contactMobile: '',
    contactName: '',
    paymentType: pageParams.payType || '',
  })

  const formValidator = new Schema({
    contactMobile: {
      required: true,
      validator(rule, value, callback) {
        if (!value) {
          callback('请输入联系人手机号码')
        }
        if (!validate('mobile', value)) {
          callback('请输入正确的手机号码')
        }
        callback()
      },
    },
    contactName: { required: true, message: '请输入联系人姓名' },
    paymentType: { required: true, message: '请选择支付方式' },
  })

  const handledPlatformSelect = (value) => {
    form.paymentType = value
  }

  // 获取订单详情
  const getOrderDetail = () => {
    // 页面参数有订单号，则通过订单详情接口获取数据
    if (orderNo.value) {
      reqOrderDetails({ id: orderNo.value }).then((res) => {
        const { data } = res
        originalOrderData.value = data
        orderDetail.value.contactMobile = form.contactMobile = data.contactMobile
        orderDetail.value.contactName = form.contactName = data.contactName
        orderDetail.value.paymentAmount = data.paymentAmount
        orderDetail.value.totalAmount = data.totalAmount
        orderDetail.value.orderItemList = data.orderItemList
      })
    } else {
      let params = {
        classesId: pageParams.classesId, // 授课id
        coachId: pageParams.coachId, // 教练id
        quantity: pageParams.quantity, // 课时数量
      }
      reqCheckOrder(params).then((res) => {
        const { data } = res
        orderDetail.value.contactMobile = form.contactMobile = data.contactMobile
        orderDetail.value.contactName = form.contactName = data.contactName
        orderDetail.value.paymentAmount = data.paymentAmount
        orderDetail.value.totalAmount = data.totalAmount
        orderDetail.value.orderItemList = data.orderItemList
      })
    }
  }

  // 下单
  const placeOrder = () => {
    return new Promise((resolve) => {
      let params = {
        classesId: pageParams.classesId,
        coachId: pageParams.coachId,
        quantity: pageParams.quantity,
        contactMobile: form.contactMobile,
        contactName: form.contactName,
      }

      // 有邀约码则携带上
      if (sessionProxyStorage?.shareId) {
        params.ijlShare = {
          shareId: sessionProxyStorage.shareId,
        }
      }

      reqSubmitOrder(params)
        .then((res) => {
          resolve(res.data)
        })
        .catch(() => {
          resolve()
          payLoading.value && payLoading.value.clear()
        })
    })
  }

  // 发起支付
  const onLaunchPay = () => {
    let formData = JSON.parse(JSON.stringify(form))
    formValidator
      .validate(formData, { first: true })
      .then(async () => {
        payLoading.value = Toast.loading({
          duration: 0,
          forbidClick: true,
          message: '加载中...',
        })

        if (orderNo.value) {
          // 更改了联系人和姓名，则需要更改订单信息
          if (
            form.contactMobile !== originalOrderData.value.contactMobile ||
            form.contactName !== originalOrderData.value.contactName
          ) {
            await reqModifyOrder({
              contactMobile: form.contactMobile,
              contactName: form.contactName,
              orderId: orderNo.value,
            })
          }
        } else {
          orderNo.value = await placeOrder()
        }

        if (orderNo.value) {
          let params = {
            orderId: orderNo.value,
            paymentSource: 'wxh5',
            paymentType: form.paymentType,
          }

          if (params.paymentType === payPlatform.alipay) {
            params.returnUrl = baseURL + '/pay/ap-success' //支付宝支付成功跳转链接
            params.quitUrl = baseURL + '/pay/ap-error' // 支付宝退出支付返回的链接
          }

          // 发起支付,获取支付参数 调用第三方支付控件
          reqLaunchPay(params).then((res) => {
            const { data } = res
            payLoading.value && payLoading.value.clear()
            payControl(params.paymentType, data.payload)
          })
        } else {
          payLoading.value && payLoading.value.clear()
        }
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  const handledWeChatPay = (data) => {
    weChatPay(data, (res) => {
      const { err_msg, err_desc } = res
      // 支付成功
      if (err_msg === 'get_brand_wcpay_request:ok') {
        toOrderDetails()
      }
      // 支付过程中用户取消
      if (err_msg === 'get_brand_wcpay_request:cancel') {
        toOrderDetails()
      }
      // 支付过程中失败
      if (err_msg === 'get_brand_wcpay_request:fail') {
        Dialog.alert({
          title: '支付失败',
          message: err_desc,
        })
      }
    })
  }

  const handleAlipay = (data) => {
    alipay.pay(data, `&orderNo=${orderNo.value}&payType=${form.paymentType}`)
  }

  const toOrderDetails = () => {
    router.replace({
      name: 'studentOrderDetails',
      query: {
        orderId: orderNo.value,
      },
    })
  }

  // 唤起支付控件
  const payControl = (type, data) => {
    if (type === payPlatform.weChat) {
      handledWeChatPay(data)
    }

    if (type === payPlatform.alipay) {
      handleAlipay(data)
    }
  }

  const init = () => {
    // 非微信环境，且有支付宝支付链接参数,不展示页面内容
    if (!isWeChatEnv.value && pageParams.goto) {
      pageShow.value = false
      return
    }

    getOrderDetail()
  }

  init()
</script>

<style lang="scss" scoped>
  .container {
    padding-bottom: 1rem;
  }

  .goods {
    display: flex;
    padding: 0.12rem 0.15rem;
    background: #fff;

    .goods-images {
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .goods-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }
  }

  .form-wrap {
    background: #fff;
    margin-top: 0.08rem;
    padding: 0 0.15rem;

    .input-wrap {
      padding: 0.17rem 0;
      font-size: 0.14rem;
      line-height: 0.24rem;
      border-bottom: 1px solid #eee;

      :deep(.van-field__label) {
        width: 0.66rem;
        //padding-left: 0.1rem;
        margin-right: 0.2rem;
        color: #453938;
      }
    }

    .pay-select {
      padding-bottom: 0.15rem;

      .label {
        padding: 0.12rem 0 0.1rem 0;
        font-size: 0.16rem;
        font-weight: bold;
        color: #1a1b1d;
      }

      .select {
        .item {
          display: flex;
          align-items: center;
          padding: 0.08rem 0.15rem;
          background: #ffffff;
          border-radius: 0.04rem;
          border: 1px solid #eeeeee;

          &:not(:last-child) {
            margin-bottom: 0.15rem;
          }
        }

        .platform-wrap {
          display: flex;
          flex: 1;
          align-items: center;
        }

        .selected {
          border: 1px solid #ff9b26;
        }

        .icon-selected {
          width: 0.15rem;
          height: 0.15rem;
          background: url('../../assets/images/coach-worktable/icon-selected.png') no-repeat;
          background-size: 100% 100%;
        }

        .platform-name {
          font-size: 0.14rem;
          color: #1a1b1d;
          margin-left: 0.06rem;
        }

        .platform-logo {
          width: 0.24rem;
          height: 0.24rem;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .tip {
    margin-top: 0.1rem;
    padding: 0 0.15rem;
    color: #b2b1b7;

    .warning {
      margin-right: 0.04rem;
    }
  }

  .refund-tip {
    padding: 0 0.26rem 0.23rem 0.26rem;

    .title {
      color: #616568;
      margin-bottom: 0.06rem;
    }

    p {
      color: #1a1b1d;
      margin-bottom: 0.06rem;
    }
  }

  .pay-footer {
    position: fixed;
    bottom: 0;
    background-color: #fff;
    width: 3.75rem;
    height: 0.6rem;
    padding: 0 0.15rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .label {
      font-size: 0.16rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .price-real {
      font-size: 0.16rem;
      font-weight: bold;
      color: #ff6445;
    }

    .line-through {
      text-decoration: line-through;
      color: #b2b1b7;
      margin-left: 0.07rem;
    }

    .pay-btn {
      padding: 0.1rem 0.28rem;
      font-size: 0.16rem;
      color: #ffffff;
      background: #ff9b26;
      border-radius: 0.23rem;
    }
  }
</style>
