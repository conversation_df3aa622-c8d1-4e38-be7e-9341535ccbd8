export const noticeList = [
  { avatar: '/signHead/1.png', messages: '欢迎李**申请入驻教练' },
  { avatar: '/signHead/2.png', messages: '欢迎159****4660注册爱教练' },
  { avatar: '/signHead/3.png', messages: '广州天河区教练杨**发布教练信息啦!' },
  { avatar: '/signHead/4.png', messages: '广州越秀区场馆王**发布场馆信息啦!' },
  { avatar: '/signHead/5.png', messages: '广州海珠区教练李**发布培训信息啦!' },
  { avatar: '/signHead/6.png', messages: '深圳福田区教练周**发布教学文章啦!' },
  { avatar: '/signHead/7.png', messages: '佛山南海区教练陈**发布教学视频啦!' },
  { avatar: '/signHead/8.png', messages: '深圳南山区教练林**发布招聘信息啦!' },
]

// 平台特性
export const platformFeature = [
  { label: '网站运营', value: '10年+', icon: require('../../assets/images/home/<USER>') },
  {
    label: '入驻教练',
    value: '14000+',
    icon: require('../../assets/images/home/<USER>'),
  },
  {
    label: '服务用户',
    value: '120万+',
    icon: require('../../assets/images/home/<USER>'),
  },
  { label: '覆盖城市', value: '120+', icon: require('../../assets/images/home/<USER>') },
]

export const moduleEntry = [
  { path: '/news-list', image: require('../../assets/images/home/<USER>') },
  { path: '/video-list', image: require('../../assets/images/home/<USER>') },
  { path: '/train-list', image: require('../../assets/images/home/<USER>') },
  { path: '/help', image: require('../../assets/images/home/<USER>') },
]
