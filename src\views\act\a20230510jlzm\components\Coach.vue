<template>
  <div class="Coach">
    <div class="count-down">
      <van-count-down :time="time" format="DD 天 HH:mm:ss " />
    </div>

    <div class="slogan">我要成为明星教练，服务更多的学员</div>

    <div class="coach-info">
      <div class="content">
        <van-popover
          v-model:show="showPopover"
          :show-arrow="false"
          placement="top-start"
          :offset="[-15, 0]"
        >
          <div class="popover">
            <div class="flex">
              <img
                v-default-avatar
                class="avatar"
                :src="getOssURL(helpCoachInfo.coachInfo.coachImage)"
              />
              <div class="user-info">
                <p class="username omit">{{ helpCoachInfo.coachInfo.coachName }}</p>
                <p class="desc omit">{{ helpCoachInfo.coachInfo.coachTitle }}</p>
                <div class="tags">
                  <span v-if="helpCoachInfo.coachInfo.teachYear">
                    {{ helpCoachInfo.coachInfo.teachYear }}年经验
                  </span>
                  <span v-if="helpCoachInfo.coachInfo.teachArea">
                    {{ helpCoachInfo.coachInfo.teachArea }}
                  </span>
                </div>
              </div>
            </div>
            <button
              v-if="isSelf"
              class="edit-btn"
              @click="$router.push('/act/a20230510jlzm/enter-for?actionType=edit')"
            >
              修改信息
            </button>
          </div>
          <template #reference>
            <div class="avatar">
              <img v-default-avatar :src="getOssURL(helpCoachInfo.coachInfo.coachImage)" alt="" />
            </div>
          </template>
        </van-popover>
        <div>
          <p class="username">{{ helpCoachInfo.coachInfo.coachName }}</p>
          <p class="tag">{{ helpCoachInfo.coachInfo.coachTitle }}</p>
        </div>
      </div>
      <div class="like">
        <span class="num">{{ helpCoachInfo.coachInfo.likeCount }}</span>
        <span>赞</span>
      </div>
    </div>

    <!-- 排名 -->
    <!-- 数量÷总数×100 公式 -->
    <div v-if="helpCoachInfo.coachInfo.ranking !== 1" class="ranking">
      <div class="cur-ranking">第{{ helpCoachInfo.coachInfo.ranking }}名</div>
      <div class="progress">
        <div class="progress-bar">
          <div class="progress-portion" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="bubble-box" ref="bubbleRef" :style="{ left: bubbleLeft }">
          还有{{ rankingGap }}个赞就可超越啦！
        </div>
      </div>
      <div class="prev-ranking">第{{ helpCoachInfo.prevRanking }}名</div>
    </div>
    <div class="first" v-else>第一名</div>

    <div class="operational">
      <SolidButton v-if="!isLike" @click="onLike">点赞</SolidButton>
      <SolidButton v-else-if="isSelf" @click="onShare">分享集赞</SolidButton>
      <SolidButton v-else @click="onShare">帮ta拉赞</SolidButton>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useParent } from '@vant/use'
  import { getOssURL } from '@/common'
  import { getDateTime } from '@/utils/day'
  // import LikesBtn from "./LikesBtn";
  import SolidButton from './SolidButton'

  const { parent } = useParent('321ACT')

  const showPopover = ref(false)
  const bubbleRef = ref(null)

  const isLike = computed(() => {
    return !!parent.actInfo.value.currentLikeCoachUserId
  })

  const time = computed(() => {
    const date = parent.actInfo.value.endTime.replaceAll('-', '/')
    return getDateTime(date) - getDateTime()
  })

  const helpCoachInfo = computed(() => {
    return parent.actInfo.value.helpCoachInfo
  })

  const rankingGap = computed(() => {
    return helpCoachInfo.value.prevRankingLikeCount - helpCoachInfo.value.coachInfo.likeCount + 1
  })

  const progress = computed(() => {
    let res =
      (helpCoachInfo.value.coachInfo.likeCount / (helpCoachInfo.value.prevRankingLikeCount + 1)) *
      100
    return isNaN(res) ? 0 : res
  })

  const bubbleLeft = computed(() => {
    let bubbleWidth = bubbleRef.value?.offsetWidth / 2 - 3
    return `calc(${progress.value}% - ${bubbleWidth}px)`
  })

  const isSelf = computed(() => {
    return (
      parent.actInfo.value.loginUserId === parent.actInfo.value.helpCoachInfo.coachInfo.coachUserId
    )
  })

  const onLike = () => {
    parent.giveLike()
  }

  const onShare = () => {
    parent.openSharePopup(helpCoachInfo.value.coachInfo)
  }
</script>

<style lang="scss" scoped>
  @import '../font/index.css';

  .Coach {
    width: 3.5rem;
    height: 3.11rem;
    border-radius: 0.08rem;
    background: url('../images/bg-1.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin: 0 auto;
    user-select: none;
  }

  .count-down {
    position: absolute;
    left: 0;
    top: 0;
    width: 1.41rem;
    height: 0.34rem;

    .van-count-down {
      text-align: center;
      line-height: 0.34rem;
      font-size: 0.14rem;
      color: #ffd600;
      font-family: YouSheBiaoTiHei;
    }
  }

  .slogan {
    position: absolute;
    top: 0.4rem;
    left: 0.2rem;
    font-size: 0.21rem;
    font-family: YouSheBiaoTiHei;
    color: #ffffff;
  }

  .coach-info {
    width: 3.39rem;
    height: 1.1rem;
    background: url('../images/coach-bg.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0.06rem;
    top: 0.66rem;

    .content {
      position: absolute;
      left: 0.26rem;
      top: 0.27rem;
      display: flex;
    }

    .avatar {
      width: 0.52rem;
      height: 0.52rem;
      border-radius: 50%;
      margin-right: 0.08rem;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .username {
      font-size: 0.16rem;
      color: #1f1f1f;
      font-weight: bold;
    }

    .tag {
      color: #655d58;
      margin-top: 0.04rem;
    }

    .like {
      position: absolute;
      top: 0.33rem;
      right: 0.18rem;
      color: #ffe8e3;
      display: flex;
      justify-content: center;
      align-items: center;

      .num {
        font-size: 0.24rem;
        font-weight: 600;
        color: #ffffff;
        margin-right: 0.02rem;
      }

      span {
        line-height: 0.33rem;
      }
    }
  }

  .ranking {
    width: 100%;
    padding: 0 0.05rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 1.05rem;
    left: 0;

    .cur-ranking {
      text-align: center;
      color: #fff;
      flex: 1;
      font-weight: bold;
    }

    .prev-ranking {
      flex: 1;
      color: #fff;
      text-align: center;
      font-weight: bold;
    }

    .progress {
      width: 2.06rem;
      height: 0.16rem;
      background: #2244a6;
      border-radius: 0.14rem;
      margin: 0 0.05rem;
      padding: 0.01rem 0.03rem;
      position: relative;

      .progress-bar {
        width: 100%;
        height: 0.14rem;
        background-size: 100% 100%;
        border-radius: 0.14rem;
      }

      .progress-portion {
        width: 100%;
        height: 0.13rem;
        background: rgb(255, 214, 0);
        border-radius: 0.14rem;
      }
    }
  }

  .bubble-box {
    width: max-content;
    height: 0.24rem;
    position: absolute;
    left: 0;
    padding: 0 0.1rem;
    line-height: 0.24rem;
    background: #ffd600;
    border-radius: 0.15rem;
    text-align: center;
    margin-top: 0.1rem;
    font-size: 0.12rem;
    font-weight: 600;
    color: #000000;
  }

  .bubble-box::before {
    content: ' ';
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    margin-top: -0.11rem;
    border: 0.06rem solid transparent;
    border-bottom-color: #ffd96a;
  }

  .btn {
    width: 3.06rem;
    height: 0.47rem;
    position: absolute;
    top: 2rem;
    left: 0.22rem;
    cursor: pointer;
  }

  .like-btn {
    width: 3.5rem;
    position: absolute;
    top: 2rem;
    left: 0;
    cursor: pointer;
  }

  .operational {
    width: 3.22rem;
    position: absolute;
    left: 0.15rem;
    bottom: 0.12rem;
  }

  .first {
    width: 100%;
    height: 0.4rem;
    line-height: 0.4rem;
    text-align: center;
    background: url('../images/sort-bg.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 1.78rem;
    left: 0;
    font-size: 0.18rem;
    font-weight: 600;
    color: #313131;
  }

  .popover {
    width: 3.17rem;
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
    border-radius: 0.1rem;
    padding: 0.12rem 0 0.07rem 0.12rem;

    .avatar {
      width: 0.7rem;
      height: 0.7rem;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 0.12rem;
    }

    .edit-btn {
      font-size: 0.12rem;
      color: #0083fc;
      margin-left: 0.1rem;
    }

    .user-info {
      flex: 1;
      width: 2rem;
      padding-right: 0.26rem;
    }

    .username {
      font-size: 0.2rem;
      font-weight: 600;
      color: #1f1f1f;
      line-height: 0.28rem;
    }

    .desc {
      color: #1f1f1f;
      line-height: 0.2rem;
    }

    .tags {
      margin-top: 0.05rem;

      span {
        font-size: 0.13rem;
        padding: 0.02rem 0.06rem;
        background: #fff3e5;
        color: #ff6445;
        border-radius: 0.02rem;
        margin-right: 0.04rem;
      }
    }
  }
</style>
