<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="container min-height-100">
        <van-tabs
          class="tabs"
          v-model:active="tabIndex"
          sticky
          offset-top="0.44rem"
          @click-tab="handledTabsChange"
        >
          <van-tab v-for="(tab, index) in tabs" :key="index" :title="tab.title" :name="tab.name">
            <van-list
              :ref="(el) => (listRefs[index] = el)"
              v-model:loading="tab.loading"
              :finished="tab.finished"
              :finished-text="tab.emptyShow ? null : '-没有更多了-'"
              @load="() => onLoad(index)"
            >
              <template v-if="tab.name !== 4">
                <order-item
                  v-for="item in tab.list"
                  :key="item"
                  :order="item"
                  @click="toOrderDetails(item)"
                  @refundBtnClick="orderBtnReturn(item)"
                />
              </template>
              <template v-else>
                <refund-order-item
                  v-for="order in tab.list"
                  :key="order.id"
                  :order="order"
                  @click="toRefundOrderDetail(order)"
                  @refundBtnClick="handledRefund(order)"
                />
              </template>
            </van-list>
            <empty v-if="tab.emptyShow" top="1.9rem" :description="tab.emptyDesc" />
          </van-tab>
        </van-tabs>
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'studentOrder' }
</script>
<script setup>
  import { ref, reactive, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { tabList } from '@/views/student-module/order/tabs'
  import Empty from '@/components/empty'
  import { Toast, Dialog } from 'vant'
  import OrderItem from './components/OrderItem'
  import {
    getOrderList,
    getAfterSaleList,
    getStuAfterSaleCancel,
    checkCanApply,
  } from '@/api/trade-server'
  import RefundOrderItem from './components/RefundOrderItem'
  import gm from '@/components/gm-popup'
  // import useKeepAliveStore from "@/store/keepAlive";

  // const keepAliveStore = useKeepAliveStore();
  // onBeforeRouteLeave((to) => {
  //   let pages = ["studentOrderRefundDetails"];
  //   if (!pages.includes(to.name)) {
  //     // 卸载缓存
  //     keepAliveStore.removeKeepAlive("studentOrder");
  //   }
  // });

  const router = useRouter()
  const tabIndex = ref(0)
  const listRefs = ref([])
  const tabs = reactive(tabList)

  // const orderList = ref([]);

  // 获取订单列表
  const getOderList = (index) => {
    tabs[index].pageNum += 1

    let params = {
      pageNum: tabs[index].pageNum,
      pageSize: 7,
      type: index,
    }

    getOrderList(params).then((res) => {
      const { data } = res

      tabs[index].list = tabs[index].list.concat(data)

      tabs[index].emptyShow = tabs[index].list.length === 0
      // 加载结束
      tabs[index].loading = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        tabs[index].finished = true
      }
    })
  }

  // 获取退费订单列表
  const getAfterSaleOrder = (index) => {
    tabs[index].pageNum += 1

    let params = {
      pageNum: tabs[index].pageNum,
      pageSize: 7,
      type: index,
    }
    getAfterSaleList(params).then((res) => {
      const { data } = res
      tabs[index].list = tabs[index].list.concat(data)
      tabs[index].emptyShow = tabs[index].list.length === 0

      // 加载结束
      tabs[index].loading = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        tabs[index].finished = true
      }
    })
  }
  const onLoad = (index) => {
    console.log('加载load')
    if (tabs[index].name !== 4) {
      getOderList(index)
    } else {
      getAfterSaleOrder(index)
    }
  }

  const handledTabsChange = (index) => {
    listRefs.value[index] && listRefs.value[index].check()
  }

  // 订单按钮回调
  const orderBtnReturn = (item) => {
    const status = item.orderStatus.status
    // 待支付
    if (status === 'OBLIGATION') {
      router.push({
        name: 'payCourse',
        query: {
          orderNo: item.id,
        },
      })
    }
    // 待上课/上课中
    // if (["PAID", "PART_CONSUMED"].includes(status)) {
    //   router.push({
    //     name: "studentClassDetails",
    //     query: {
    //       coachUserId: item.sellerId,
    //       isContactCoach: true,
    //     },
    //   });
    // }
    // 交易完成/交易关闭
    if (['FINISH', 'CANCELED'].includes(status)) {
      router.push({
        path: `/coach/details/${item.coachId}`,
        query: {
          isShowSku: true,
        },
      })
    }
  }

  const toOrderDetails = (item) => {
    router.push({
      name: 'studentOrderDetails',
      query: {
        orderId: item.id,
      },
    })
  }

  // 申请退款页
  const handledRefund = (order) => {
    console.log(order.id, 'order.id')
    // 取消退款
    if (order.afterSaleStatus.status === 'APPLIED') {
      Dialog.confirm({
        message: '是否确认取消退款？',
      })
        .then(() => {
          getStuAfterSaleCancel({ id: order.id }).then(() => {
            Toast('取消退款成功')
            // router.go(0);
            tabs[4].list = []
            tabs[4].loading = false
            tabs[4].finished = false
            tabs[4].pageNum = 0
            nextTick(() => {
              onLoad(4)
            })
          })
          // router.go(0);
        })
        .catch(() => {})
    }
    // 重新申请
    if (order.afterSaleStatus.status === 'CANCELED') {
      const params = {
        orderId: order.orderId,
        orderItemId: order.orderItemId,
      }
      checkCanApply(params).then((res) => {
        if (res.data) {
          router.push({
            name: 'studentOrderApplyRefund',
            query: {
              orderId: order.orderId,
              orderItemId: order.orderItemId,
            },
          })
        } else {
          Toast(res.msg)
          router.push({
            name: 'studentOrder',
          })
        }
      })
    }
    // 客服介入
    if (order.afterSaleStatus.status === 'REFUSED') {
      gm.open({
        title: '联系客服',
        desc: '添加企业微信，在线联系客服',
      })
    }
  }
  // 查看退款订单详情
  const toRefundOrderDetail = (order) => {
    router.push({
      name: 'studentOrderRefundDetails',
      query: {
        orderId: order.id,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .tabs {
    :deep(.van-tabs__wrap) {
      height: 0.45rem;
    }

    :deep(.van-tab) {
      flex: auto;
      font-size: 0.14rem;
      color: #616568;
      line-height: 0.2rem;
    }

    :deep(.van-tab--active) {
      font-size: 0.18rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    :deep(.van-tabs__line) {
      width: 0.18rem;
      height: 0.03rem;
      background: linear-gradient(90deg, #ff9b26 0%, #ff6445 100%);
      border-radius: 0.03rem;
      bottom: 0.2rem;
    }
    :deep(.van-tab__panel) {
      padding: 0 0.08rem;
    }
  }
</style>
