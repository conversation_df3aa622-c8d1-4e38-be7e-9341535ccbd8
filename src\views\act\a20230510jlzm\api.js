import http from '@/utils/axios'

const activityId = '2'

// 获取活动弹幕
export const reqActMarquee = (params) => {
  return http.get(`/generic-server/api/sem/coach-recruit-ranking/${activityId}/help-roll/nL`, {
    params,
  })
}

// 获取活动详情
export const reqActInfo = (params) => {
  return http.get(`/generic-server/api/sem/coach-recruit-ranking/${activityId}/nL`, {
    params,
  })
}

// 教练身份报名
export const reqCoachEnterFor = (params) => {
  return http.get(`/generic-server/api/activity-star-coach-sign/coachSign/${activityId}`, {
    params,
  })
}

// 非教练身份报名
export const reqUserEnterFor = (params) => {
  return http.post('/generic-server/api/activity-star-coach-sign/nonCoachSign', params)
}

// 获取活动中的排行榜
export const reqActRankingList = (params) => {
  return http.get(`/generic-server/api/sem/coach-recruit-ranking/${activityId}/ranking-list/nL`, {
    params,
  })
}

// 获取用户报名信息
export const reqUserRegistrationInfo = (params) => {
  return http.get('/generic-server/api/activity-star-coach-sign/getInfo', { params })
}

export const reqCoachInfo = (params) => {
  return http.get(`/generic-server/api/activity-star-coach-sign/coachSign/${activityId}`, {
    params,
  })
}

// 更新报名信息
export const reqUpdateRegistrationInfo = (params) => {
  return http.post('/generic-server/api/activity-star-coach-sign/updateSignInfo', params)
}

// user 点赞
export const reqUserLike = (params) => {
  return http.post(`/generic-server/api/sem/coach-recruit-ranking/${activityId}/like`, params)
}

// 分享统计
export const reqUserShareStats = (params) => {
  return http.post(`/generic-server/api/sem/coach-recruit-ranking/${activityId}/share/nL`, params)
}
