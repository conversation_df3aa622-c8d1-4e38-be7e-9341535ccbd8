module.exports = {
  root: true,
  env: {
    node: true,
    // 解決 vue3 Script Setup 语法 导致eslint 冲突
    'vue/setup-compiler-macros': true,
  },
  globals: {
    sensors: true,
  },
  extends: ['plugin:vue/vue3-essential', 'eslint:recommended', 'plugin:prettier/recommended'],
  // "plugin:prettier/recommended"
  parserOptions: {
    parser: '@babel/eslint-parser',
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prettier/prettier': ['error', { endOfLine: 'auto' }],
    // "prettier/prettier": 'off',

    //关闭组件命名规则
    'vue/multi-word-component-names': 'off',
    // 'vue/script-indent': ['error', 2, { baseIndent: 1 }],
  },
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)', '**/tests/unit/**/*.spec.{j,t}s?(x)'],
      env: {
        jest: true,
      },
    },
  ],
}
