import QRCode from 'qrcodejs2'

export const genQrCode = (config) => {
  const container = document.createElement('container')

  new QRCode(container, {
    width: config.width || 100,
    height: config.height || 100,
    text: config.link || '',
    colorDark: '#000000',
    colorLight: '#ffffff',
    correctLevel: QRCode.CorrectLevel.L,
  })

  return container.firstChild.toDataURL('image/png')
}
