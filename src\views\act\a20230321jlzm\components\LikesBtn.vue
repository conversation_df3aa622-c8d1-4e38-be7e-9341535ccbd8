<template>
  <div class="LikesBtn" ref="lottieBox" @click="onBtnClick($event)"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import lottie from 'lottie-web'
  import likeBtn from '@/views/act/a20230321jlzm/likeBtn.json'

  const lottieBox = ref(null)
  const anim = ref(null)

  const emit = defineEmits(['click'])

  onMounted(() => {
    if (lottieBox.value) {
      anim.value = lottie.loadAnimation({
        container: lottieBox.value,
        renderer: 'svg', // 渲染方式:svg：支持交互、不会失帧、canvas、html：支持3D，支持交互
        loop: true, // 循环播放，默认：true
        autoplay: true, // 自动播放 ，默认true
        animationData: likeBtn, //本地路径，优先级更高
      })

      anim.value.addEventListener('complete', () => {
        anim.value?.destroy()
      })
    }
  })

  onUnmounted(() => {
    anim.value?.destroy()
  })

  const onBtnClick = (e) => {
    let targetElem = e.target

    // 点击的按钮
    if (targetElem.tagName === 'image' && targetElem.getAttribute('width') === '620px') {
      emit('click')
    }
  }
</script>

<style lang="scss" scoped></style>
