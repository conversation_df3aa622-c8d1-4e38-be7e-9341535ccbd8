<template>
  <div class="ranking-list">
    <div class="tip">
      <img src="../images/welfare-1.png" alt="" />
      <span>点赞即可获得神秘大礼包！每天仅有一次机会哟～</span>
    </div>

    <div class="ranking-list-head">
      <div class="ranking-list-title" />
      <div class="ranking-list-desc">
        <button class="gift-btn" @click="openContactPopup">
          <img src="../images/icon-kf.png" alt="" />领取礼包
        </button>
        <div class="num">{{ count }}</div>
        <div>名教练上榜</div>
        <button class="refresh-btn" @click="onRefresh">刷新排名</button>
      </div>
    </div>

    <div class="table">
      <div class="table-thead">
        <div class="col-1-width th">排名</div>
        <div class="col-2-width th">教练</div>
        <div class="col-3-width th" style="text-align: center; padding-left: 0">拉赞/点赞</div>
      </div>
      <div class="table-body">
        <div v-for="(item, index) in list" :key="index" class="row">
          <div class="col-1-width col-1">
            <template v-if="[1, 2, 3].includes(index + 1)">
              <div :class="['medal-' + (index + 1)]" />
            </template>
            <span v-else>{{ index + 1 }}</span>
          </div>
          <div class="col-2-width col-2">
            <div class="coach">
              <img
                v-if="item.coachId"
                v-default-avatar
                class="coach-avatar"
                :src="getOssURL(item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')"
                @click="onItemClick(item)"
                alt=""
              />
              <van-popover
                v-else
                v-model:show="item.showPopover"
                :show-arrow="false"
                placement="top-start"
                :offset="[-25, -60]"
              >
                <div class="popover">
                  <img
                    v-default-avatar
                    class="avatar"
                    :src="
                      getOssURL(item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
                    "
                  />
                  <div class="user-info">
                    <p class="username omit">
                      {{ item.coachName }}
                    </p>
                    <p class="desc omit">{{ item.coachTitle }}</p>
                    <div class="tags">
                      <span v-if="item.teachYear">{{ item.teachYear }}年经验</span>
                      <span v-if="item.teachArea">{{ item.teachArea }}</span>
                    </div>
                  </div>
                </div>
                <template #reference>
                  <img
                    v-default-avatar
                    class="coach-avatar"
                    :src="
                      getOssURL(item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
                    "
                    alt=""
                  />
                </template>
              </van-popover>
              <div class="coach-info">
                <div class="flex" style="align-items: center">
                  <span class="coach-name omit">{{ item.coachName }}</span>
                  <span class="coach-tag omit">{{ item.coachTitle }}</span>
                </div>
                <div class="praise-num">
                  <div class="likes" :class="{ red: item.likeCount > 0 }">
                    {{ item.likeCount }}<span>赞</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-3-width col-3">
            <div class="action">
              <div v-if="isGiveLikeBtn" class="action-icon" @click="onGiveLike(item)">
                <div class="icon-praise" :class="{ active: item.isGiveLike }" />
                <span>点赞</span>
              </div>
              <div class="action-icon" @click="openSharePopup(item)">
                <div class="icon-share" />
                <span>分享</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="fetch-more">
        <div v-if="loading" class="list-loading-text">
          <van-loading size="0.14rem">加载中...</van-loading>
        </div>
        <div v-if="finished" class="list-finished-text">- 没有更多了 -</div>
        <button v-else-if="!finished && !loading" class="more-btn" @click="onLoadMore">
          查看更多教练
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { reqActRankingList, reqUserLike } from '@/views/act/a20230321jlzm/api'
  import { getOssURL, isLogin } from '@/common'
  import { useParent } from '@vant/use'
  import { Toast } from 'vant'

  const router = useRouter()
  const { parent } = useParent('321ACT')

  let group = []

  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const count = ref(0)

  const isGiveLikeBtn = computed(() => {
    return !parent.shareUserId || parent.shareUserId === parent.actInfo.value.loginUserId
  })

  const slicer = (data, len) => {
    if (!Array.isArray(data)) return []

    const arr = []
    for (let i = 0; i < data.length; i += len) {
      arr.push(data.slice(i, i + len))
    }
    return arr
  }

  const getRankingList = () => {
    loading.value = true
    reqActRankingList().then((res) => {
      const { data } = res
      count.value = data.count

      data.rankingList.forEach((item) => {
        item.showPopover = false
        item.isGiveLike = parent.actInfo.value.currentLikeCoachUserId === item.coachUserId
      })

      list.value = data.rankingList.splice(0, 20)
      loading.value = false
      finished.value = count.value <= 20

      group = slicer(data.rankingList, 10)
    })
  }

  const onLoadMore = () => {
    loading.value = true
    setTimeout(() => {
      list.value = list.value.concat(...group.splice(0, 1))
      loading.value = false
      finished.value = list.value.length === count.value
    }, 50)
  }

  const onRefresh = () => {
    group.length = 0
    finished.value = false
    list.value.length = 0
    getRankingList()
  }

  const openSharePopup = (data) => {
    parent.openSharePopup(data)
  }

  const onGiveLike = (item) => {
    if (isLogin()) {
      if (parent.actInfo.value.currentLikeCoachUserId) {
        if (!item.isGiveLike) {
          Toast('今日点赞次数用完啦，可以帮ta拉赞喔！')
        }
        return
      }

      let params = { coachUserId: item.coachUserId }
      reqUserLike(params).then(() => {
        // 开始点赞动画
        parent.playLikeAnim()
        parent.actInfo.value.currentLikeCoachUserId = item.coachUserId
        onRefresh()

        setTimeout(() => {
          if (
            parent.actInfo.value.loginUserId === item.coachUserId &&
            parent.actInfo.value.btnType === 2
          ) {
            parent.openSharePopup()
          } else {
            parent.openGiftPopup()
          }
        }, 3000)
      })
    } else {
      parent.openLoginPopup()
    }
  }

  // eslint-disable-next-line no-unused-vars
  const onItemClick = (item) => {
    router.push('/coach/details/' + item.coachId)
  }

  const openContactPopup = () => {
    parent.showContactPopup()
  }

  getRankingList()
</script>

<style lang="scss" scoped>
  .tip {
    display: flex;
    height: 0.42rem;
    align-items: center;
    justify-content: center;

    img {
      width: 0.22rem;
      height: 0.22rem;
      margin-right: 0.04rem;
    }

    span {
      font-size: 0.13rem;
      color: #cd691e;
      padding-top: 0.03rem;
    }
  }

  .ranking-list-head {
    margin-bottom: 0.12rem;

    .ranking-list-title {
      width: 100%;
      height: 0.52rem;
      background: url('../images/ranking-list-title.png') no-repeat;
      background-size: 100% 100%;
    }

    .ranking-list-desc {
      margin-top: 0.04rem;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 0.22rem;
      font-size: 0.12rem;
      color: #000000;
      position: relative;

      .gift-btn {
        position: absolute;
        left: 0.12rem;
        width: 0.8rem;
        height: 0.22rem;
        line-height: 0.22rem;
        background: rgba(243, 51, 8, 0.72);
        border-radius: 0.1rem;
        color: #fff;
        font-size: 0.12rem;

        img {
          width: 0.13rem;
          height: 0.15rem;
          margin-right: 0.03rem;
          vertical-align: sub;
        }
      }

      .refresh-btn {
        position: absolute;
        right: 0.12rem;
        line-height: 0.22rem;
        background: transparent;
        font-size: 0.12rem;
        color: #f33308;
        padding-left: 0.16rem;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 0.04rem;
          width: 0.12rem;
          height: 0.12rem;
          background: url('../images/icon-refresh.png') no-repeat;
          background-size: 100% 100%;
        }
      }

      .num {
        font-size: 0.16rem;
        color: #f4370b;
        margin-right: 0.02rem;
        font-weight: bold;
      }

      span {
        display: inline-block;
      }
    }
  }

  .table {
    width: 3.5rem;
    margin: 0 0.12rem;
    border-radius: 0.1rem;
    box-shadow: 0 0 0.06rem 0 #fec202;
    background: #fff;
    min-height: 2rem;
    overflow: hidden;

    .col-1-width {
      width: 0.46rem;
    }

    .col-2-width {
      width: 2.1rem;
    }

    .col-3-width {
      width: 0.95rem;
    }

    .table-thead {
      background: #f5f5f5;
      display: flex;

      .th {
        font-size: 0.12rem;
        color: #979797;
        padding: 0.07rem 0 0.09rem 0.1rem;
      }
    }

    .row {
      display: flex;
      align-items: center;
    }

    .col-1 {
      font-weight: 600;
      color: #979797;
      text-align: center;
    }
  }

  .fetch-more {
    padding: 0.15rem 0;
    text-align: center;

    .list-loading-text,
    .list-finished-text {
      color: #969799;
      font-size: 0.12rem;
      line-height: 0.32rem;
      text-align: center;
    }

    .more-btn {
      width: 2.24rem;
      height: 0.32rem;
      background: #f4f4f4;
      border-radius: 0.16rem;
      font-size: 0.12rem;
      color: #616568;
    }
  }

  .coach {
    display: flex;
    padding: 0.08rem 0;

    .coach-avatar {
      width: 0.44rem;
      height: 0.44rem;
      object-fit: cover;
      border-radius: 50%;
      margin-right: 0.1rem;
    }

    .coach-info {
      .coach-name {
        max-width: 0.6rem;
        font-size: 0.15rem;
        color: #1f1f1f;
        font-weight: bold;
      }

      .coach-tag {
        max-width: 0.96rem;
        font-size: 0.12rem;
        color: #616568;
        margin-left: 0.06rem;
      }

      .praise-num {
        font-size: 0.12rem;
        font-family: PingFangSC-Medium, PingFang SC;
        color: #b2b1b7;
      }

      .red {
        font-size: 0.16rem;
        color: #ff6445;

        span {
          font-size: 0.14rem;
        }
      }
    }
  }

  .action {
    display: flex;

    .action-icon {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .icon-praise {
        width: 0.18rem;
        height: 0.18rem;
        background: url('../images/icon-praise.png') no-repeat;
        background-size: 100% 100%;
      }

      .active {
        background: url('../images/icon-praise-active.png') no-repeat;
        background-size: 100% 100%;
      }

      .icon-share {
        width: 0.18rem;
        height: 0.18rem;
        background: url('../images/icon-share.png') no-repeat;
        background-size: 100% 100%;
      }

      span {
        margin-top: 0.03rem;
        font-size: 0.12rem;
        color: #979797;
      }
    }
  }

  .medal-1 {
    width: 0.23rem;
    height: 0.26rem;
    display: inline-block;
    background: url('../images/medal-1.png') no-repeat;
    background-size: 100% 100%;
  }

  .medal-2 {
    width: 0.23rem;
    height: 0.26rem;
    display: inline-block;
    background: url('../images/medal-2.png') no-repeat;
    background-size: 100% 100%;
  }

  .medal-3 {
    width: 0.23rem;
    height: 0.26rem;
    display: inline-block;
    background: url('../images/medal-3.png') no-repeat;
    background-size: 100% 100%;
  }

  .popover {
    width: 3.17rem;
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
    border-radius: 0.1rem;
    display: flex;
    padding: 0.12rem 0 0.12rem 0.12rem;

    .avatar {
      width: 0.7rem;
      height: 0.7rem;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 0.12rem;
    }

    .user-info {
      flex: 1;
      width: 2rem;
      padding-right: 0.1rem;
    }

    .username {
      font-size: 0.2rem;
      font-weight: 600;
      color: #1f1f1f;
      line-height: 0.28rem;
    }

    .desc {
      color: #1f1f1f;
      line-height: 0.2rem;
    }

    .tags {
      margin-top: 0.05rem;
      span {
        font-size: 0.13rem;
        padding: 0.02rem 0.06rem;
        background: #fff3e5;
        color: #ff6445;
        border-radius: 0.02rem;
        margin-right: 0.04rem;
      }
    }
  }
</style>
