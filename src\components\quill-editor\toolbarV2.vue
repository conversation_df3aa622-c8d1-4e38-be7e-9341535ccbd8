<template>
  <div id="quill-toolbar">
    <div class="toolbar-left">
      <button class="ql-packUp"></button>
    </div>
    <button class="ql-redo"></button>
    <button class="ql-undo"></button>
    <button class="ql-clean"></button>
    <button class="ql-link"></button>
    <button class="ql-image"></button>
    <select class="ql-align">
      <option selected></option>
      <option value="center"></option>
      <option value="right"></option>
      <option value="justify"></option>
    </select>
    <i-popover>
      <button class="ql-bold"></button>
      <button class="ql-italic"></button>
      <button class="ql-header" value="1"></button>
      <button class="ql-header" value="2"></button>
      <select class="ql-color"></select>
      <button class="ql-blockquote"></button>
      <button class="ql-list" value="ordered"></button>
      <button class="ql-list" value="bullet"></button>
      <template #reference>
        <button class="ql-fonts"></button>
      </template>
    </i-popover>
  </div>
</template>

<script setup>
  import IPopover from './popoverV2'
</script>

<style lang="scss" scoped>
  @import '@vueup/vue-quill/dist/vue-quill.snow.css';

  #quill-toolbar {
    width: 3.75rem;
    position: absolute;
    bottom: 0;
    right: 0;
    top: auto;
    left: 50%;
    z-index: 3;
    transform: translateX(-50%);
    background-color: #fff;
  }

  .toolbar-left {
    button {
      float: left !important;
    }
  }

  :deep(.ql-editor) {
    padding: 12px 0;
    font-size: 0.15rem;
  }

  :deep(.ql-blank) {
    &::before {
      color: #b2b1b7;
      content: attr(data-placeholder);
      font-style: initial;
      left: 0;
      pointer-events: none;
      position: absolute;
      right: 15px;
    }
  }

  // 文字大小
  :deep(.ql-size .ql-picker-options) {
    top: -189px !important;
  }

  // 文字颜色
  :deep(.ql-color .ql-picker-options) {
    top: -112px !important;
  }

  // 文字背景颜色
  :deep(.ql-background .ql-picker-options) {
    top: -110px !important;
  }

  // 标题
  :deep(.ql-header .ql-picker-options) {
    top: -132px !important;
  }

  // 内容排版
  :deep(.ql-align .ql-picker-options) {
    top: -1.28rem !important;

    .ql-picker-item {
      width: 0.3rem;
      height: 0.28rem;
    }
  }

  :deep(.ql-picker) {
    float: right;
  }

  .ql-snow.ql-toolbar button {
    width: 0.32rem;
    height: 0.28rem;
    float: right;
  }

  :deep(.ql-color-picker) {
    width: 0.32rem;
    height: 0.28rem;
  }

  :deep(.ql-icon-picker) {
    width: 0.32rem;
    height: 0.28rem;
  }

  :deep(.editor) {
    border: none;
  }

  $fontSizeArr: 12, 14, 16, 20, 24, 36;
  @each $item in $fontSizeArr {
    :deep(.ql-picker-item[data-value='#{$item}px']) {
      &::before {
        content: '#{$item}px';
      }
    }

    :deep(.ql-picker-label[data-value='#{$item}px']) {
      &::before {
        content: '#{$item}px';
      }
    }
  }
</style>
