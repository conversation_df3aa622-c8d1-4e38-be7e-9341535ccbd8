<template>
  <!-- 申请退款页 -->
  <page :title="$route.meta?.title">
    <template #page>
      <div class="apply-refund">
        <div class="goods-details">
          <van-image
            class="goods-images"
            round
            fit="cover"
            width="0.74rem"
            height="0.74rem"
            :src="getOssURL(refundInfo.orderItem?.imageUrl)"
          />
          <div class="goods-info">
            <div>
              <van-row justify="space-between" align="center">
                <van-col class="goods-name omit"
                  >{{ refundInfo.orderItem?.spuName }}｜
                  {{ refundInfo.orderItem?.skuName }}</van-col
                >
                <van-col class="buy-price">¥{{ totalPrice }}</van-col>
              </van-row>
            </div>
            <div class="goods-spec">授课方式：{{ refundInfo.orderItem?.teachingWay.typeName }}</div>
            <div class="buy-number">课时数：{{ refundInfo.orderItem?.quantity }}个课时</div>
          </div>
        </div>
        <h3 class="apply-title">退款原因</h3>
        <div class="class-late">
          <van-radio-group v-model="formData.refundReason" direction="horizontal">
            <van-radio v-for="(item, index) in tagList" :key="item.tagName" :name="index + 1">
              <template #icon="props">
                <div class="tag-check-item">
                  <span :class="{ 'tag-check-checked': props.checked }">{{ item.tagName }}</span>
                </div>
              </template>
            </van-radio>
          </van-radio-group>
        </div>
        <div class="split"></div>
        <div class="price-box">
          <div class="price-head flex">
            <h3 class="apply-title">退款金额</h3>
            <div class="flex" @click="showExplain">
              <i class="icon-tip"></i>
              <span>退款说明</span>
            </div>
          </div>
          <div class="price-num">¥{{ refundInfo.predictRefundAmount }}</div>
          <p class="details">
            剩余{{ refundInfo.remainQuantity }}个课时， 共{{ refundInfo.remainAmount }}元，
            预计可退{{ refundInfo.predictRefundAmount }}元。
          </p>
        </div>
        <h3 class="apply-title">其他想法 <span>（选填）</span></h3>
        <van-field
          v-model="formData.refundExplain"
          rows="4"
          type="textarea"
          maxlength="100"
          placeholder="我们将用心倾听你的想法～"
          show-word-limit
        />
        <div>
          <upload-file
            class="upload-file"
            preview-size="0.73rem"
            file-type="jpg|png|gif|jpeg"
            :max-size="10240 * 1024"
            v-model="formData.coachImages"
            :max-count="9"
          />
        </div>
        <BottomBtns isOnlyOne="left" leftBtnText="提交" @leftClick="submit" />

        <i-dialog v-model:show="isShowExplain" confirmButtonText="我知道了" title="退款说明">
          <div class="refund-tip">
            <p>1、用户提交订单后，未上课（核销课时）申请退款可全额退回；</p>
            <p>
              2、已联系私教服务提供者并至少完成一节课时（核销一节课时），此时申请退款，爱教练平台将退回剩余未上课的课时对应的款项，已核销的课时对应的款项不予退回；
            </p>
          </div>
        </i-dialog>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { reactive, ref, computed } from 'vue'
  import UploadFile from '@/components/upload-file'
  import BottomBtns from '@/components/basic/bottom-btns.vue'
  import { getStuApplyPreview, getStuAfterSaleApply } from '@/api/trade-server'
  import { useRoute, useRouter } from 'vue-router'
  import { getOssURL } from '@/common'
  import { Toast } from 'vant'

  const route = useRoute()
  const router = useRouter()

  const orderId = route.query.orderId
  const orderItemId = route.query.orderItemId
  const formData = reactive({
    refundReason: '', //	退款原因
    mediaUrls: [],
    orderId: '', //	订单id
    orderItemId: '', //	订单明细id
    refundExplain: '', //	退款描述
    coachImages: [],
  })

  const tagList = ref([
    { tagName: '临时有事' },
    { tagName: '教练无法联系' },
    { tagName: '身体原因' },
    { tagName: '天气原因' },
    { tagName: '不想上课了' },
    { tagName: '线下更优惠' },
  ])

  // 总金额
  const totalPrice = computed(() => {
    return refundInfo.value.orderItem?.quantity * refundInfo.value.orderItem?.price
  })
  // const labelTxt = ref([]);

  const refundInfo = ref({})
  const init = () => {
    const params = {
      orderId: orderId,
      orderItemId: orderItemId,
    }
    getStuApplyPreview(params).then((res) => {
      const { data } = res
      refundInfo.value = data
      formData.refundExplain = data.refundExplain || ''
      formData.mediaUrls = data.mediaUrls || ''
      // 图片回显
      if (Array.isArray(data.mediaUrls)) {
        formData.coachImages = data.mediaUrls.map((url) => {
          return { url: getOssURL(url), path: url }
        })
      }

      formData.refundReason = data.refundReason?.type
    })
    // getStuRefundReasons();
  }
  init()
  const handleFormData = () => {
    let form = JSON.parse(JSON.stringify(formData))
    form.mediaUrls = form.coachImages.map((item) => item.path)
    form.orderId = orderId
    form.orderItemId = orderItemId
    form.coachImages = []
    return form
  }

  const isShowExplain = ref(false)
  const showExplain = () => {
    isShowExplain.value = true
  }
  const submit = () => {
    if (!formData.refundReason) {
      Toast('请选择退款原因')
      return
    }
    let form = handleFormData()
    console.log(form, 'formData')
    getStuAfterSaleApply(form).then((res) => {
      Toast('退款申请已提交')
      router.replace({
        name: 'studentOrderRefundDetails',
        query: {
          orderId: res.data,
        },
      })
    })
  }
</script>

<style scoped lang="scss">
  @import '@/styles/mixins/mixins.scss';

  @include Icon('tip', 0.13rem, 0.13rem);
  .apply-refund {
    // height: 100vh;
    background-color: #fff;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .apply-title {
    margin-bottom: 0.08rem;
    font-size: 0.16rem;
    font-weight: 600;
    color: #1a1b1d;
    span {
      font-size: 0.12rem;
      color: #1a1b1d;
    }
  }
  :deep(.check-box span) {
    width: auto;
    margin-right: 0.15rem;
    margin-bottom: 0.08rem;
    padding: 0.04rem 0.08rem;
    font-size: 0.13rem;
    border-radius: 0.04rem;
    border: 0.01rem solid transparent;
  }

  :deep(.check-box .type-selected) {
    color: #ff9b26;
    background-color: #fff6e9;
    border: 0.01rem solid #ff9b26;
  }
  .apply-refund {
    padding: 0 0.15rem;
    background-color: #fff;
  }
  .goods-details {
    padding-top: 0.12rem;
    margin-bottom: 0.2rem;
    display: flex;
    background: #fff;

    .goods-images {
      width: 0.74rem;
      height: 0.74rem;
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      color: #1a1b1d;
      font-weight: 600;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.03rem;
    }
  }
  .split {
    margin: 0.08rem 0 0.1rem;
    width: 3.45rem;
    height: 0.01rem;
    background: #eeeeee;
  }
  .price-box {
    margin-bottom: 0.18rem;
    .price-head {
      justify-content: space-between;
      span {
        margin-left: 0.03rem;
        font-size: 0.14rem;
        color: #b2b1b7;
      }
      .apply-title {
        margin-bottom: 0;
      }
    }
    .price-num {
      margin: 0.04rem 0;
      font-size: 0.22rem;
      font-weight: 600;
      color: #ff6445;
    }
    .details {
      font-size: 0.12rem;
      color: #616568;
    }
  }

  :deep(.van-cell) {
    padding: 0.1rem;
    background: #fafafa;
    border-radius: 0.06rem;
  }
  :deep(.van-field__control) {
    // height: 1.36rem;
    overflow-y: scroll;
  }
  :deep(.van-field__word-limit) {
    color: #b2b1b7;
  }
  :deep(.multiple-tags) {
    margin-bottom: 0.08rem;
  }
  .upload-file {
    margin-top: 0.08rem;
    margin-bottom: 1rem;
  }
  :deep(.van-uploader__wrapper) {
    flex-shrink: 0;
  }
  :deep(.van-uploader__preview) {
    margin-right: 0.1rem;
  }
  :deep(.van-checkbox__label) {
    font-size: 0.14rem;
    color: #616568;
  }
  :deep(.van-popup) {
    border-radius: 0.06rem;
  }

  .class-late {
    margin-bottom: 0.08rem;

    :deep(.van-checkbox) {
      overflow: initial;
    }

    :deep(.van-checkbox__icon) {
      height: auto;
      line-height: initial;
    }
    :deep(.van-radio) {
      margin-bottom: 0.08rem;
      overflow: visible;
    }
    :deep(.van-radio__icon) {
      height: auto;
    }

    .tag-check-item {
      // margin-bottom: 0.08rem;

      span {
        padding: 0.04rem 0.08rem;
        font-size: 0.13rem;
        background: #f3f3f3;
        border-radius: 0.04rem;
        border: 0.01rem dashed #dddddd;
        display: block;
      }

      .tag-check-checked {
        color: #ff9b26;
        background-color: #fff6e9;
        border: 0.01rem dashed #ff9b26;
      }
    }
  }

  .refund-tip {
    padding: 0 0.26rem 0.23rem 0.26rem;

    .title {
      color: #616568;
      margin-bottom: 0.06rem;
    }

    p {
      color: #1a1b1d;
      margin-bottom: 0.06rem;
    }
  }
</style>
