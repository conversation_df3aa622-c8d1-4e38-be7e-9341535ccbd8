<template>
  <div class="editor-wrap">
    <quill-editor
      ref="editorRef"
      class="editor"
      :style="{ height: height }"
      v-model:content="content"
      toolbar="#quill-toolbar"
      contentType="html"
      :modules="editorModules"
      :options="options"
      @focus="focus"
      @blur="blur"
      @ready="ready"
      @update="update"
      @textChange="textChange"
      @selectionChange="selectionChange"
      @editorChange="editorChange"
    />
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { QuillEditor } from '@vueup/vue-quill'
  import editorModules from './modules'
  import './custom'

  const content = ref('')
  const toolbarShow = ref(false)
  const editorRef = ref('')

  const props = defineProps({
    modelValue: String,
    placeholder: {
      type: String,
      default: 'Compose an epic...',
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
    height: {
      type: String,
      default: null,
    },
  })

  const emit = defineEmits([
    'update:modelValue',
    'ready',
    'focus',
    'blur',
    'update',
    'textChange',
    'selectionChange',
    'editorChange',
  ])

  watch(
    () => props.modelValue,
    (newVal) => {
      if (editorRef.value && newVal !== content.value) {
        editorRef.value.setHTML(newVal)
      }
    },
    {
      immediate: true,
    },
  )

  const options = ref({
    placeholder: props.placeholder,
    readOnly: props.readOnly,
    theme: 'snow',
  })

  // 编辑器初始化后触发
  const ready = (quill) => {
    console.log('[编辑器](ready)：初始化完成')
    // TODO: 解决插件bug，第二次进入页面渲染富文本导致 ql-align不展示出来
    let elems = document.querySelectorAll('.ql-align')
    console.log(elems, 'elems')
    if (elems.length === 3) {
      elems[1].style.display = 'block'
      elems[0].remove()
    }

    customToolbar(quill)
    emit('ready', quill)
  }

  // 在编辑器获得焦点时触发
  const focus = (editor) => {
    toolbarShow.value = true
    emit('focus', editor)
    console.log('[编辑器](focus)：获得焦点')
  }

  // 在编辑器失去焦点时触发
  const blur = (editor) => {
    toolbarShow.value = false
    emit('blur', editor)
    console.log('[编辑器](blur)：失去焦点')
    console.log('[编辑器](当前HTML内容)：', content.value)
  }

  // 在编辑器内容更改时触发
  const update = (content) => {
    emit('update', content)
    console.log('[编辑器](update)：内容发生改变')
  }

  // 文本更改时触发
  const textChange = (args) => {
    emit('update:modelValue', content.value)
    emit('textChange', args)
    console.log('[编辑器](textChange)：文本发生改变')
  }

  // 在选择更改时触发
  const selectionChange = (args) => {
    emit('selectionChange', args)
    console.log('[编辑器](selectionChange)：选择更改内容')
  }

  // 在编辑器更改时触发，或者text-change、selection-change
  const editorChange = (args) => {
    emit('selectionChange', args)
    console.log('[编辑器](editorChange)：内容更改or选择更改')
  }

  function customToolbar(quill) {
    let elToolbar = editorRef.value.getToolbar()
    let elUndo = elToolbar.querySelector('.ql-undo')
    let elRedo = elToolbar.querySelector('.ql-redo')
    let elPackUp = elToolbar.querySelector('.ql-packUp')

    // 撤销
    elUndo.addEventListener('click', function () {
      quill.history.undo()
    })

    // 重做
    elRedo.addEventListener('click', function () {
      quill.history.redo()
    })

    // 收起键盘
    elPackUp.addEventListener('click', function () {
      quill.blur()
    })
  }
</script>

<style lang="scss" scoped>
  @import '@vueup/vue-quill/dist/vue-quill.snow.css';
  @import 'fixed-bottom';
</style>
