<template>
  <!-- 活动已结束 -->
  <div class="ActEnded">
    <div v-if="isLoginState" class="spree">
      <div class="act-time">活动已结束</div>
      <p class="text">{{ text }}</p>
      <div v-if="isApply" class="coach-wrap">
        <div class="coach-info">
          <div class="content">
            <div class="avatar">
              <img :src="getOssURL(actRes.finalResult.coachInfo.coachImage)" alt="" />
            </div>
            <div>
              <p class="username">{{ actRes.finalResult.coachInfo.coachName }}</p>
              <p class="tag">{{ actRes.finalResult.coachInfo.coachTitle }}</p>
            </div>
          </div>
          <div class="like">
            <span class="num">{{ actRes.finalResult.coachInfo.likeCount }}</span>
            <span>赞</span>
          </div>
        </div>
        <div class="ranking">
          No.<span>{{ actRes.finalResult.coachInfo.ranking }}</span>
        </div>
      </div>
      <div v-else class="user-gift" />

      <div class="operation">
        <SolidButton v-if="isApply" :hand="false" @click="contactPopupShow = true">
          联系客服，领取活动参与权益
        </SolidButton>
        <SolidButton
          v-else
          :hand="false"
          @click="$router.push('/act/a20230510jlzm/collect-gift-bag')"
        >
          联系客服，领取活动礼包
        </SolidButton>
      </div>
    </div>

    <div class="ranking-list">
      <div class="podium-wrap">
        <div class="podium-item second">
          <div class="user-info">
            <div class="avatar">
              <img
                v-default-avatar
                :src="
                  getOssURL(
                    rankingList[1].coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480',
                  )
                "
              />
            </div>
            <div class="username omit">{{ rankingList[1].coachName }}</div>
          </div>
          <div class="job-title">{{ rankingList[1].coachTitle }}</div>
          <div class="ranking">
            <!-- <p>
              <span>NO.</span><strong>{{ rankingList[1].ranking }}</strong>
            </p> -->
            <p class="praise-num">{{ rankingList[1].likeCount }}赞</p>
          </div>
        </div>
        <div class="podium-item first">
          <div class="user-info">
            <div class="avatar">
              <img
                v-default-avatar
                :src="
                  getOssURL(
                    rankingList[0].coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480',
                  )
                "
              />
            </div>
            <div class="username omit">{{ rankingList[0].coachName }}</div>
          </div>
          <div class="job-title">{{ rankingList[0].coachTitle }}</div>
          <div class="ranking">
            <!-- <p>
              <span>NO.</span><strong>{{ rankingList[0].ranking }}</strong>
            </p> -->
            <p class="praise-num">{{ rankingList[0].likeCount }}赞</p>
          </div>
        </div>
        <div class="podium-item thirdly">
          <div class="user-info">
            <div class="avatar">
              <img
                v-default-avatar
                :src="
                  getOssURL(
                    rankingList[2].coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480',
                  )
                "
              />
            </div>
            <div class="username omit">{{ rankingList[2].coachName }}</div>
          </div>
          <div class="job-title">{{ rankingList[2].coachTitle }}</div>
          <div class="ranking">
            <!-- <p>
              <span>NO.</span><strong>{{ rankingList[2].ranking }}</strong>
            </p> -->
            <p class="praise-num">{{ rankingList[2].likeCount }}赞</p>
          </div>
        </div>
      </div>
      <div class="middle-box">
        <div class="other-ranking">
          <div v-for="item in [3, 4]" :key="item" class="ranking-item">
            <template v-if="rankingList[item]">
              <div class="sort">{{ rankingList[item].ranking }}</div>
              <div class="coach-info">
                <div class="avatar">
                  <img
                    v-default-avatar
                    :src="
                      getOssURL(
                        rankingList[item].coachImage +
                          '?x-oss-process=image/resize,m_fill,h_480,w_480',
                      )
                    "
                  />
                </div>
                <div>
                  <p class="username">{{ rankingList[item].coachName }}</p>
                  <p class="job-title">{{ rankingList[item].coachTitle }}</p>
                </div>
              </div>
              <div class="praise-num">{{ rankingList[item].likeCount }}赞</div>
            </template>
          </div>
        </div>
        <div class="gift">
          <p>1、指定教练！每月30+场线下活动！</p>
          <p>2、打造明星教练！高校活动10万+曝光！</p>
          <p>3、霸屏全网！价值8980元营销推广服务！</p>
          <p>4、享受优先推荐学员服务！</p>
          <p>5、拓展企业端教学机会！</p>
        </div>
      </div>
    </div>

    <div class="participant">
      <div class="participant-list">
        <van-swipe
          class="swipe"
          lazy-render
          :autoplay="4000"
          indicator-color="#FF9B26FF"
          :show-indicators="false"
        >
          <van-swipe-item v-for="(arr, index) in participant" :key="index">
            <div class="swipe-container">
              <div v-for="item in arr" :key="item" class="user">
                <img
                  v-default-avatar
                  class="avatar"
                  :src="
                    getOssURL(item.coachImage + '?x-oss-process=image/resize,m_fill,h_480,w_480')
                  "
                />
                <span class="omit">{{ item.coachName }}</span>
              </div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="participant-gift">
        <p>1、学员推荐！</p>
        <p>2、100万曝光，价值2980元网络营销服务！</p>
      </div>
    </div>

    <ContactPopup v-model="contactPopupShow" />
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import ContactPopup from './ContactPopup'
  import { isLogin } from '@/common'
  import SolidButton from './SolidButton'

  import { useParent } from '@vant/use'
  import { getOssURL } from '@/common'

  const { parent } = useParent('321ACT')

  const contactPopupShow = ref(false)
  const isLoginState = ref(isLogin())

  const isApply = computed(() => {
    return parent.actInfo.value.finalResult && parent.actInfo.value.btnType === 3
  })

  const actRes = computed(() => {
    return parent.actInfo.value
  })

  const text = computed(() => {
    return isApply.value
      ? '活动已结束，感谢自己的努力，明年继续加油~'
      : '活动已结束，感谢您的参与，送您一个活动大礼包'
  })

  const rankingList = computed(() => {
    return parent.actInfo.value.rankingList
  })

  const slicer = (data, len) => {
    if (!Array.isArray(data)) return []

    const arr = []
    for (let i = 0; i < data.length; i += len) {
      arr.push(data.slice(i, i + len))
    }
    return arr
  }

  const participant = computed(() => {
    return slicer(parent.actInfo.value.finalCoachList, 30)
  })
</script>

<style lang="scss" scoped>
  .ActEnded {
  }

  .spree {
    width: 3.5rem;
    min-height: 3.13rem;
    margin: 0 auto;
    border-radius: 0.08rem;
    background: url('../images/bg-1.png') no-repeat;
    background-size: 100% 100%;
    position: relative;

    .act-time {
      position: absolute;
      left: 0;
      top: 0;
      width: 1.41rem;
      height: 0.34rem;
      font-size: 0.14rem;
      color: #ffd600;
      text-align: center;
      line-height: 0.34rem;
      font-family: YouSheBiaoTiHei;
    }

    .text {
      font-size: 0.16rem;
      text-align: center;
      padding-top: 0.46rem;
      color: hsla(0deg, 0%, 100%, 0.5);
      font-family: YouSheBiaoTiHei;
    }

    .user-gift {
      width: 2.84rem;
      height: 1.72rem;
      background: url('../images/welfare.png') no-repeat;
      background-size: 100% 100%;
      margin: 0.12rem auto 0 auto;
    }

    .operation {
      width: 3.22rem;
      margin: 0.25rem auto 0 auto;
      padding-bottom: 0.15rem;
    }

    .coach-info {
      width: 3.23rem;
      height: 1.05rem;
      margin: 0 auto;
      background: url('../images/coach-bg.png') no-repeat;
      background-size: 100% 100%;
      position: relative;

      .content {
        position: absolute;
        left: 0.26rem;
        top: 0.27rem;
        display: flex;
      }

      .avatar {
        width: 0.52rem;
        height: 0.52rem;
        border-radius: 50%;
        margin-right: 0.08rem;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .username {
        font-size: 0.16rem;
        color: #1f1f1f;
        font-weight: bold;
      }

      .tag {
        color: #655d58;
        margin-top: 0.04rem;
      }

      .like {
        position: absolute;
        top: 0.33rem;
        right: 0.18rem;
        color: #ffe8e3;
        display: flex;
        justify-content: center;
        align-items: center;

        .num {
          font-size: 0.24rem;
          font-weight: 600;
          color: #ffffff;
          margin-right: 0.02rem;
        }

        span {
          line-height: 0.33rem;
        }
      }
    }

    .ranking {
      width: 100%;
      height: 0.4rem;
      line-height: 0.4rem;
      text-align: center;
      color: #313131;
      font-weight: 600;
      background: url('../images/sort-bg.png') no-repeat;
      background-size: 100% 100%;
      margin-top: 0.07rem;

      span {
        font-size: 0.26rem;
      }
    }
  }

  .ranking-list {
    width: 3.75rem;
    padding-bottom: 0.15rem;
    border-radius: 0.1rem;
    overflow: hidden;
    .podium-wrap {
      width: 100%;
      height: 3.14rem;
      background: url('../images/rank-head-bg.png') no-repeat;
      background-size: 100% 100%;
      position: relative;

      .podium-item {
        width: 1.03rem;
        position: absolute;

        .user-info {
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .avatar {
            width: 0.6rem;
            height: 0.6rem;
            border-radius: 50%;
            box-shadow: 0 0 0.16rem 0 rgba(254, 194, 2, 0.29);

            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
            }
          }

          .username {
            max-width: 0.98rem;
            min-width: 0.5rem;
            height: 0.2rem;

            text-align: center;
            font-weight: bold;

            margin-top: 0.02rem;
            color: #1a1b1d;
          }
        }

        .job-title {
          font-size: 0.12rem;
          text-align: center;
          color: #1a1b1d;
          margin-top: 1px;
        }
      }

      .second {
        top: 1.1rem;
        left: 0.19rem;
        .user-info {
          &:before {
            content: '';
            width: 0.26rem;
            height: 0.26rem;
            position: absolute;
            left: 0.65rem;
            top: -0.1rem;
            background: url('../images/rank-2.png') no-repeat;
            background-size: 100% 100%;
            z-index: 2;
          }

          .username {
            position: relative;
            z-index: 4;
          }
        }
        .ranking {
          margin-top: 0.21rem;
          color: #ff6445;

          span {
            font-size: 0.12rem;
            font-weight: bold;
          }

          p {
            color: rgba(0, 0, 0, 0.4);
            text-align: center;
            line-height: 0.17rem;
          }

          strong {
            font-size: 0.18rem;
          }
        }
      }

      .first {
        width: 1.12rem;
        top: 0.85rem;
        left: 1.3rem;

        .user-info {
          .avatar {
            width: 0.68rem;
            height: 0.68rem;
            border: 1px solid #7719be;
            position: relative;

            img {
              position: relative;
              z-index: 3;
            }

            &:before {
              content: '';
              width: 0.26rem;
              height: 0.26rem;
              position: absolute;
              left: 0.5rem;
              top: -0.08rem;
              background: url('../images/rank-1.png') no-repeat;
              background-size: 100% 100%;
              z-index: 2;
            }
          }

          .username {
            position: relative;
            z-index: 4;
          }
        }

        .ranking {
          margin-top: 0.21rem;
          color: #ff6445;

          span {
            font-size: 0.12rem;
            font-weight: bold;
          }

          p {
            color: rgba(0, 0, 0, 0.4);
            text-align: center;
            line-height: 0.17rem;
          }

          strong {
            font-size: 0.18rem;
          }
        }
      }

      .thirdly {
        top: 1.25rem;
        right: 0.2rem;
        .user-info {
          &:before {
            content: '';
            width: 0.26rem;
            height: 0.26rem;
            position: absolute;
            left: 0.65rem;
            top: -0.1rem;
            background: url('../images/rank-3.png') no-repeat;
            background-size: 100% 100%;
            z-index: 2;
          }
        }

        .ranking {
          margin-top: 0.21rem;
          color: #ff6445;

          span {
            font-size: 0.12rem;
            font-weight: bold;
          }

          p {
            color: rgba(0, 0, 0, 0.4);
            text-align: center;
            line-height: 0.17rem;
          }

          strong {
            font-size: 0.18rem;
          }
        }
      }
    }

    .gift {
      width: 3.2rem;
      height: 1.92rem;
      margin: 0.1rem auto 0 auto;
      background: #ffcd00;
      border-radius: 0.04rem;
      border: 0.02rem solid #fff0a5;
      padding: 0.71rem 0.12rem 0.12rem 0.12rem;
      position: relative;
      color: #1f1f1f;

      &:before {
        content: '';
        width: 3.17rem;
        height: 0.57rem;
        position: absolute;
        top: 0.08rem;
        left: -0.07rem;
        background: url('../images/gift-streamer.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .middle-box {
    width: 3.48rem;
    margin: 0 auto;
    background: #fffbee;
    border-radius: 0 0 0.07rem 0.07rem;

    border-bottom: 1px solid #7719be;
    border-left: 1px solid #7719be;
    border-right: 1px solid #7719be;
  }
  .other-ranking {
    width: 3.5rem;
    margin: 0 auto;
    .ranking-item + .ranking-item {
      border-top: 1px solid #fff5cb;
    }

    .ranking-item {
      display: flex;
      align-items: center;
      margin: 0 0.06rem;
      height: 0.6rem;

      .sort {
        width: 0.44rem;
        text-align: center;
        font-weight: 600;
        color: #979797;
      }

      .coach-info {
        flex: 1;
        display: flex;

        .avatar {
          width: 0.44rem;
          height: 0.44rem;
          margin-right: 0.1rem;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }

        .username {
          font-size: 0.15rem;
          color: #1f1f1f;
        }

        .job-title {
          font-size: 0.12rem;
          color: #616568;
        }
      }

      .praise-num {
        width: 0.8rem;
        color: rgba(55, 107, 254, 1);
        padding-right: 0.18rem;
        text-align: right;
      }
    }
  }

  .participant {
    width: 3.5rem;
    margin: 0.24rem auto 0 auto;
    border-radius: 0.1rem;
    padding-bottom: 0.15rem;
    background: #fff;
    position: relative;
    padding-bottom: 0.74rem;

    .participant-list {
      background: #fff;
      border-radius: 0.1rem;
      position: relative;
      z-index: 2;
    }

    .participant-gift {
      position: absolute;
      top: 1.83rem;
      left: -0.01rem;
      z-index: 1;
      width: 3.52rem;
      height: 2.52rem;
      margin: 0 auto 0 auto;
      background: #5a7cff;
      border-radius: 0.04rem;
      border: 1px solid #7719be;
      padding: 1.94rem 0.12rem 0.12rem 0.12rem;
      color: #ffffff;

      &:before {
        content: '';
        width: 3.14rem;
        height: 0.3rem;
        position: absolute;
        left: -0.01rem;
        top: 1.52rem;
        background: url('../images/participant-gift-streamer.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .swipe {
    height: 3.22rem;

    .swipe-container {
      //display: flex;
      //flex-flow: wrap;
      //justify-content: space-between;

      .user {
        width: 16.6%;
        float: left;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-top: 0.12rem;

        .avatar {
          width: 0.32rem;
          height: 0.32rem;
          border-radius: 50%;
          object-fit: cover;
        }

        span {
          font-size: 0.12rem;
          color: #7b3a08;
          max-width: 0.6rem;
        }
      }
    }
  }
</style>
