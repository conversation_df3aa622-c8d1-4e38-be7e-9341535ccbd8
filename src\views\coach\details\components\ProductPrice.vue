<template>
  <div class="product-price" :style="{ color }">
    <div class="product-price__symbol">{{ symbol }}</div>
    <div class="product-price__number">{{ price }} </div>
    <div class="product-price__unit">/{{ unit }}</div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue'

  export default defineComponent({
    props: {
      symbol: { type: String, default: '¥' },
      price: { type: [String, Number], default: 0 },
      unit: { type: String, default: '课时' },
      color: { type: String, default: '#FF6445' },
    },

    setup() {},
  })
</script>

<style scoped lang="scss">
  @import '~@/styles/font-package/regular/index';

  .product-price {
    display: inline-block;
    letter-spacing: 1px;
  }

  .product-price__symbol {
    display: inline;
    font-size: 0.12rem;
    font-family: 'regular', serif;
    font-weight: bold;
    margin-right: 0.02rem;
  }

  .product-price__number {
    display: inline;
    font-size: 0.18rem;
    font-family: 'regular', serif;
    font-weight: bold;
  }

  .product-price__unit {
    display: inline;
    font-size: 0.12rem;
    font-family: monospace;
    font-weight: bold;
  }
</style>
