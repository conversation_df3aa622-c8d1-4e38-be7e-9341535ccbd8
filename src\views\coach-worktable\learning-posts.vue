<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content page-bg-white">
        <main class="content-wrap">
          <!-- 发布文章操作面板 -->
          <div class="operation-panel">
            <div
              class="panel feedback"
              @click="$router.push({ name: 'coachWorktablePublishArticle' })"
            >
              <img
                class="icon"
                src="../../assets/images/coach-worktable/icon-publish-article.png"
                alt=""
              />
              发布文章
            </div>
            <div
              class="panel feedback"
              @click="$router.push({ name: 'coachWorktablePublishVideo' })"
            >
              <img class="icon" src="../../assets/images/icon/icon-upload-video.png" alt="" />
              发布视频
            </div>
          </div>

          <div class="tabs">
            <div class="tabs-navs">
              <div
                v-for="item in tabs"
                :key="item.name"
                class="tabs-nav-item"
                :class="{ 'nav-active': tabsActiveName === item.name }"
                @click="toggleTabs(item.name)"
              >
                {{ item.title }}
              </div>
            </div>
            <div class="tabs-content">
              <div v-show="tabsActiveName === '1'" class="tab-panel">
                <div class="posts-list">
                  <van-list
                    ref="articleListRef"
                    v-model:loading="article.loading"
                    :finished="article.finished"
                    @load="onArticlePostLoad"
                  >
                    <article-post
                      v-for="item in article.list"
                      :key="item.id"
                      :post="item"
                      :class="{ 'post-disable': !item.publish }"
                      @click="toArticlePostPage(item.id)"
                    >
                      <template v-if="!item.publish" #state>
                        <post-state></post-state>
                      </template>
                    </article-post>
                  </van-list>
                </div>
                <div v-if="article.emptyShow" class="empty">
                  <img
                    class="empty-icon"
                    src="../../assets/images/empty.png"
                    alt="你还未发布过内容哦～"
                  />
                  <p class="empty-desc">你还未发布过内容哦～</p>
                </div>
              </div>
              <div v-show="tabsActiveName === '2'" class="tab-panel">
                <div class="posts-list">
                  <van-list
                    ref="videoListRef"
                    v-model:loading="video.loading"
                    :finished="video.finished"
                    @load="onVideoPostLoad"
                  >
                    <video-post
                      v-for="item in video.list"
                      :key="item.id"
                      :post="item"
                      :class="{ 'post-disable': !item.publish }"
                      @click="toVideoPostPage(item.id)"
                    >
                      <template v-if="!item.publish" #state>
                        <post-state></post-state>
                      </template>
                    </video-post>
                  </van-list>
                </div>
                <div v-if="video.emptyShow" class="empty">
                  <img
                    class="empty-icon"
                    src="../../assets/images/empty.png"
                    alt="你还未发布过内容哦～"
                  />
                  <p class="empty-desc">你还未发布过内容哦～</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'coachWorktableLearningPosts' }
</script>

<script setup>
  import { ref } from 'vue'
  import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
  import ArticlePost from '@/components/post/article-post'
  import VideoPost from '@/components/post/video-post'
  import PostState from '@/components/post/post-state'
  import { getUserArticleList, getUserVideosList } from '@/api/generic-server'
  import useKeepAliveStore from '@/store/keepAlive'

  const route = useRoute()
  const router = useRouter()

  const tabsActiveName = ref(route.query.tabIndex ? String(route.query.tabIndex) : '1')

  const tabs = ref([
    { title: '教学文章', name: '1' },
    { title: '教学视频', name: '2' },
  ])

  const videoListRef = ref('')
  const articleListRef = ref('')

  let article = ref({
    loading: false,
    finished: false,
    emptyShow: false,
    list: [],
    pageNum: 0,
    pageSize: 10,
  })

  let video = ref({
    loading: false,
    finished: false,
    emptyShow: false,
    list: [],
    pageNum: 0,
    pageSize: 10,
  })

  const toggleTabs = (name) => {
    tabsActiveName.value = name

    if (name === '1') {
      articleListRef.value.check()
    }

    if (name === '2') {
      videoListRef.value.check()
    }
  }

  // 加载文章列表
  const onArticlePostLoad = () => {
    article.value.pageNum += 1
    let params = {
      pageNum: article.value.pageNum,
      pageSize: article.value.pageSize,
    }

    getUserArticleList(params).then((res) => {
      let { data } = res
      article.value.list = article.value.list.concat(data.records)
      // 加载状态结束
      article.value.loading = false
      article.value.emptyShow = !(article.value.list.length > 0)

      // 数据全部加载完成
      if (data.records.length === 0 || data.records.length < article.value.pageSize) {
        article.value.finished = true
      }
    })
  }

  // 获取视频文章列表
  const onVideoPostLoad = () => {
    video.value.pageNum += 1
    let params = {
      pageNum: video.value.pageNum,
      pageSize: video.value.pageSize,
    }

    getUserVideosList(params).then((res) => {
      let { data } = res
      video.value.list = video.value.list.concat(data.records)
      // 加载状态结束
      video.value.loading = false
      video.value.emptyShow = !(video.value.list.length > 0)

      // 数据全部加载完成
      if (data.records.length === 0 || data.records.length < video.value.pageSize) {
        video.value.finished = true
      }
    })
  }

  const toArticlePostPage = (articleId) => {
    router.push({
      name: 'newsDetails',
      params: {
        articleId: articleId,
      },
    })
  }

  const toVideoPostPage = (id) => {
    router.push({
      name: 'videoDetails',
      params: {
        id: id,
      },
    })
  }

  const keepAliveStore = useKeepAliveStore()
  onBeforeRouteLeave((to) => {
    let pages = ['newsDetails', 'videoDetails']
    if (!pages.includes(to.name)) {
      // 卸载缓存
      keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
    }
  })
</script>

<style lang="scss" scoped>
  .content-wrap {
    .operation-panel {
      display: flex;
      padding: 0.15rem 0.16rem;
      background-color: #fff;

      .panel {
        width: 1.66rem;
        height: 0.8rem;
        background: #f8f8f8;
        border-radius: 0.08rem;
        border: 1px dashed #ececec;
        display: flex;
        justify-content: center;
        align-items: center;
        user-select: none;

        &:first-child {
          margin-right: 0.1rem;
        }

        .icon {
          width: 0.26rem;
          height: 0.26rem;
          margin-right: 0.06rem;
        }
      }
    }

    .posts-list {
      background-color: #fff;
    }

    .post-disable {
      background: #f7f7f7;
    }

    .tabs {
      .tabs-navs {
        display: flex;
        color: #b2b1b7;
        padding: 0.1rem 0.12rem;
        background-color: #fff;
      }

      .tabs-nav-item {
        padding: 0 0.14rem;
        line-height: 0.15rem;
        user-select: none;

        &:first-child {
          padding-left: 0;
        }

        &:not(:last-child) {
          border-right: 1px solid rgba(178, 177, 183, 0.4);
        }
      }

      .nav-active {
        color: #1a1b1d;
      }
    }
  }

  .empty {
    background-color: #fff;
    text-align: center;
    padding: 1.38rem 0;

    .empty-icon {
      width: 0.98rem;
      height: 1rem;
    }

    .empty-desc {
      font-size: 0.12rem;
      color: #b2b1b7;
      margin-top: 0.1rem;
      user-select: none;
    }

    .buttons {
      margin-top: 0.7rem;
      .btn-wrap {
        margin-bottom: 0.2rem;
      }
    }

    .empty-button {
      width: 1.8rem;
      height: 0.4rem;
      font-size: 0.16rem;
      color: #fff;
      font-weight: 600;
      background: #ff9b26;
      box-shadow: 0 2px 4px 1px rgba(245, 176, 76, 0.1);
      border-radius: 0.24rem;
    }
  }
</style>
