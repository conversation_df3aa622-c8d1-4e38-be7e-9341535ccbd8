<template>
  <div class="main-box">
    <div class="banner-img-box" @click="toAddCoach">
      <img class="banner-img" src="@/assets/images/coach/coach-intro/intro-banner.png" alt="" />
    </div>
    <div class="content-box">
      <img class="step-img" src="@/assets/images/coach/coach-intro/step-1.png" alt="" />
      <img class="step-img" src="@/assets/images/coach/coach-intro/step-2.png" alt="" />
      <img class="step-img" src="@/assets/images/coach/coach-intro/step-3.png" alt="" />
    </div>
    <div class="more-box">
      <p class="more-question" @click="connectService">更多问题>></p>
    </div>
    <div class="content-box">
      <img class="step-img" src="@/assets/images/coach/coach-intro/wx-bottom.png" alt="" />
    </div>
    <div class="footer-box">
      <div class="share-box" @click="openPosterPopup">
        <div class="icon icon-share"></div>
        <div class="share-text">邀请教练</div>
      </div>
      <button class="invite-btn" @click="$router.push({ name: 'coachApplyForm' })">立即入驻</button>
    </div>
    <poster-popup v-model:show="posterPopupShow" :showBottom="false" :showSelectIndex="1" />
  </div>
</template>

<script>
  export default { name: 'coachIntro' }
</script>

<script setup>
  import { ref } from 'vue'
  import { isLogin, toLogin } from '@/common'
  import PosterPopup from '../user-info/poster-popup.vue'
  import gm from '@/components/gm-popup'

  const posterPopupShow = ref(false)
  const isLoginState = ref(false)
  isLoginState.value = isLogin()

  const openPosterPopup = () => {
    if (isLoginState.value) {
      posterPopupShow.value = true
    } else {
      toLogin()
    }
  }

  // 联系客服
  const connectService = () => {
    gm.open({
      title: '联系客服',
      desc: '微信扫码添加客服企微，帮你解决更多问题',
    })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';
  @import '~@/styles/block-container.scss';
  @include Icon('share', 0.2rem, 0.2rem);
  .main-box {
    background: #fff;
    position: relative;
  }
  .banner-img-box {
    cursor: pointer;
    .banner-img {
      display: block;
      width: 100%;
    }
  }

  .content-box {
    .step-img {
      width: 100%;
    }
  }
  .more-box {
    margin: 0.3rem 0;
    .more-question {
      font-size: 0.14rem;
      color: #ff9b26;
      text-align: center;
      margin-top: 18px;
      cursor: pointer;
    }
  }
  .footer-box {
    position: fixed;
    bottom: 0;
    left: var(--window-left);
    right: var(--window-right);
    height: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.06);
    background: #fff;
    .share-box {
      text-align: center;
      .share-text {
        font-size: 0.12rem;
      }
    }
    .invite-btn {
      width: 2.82rem;
      height: 0.45rem;
      background: #ff8c00;
      opacity: 0.8;
      font-size: 0.16rem;
      font-weight: 600;
      color: #feffff;
      border-radius: 0.25rem;
    }
  }
</style>
