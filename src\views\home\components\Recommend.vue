<template>
  <div class="recommend">
    <div class="recommend-head">
      <span class="title">推荐教练</span>
      <div @click="$router.push('/search-result?sort=2')">
        <span class="more">更多</span>
        <van-icon class="icon" size="0.12rem" name="arrow" />
      </div>
    </div>
    <div class="waterfall">
      <ijl-skeleton :loading="loading" :count="4" animated>
        <template #skeleton>
          <div style="width: 1.67rem">
            <ijl-skeleton-image height="1.67rem" />
            <ijl-skeleton-row />
            <ijl-skeleton-row />
            <ijl-skeleton-row width="40%" />
          </div>
        </template>
        <template #content>
          <div class="coach-list">
            <coach
              v-for="(item, index) in coachRecommendList"
              :key="item.id"
              :coach-data="item"
              :is-show-hot="index < 3"
              @trainerDetails="onToCoachDetails"
            />
          </div>
        </template>
      </ijl-skeleton>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import Coach from '@/views/common/components/coach-info'
  import { reqRecommendCoachList } from '@/api/coach-server'

  const router = useRouter()
  const loading = ref(true)
  const coachRecommendList = ref([])

  const onToCoachDetails = (row) => {
    router.push('/coach/details/' + row.id)
  }

  const getRecommendList = () => {
    let params = { pageNum: 1, pageSize: 10 }
    reqRecommendCoachList(params)
      .then((res) => {
        const { data } = res
        coachRecommendList.value = data.records
        loading.value = false
      })
      .catch((error) => {
        console.log(error, 'error')
      })
  }

  getRecommendList()
</script>

<style lang="scss" scoped>
  .recommend {
    background-color: #fff;
    .recommend-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.08rem 0.12rem;
      margin-top: 0.08rem;
      user-select: none;

      .title {
        font-size: 0.16rem;
        font-weight: bold;
        color: #1a1b1d;
      }

      .more {
        font-size: 0.12rem;
        color: #979797;
      }

      .icon {
        color: #979797;
      }
    }

    .waterfall {
      width: 100%;
      padding: 0.08rem 0.13rem;

      :deep(.ijl-skeleton-item) {
        flex: initial;
      }
    }

    .coach-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
</style>
