<template>
  <div class="page modify-password">
    <div class="notify">
      <p>修改密码后，可使用新密码登录爱教练用户端和教练工作台</p>
    </div>

    <div class="form">
      <div class="form-item">
        <label class="form-item-label">用户名</label>
        <div class="form-item-content">
          <input readonly class="form-item-input" v-model="user.userName" />
        </div>
      </div>
      <div class="form-item">
        <label class="form-item-label">旧密码</label>
        <div class="form-item-content">
          <input
            class="form-item-input"
            type="password"
            v-model="form.oldPassword"
            placeholder="请填写旧密码"
          />
        </div>
        <div v-if="formError['oldPassword']" class="form-error-message">
          <i class="icon-error-tip"></i>
          <div class="message">{{ formError['oldPassword'] }}</div>
        </div>
      </div>
      <div class="form-item">
        <label class="form-item-label">新密码</label>
        <div class="form-item-content">
          <input
            class="form-item-input"
            type="password"
            v-model="form.newPassword"
            placeholder="请输入新的密码"
          />
        </div>
        <div v-if="formError['newPassword']" class="form-error-message">
          <i class="icon-error-tip"></i>
          <div class="message">{{ formError['newPassword'] }}</div>
        </div>
      </div>
      <div class="form-item">
        <label class="form-item-label">确认密码</label>
        <div class="form-item-content">
          <input
            class="form-item-input"
            type="password"
            v-model="form.confirmPassword"
            placeholder="请再次确认密码"
          />
        </div>
        <div v-if="formError['confirmPassword']" class="form-error-message">
          <i class="icon-error-tip"></i>
          <div class="message">{{ formError['confirmPassword'] }}</div>
        </div>
      </div>
      <div class="tip-text">密码必须为6-12位英文字母或数字</div>

      <div class="form-button-wrap">
        <button class="i-button form-submit-button" @click="onConfirm">确定</button>
      </div>
    </div>

    <div class="help">
      <a href="/account/password-reset">忘记密码？</a>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { localProxyStorage } from '@/utils/storage'
  import Schema from 'async-validator'
  import { checkEmpty, checkPassword } from '@/utils/validate'
  import { Toast } from 'vant'
  import { updatePasswordByOldPassword } from '@/api/user-server'
  import { signOut } from '@/common'

  const user = reactive(localProxyStorage.user)

  const form = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  const formError = ref({})

  const formValidator = new Schema({
    oldPassword: { message: '请输入旧密码', validator: checkEmpty },
    newPassword: { message: '请输入新密码', validator: checkPassword },
    confirmPassword: {
      message: '',
      validator: function (rule, value) {
        if (value !== form.newPassword) {
          this.message = '两次输入的密码不一致'
          return false
        }
        return true
      },
    },
  })

  const onConfirm = () => {
    formValidator
      .validate(form)
      .then(() => {
        formError.value = {}

        Toast.loading({
          duration: 0,
          forbidClick: true,
          message: '加载中...',
          loadingType: 'spinner',
        })

        let params = {
          oldPassword: form.oldPassword,
          newPassword: form.newPassword,
        }

        updatePasswordByOldPassword(params).then((res) => {
          const { data } = res
          if (data) {
            Toast.clear()
            Toast('密码修改成功，请重新登录')
            signOut()
          }
        })
      })
      .catch(({ errors }) => {
        formError.value = {}
        errors.forEach((item) => {
          formError.value[item.field] = item.message
        })
      })
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/mixins/mixins.scss';

  @include Icon('error-tip', 0.13rem, 0.13rem) {
    margin-top: 0.02rem;
  }

  .modify-password {
    .notify {
      padding: 0.12rem 0.15rem;
      color: #616568;
    }

    .form {
      padding: 0 0.15rem;

      .form-item {
        position: relative;
        //padding: 0.18rem 0 0.16rem 0;
      }

      .form-item-label {
        width: 0.8rem;
        line-height: 0.54rem;
        position: absolute;
        left: 0.05rem;
        color: #453938;
      }

      .form-item-content {
        line-height: 0.54rem;
      }

      .form-item-input {
        width: 100%;
        padding: 0.18rem 0 0.18rem 0.8rem;
        outline: none;
        border: none;
        border-bottom: 1px solid #eeeeee;

        &:focus {
          border-bottom-color: #f5b04c;
        }

        &::-webkit-input-placeholder {
          color: #b2b1b7;
        }
      }

      .form-error-message {
        font-size: 0.12rem;
        color: #ff6445;
        display: flex;
        margin-top: 0.02rem;

        .message {
          margin-left: 0.04rem;
        }
      }

      .tip-text {
        margin-top: 0.08rem;
        color: #979797;
      }

      .form-button-wrap {
        margin-top: 0.35rem;
      }

      .form-submit-button {
        width: 100%;
        height: 0.4rem;
        background: #ff9b26;
        box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
        border-radius: 0.22rem;
        font-size: 0.16rem;
        color: #ffffff;
      }
    }

    .help {
      margin-top: 0.2rem;
      text-align: center;
      a {
        font-size: 0.12rem;
        color: #2c71a5;
        user-select: none;
      }
    }
  }
</style>
