<template>
  <div
    ref="container"
    class="slider-verify"
    :style="{
      width: width,
    }"
  >
    <div ref="bar" class="slider-bar"></div>
    <div class="slider-text">{{ sliderText }}</div>
    <div ref="button" class="slider-button" />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  let props = defineProps({
    width: {
      type: [String, Number],
      default: '100%',
    },
    activeText: {
      type: String,
      default: '验证通过',
    },
    inActiveText: {
      type: String,
      default: '按住滑块，拖动到最右边',
    },
  })

  // # 是否验证成功
  const isVerify = ref(false)

  // # 最外层容器
  let container = ref(null)

  // # 拖动的按钮
  let button = ref(null)

  // # bar
  let bar = ref(null)

  const sliderText = computed(() => {
    return isVerify.value ? props.activeText : props.inActiveText
  })

  onMounted(() => {
    // 可滑动的最大偏移量
    let maxOffset = container.value.clientWidth - button.value.clientWidth
    let left = 0

    const setElementLeft = (left) => {
      bar.value.style.width = left + 'px'
      button.value.style.left = left + 'px'
    }

    const removeEvents = () => {
      document.removeEventListener('mousemove', handleMove)
      document.removeEventListener('touchmove', handleMove) // 移动端
      document.removeEventListener('mouseup', handleMouseup)
      document.removeEventListener('touchend', handleMouseup) // 移动端
    }

    // 验证成功
    const verifySuccess = () => {
      isVerify.value = true
      removeEvents()
      button.value.removeEventListener('mousedown', handleDown)
      button.value.removeEventListener('touchstart', handleDown) // 移动端
    }

    // 滑动中
    const handleMove = (e) => {
      const currentX = e.pageX || e.touches[0].pageX
      const offsetLeft = container.value.getBoundingClientRect().left
      left = currentX - offsetLeft - button.value.clientWidth / 2

      if (left > maxOffset) {
        left = maxOffset
        verifySuccess()
      } else if (left < 0) {
        left = 0
      }

      setElementLeft(left)
    }

    // 松开 解除事件
    const handleMouseup = () => {
      if (left < maxOffset) {
        setElementLeft(0)
      }
      removeEvents()
    }

    // 按下绑定事件
    const handleDown = () => {
      document.addEventListener('mousemove', handleMove)
      document.addEventListener('touchmove', handleMove) // 移动端
      document.addEventListener('mouseup', handleMouseup)
      document.addEventListener('touchend', handleMouseup) // 移动端
    }

    const initDrag = () => {
      button.value.addEventListener('mousedown', handleDown)
      button.value.addEventListener('touchstart', handleDown) // 移动端
    }

    initDrag()
  })
</script>

<style lang="scss" scoped>
  .slider-verify {
    height: 0.5rem;
    border-radius: 0.04rem;
    background: #e7e7e7;
    position: relative;
  }

  .slider-text {
    position: absolute;
    width: 100%;
    z-index: 2;
    text-align: center;
    font-size: 0.14rem;
    height: 100%;
    line-height: 0.5rem;
    color: #979797;
    user-select: none;
  }

  .slider-bar {
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    text-align: center;
    font-size: 0.14rem;
    background-color: #8bd143;
  }

  .slider-button {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 3;
    width: 0.56rem;
    height: 0.5rem;
    border: 1px solid #cbcad2;
    border-radius: 0.04rem;
    background-color: #fff;
    cursor: move;
  }
</style>
