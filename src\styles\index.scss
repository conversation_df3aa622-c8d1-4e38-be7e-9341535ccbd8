@import "var";
@import "pseudo-class";
@import "vant";
@import "common";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 100px;
  font-size: calc(100vw / 3.75);
  font-family: <PERSON>rebu<PERSON><PERSON>, <PERSON><PERSON><PERSON>, 'Microsoft YaHei', sans-serif;
}

body {
  font-size: .14rem;
  line-height: 1.5;
  color: #444;
  //background-color: #f8f7f7;
  background-color: #efeff4;
  overflow-y: auto;
  overflow-x: hidden;
  /* IOS禁止微信调整字体大小 */
  -webkit-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  //适配 iPhone 底部小黑条安全距离
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

button {
  outline: none;
  border: 0;
  background: #fff;
}

a {
  color: inherit;
}

#app {
  width: 100%;
  background-color: #f8f7f7;
  margin: 0 auto;
  position: relative;
}

.min-height-100 {
  min-height: 100vh;
}

.page {
  background-color: #fff;
  min-height: 100vh;
}

.page-content {
  min-height: calc(100vh - var(--nav-bar-height));
}

.page-bg-white {
  background-color: #fff;
}

.omit {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 通过缩放达到10像素字体
.f10 {
  font-size: 0.12rem;
  transform: scale(0.84);
}

.f11 {
  font-size: 0.12rem;
  transform: scale(0.92);
}

.fixed-page-footer {
  width: 3.75rem;
  position: fixed;
  bottom: 0;
  text-align: center;
  background-color: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.clearfix {
  zoom: 1;

  &:after,

  &:before {
    content: "";
    display: table
  }

  &:after {
    clear: both
  }
}

//@media only screen and (min-width:640px) {
//  html {
//    font-size: calc(640px / 3.75) !important;
//  }
//}

@media only screen and (min-device-width: 480px) {
  /* 仅在设备宽度最小为480像素且竖屏模式下应用的样式 */
  #app {
    max-width: 480px;
  }

  html {
    font-size: calc(480px / 3.75) !important;
  }
}


@media only screen and (min-device-width: 480px) and (orientation: landscape) {
  /* 仅在设备宽度最小为480像素且竖屏模式下应用的样式 */
  #app {
    max-width: 480px;
  }

  html {
    font-size: calc(480px / 3.75) !important;
  }
}

@media screen and (max-width: 360px) {
  html {
    font-size: calc(360px / 3.75) !important; /* 避免字体过大 */
  }
}

