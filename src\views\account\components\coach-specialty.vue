<template>
  <div>
    <div class="title-box" v-if="formatData.isShowTitle">
      <div class="item-title">教练资质</div>
      <div class="form-tip">
        说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
        表示审核必须完善的资料
      </div>
    </div>

    <div v-else class="form-tip" style="padding: 0.08rem 0 0 0.2rem; margin-bottom: 0">
      说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
      表示审核必须完善的资料
    </div>
    <van-form>
      <van-field class="train-type green-require" label="教练类型">
        <template #input>
          <van-radio-group v-model="formData.coachIdentity" direction="horizontal">
            <van-radio :name="2" shape="square" checked-color="#FF9B26">兼职教练</van-radio>
            <van-radio :name="1" shape="square" checked-color="#FF9B26">专职教练</van-radio>
          </van-radio-group>
        </template>
      </van-field>

      <van-field
        v-model="formData.teachYear"
        class="green-require"
        type="number"
        maxlength="2"
        label="从业年限"
        placeholder="请输入从业年限"
      >
        <template #button>
          <span class="unit">年</span>
        </template>
      </van-field>
      <van-field
        v-model="formData.teachTitle"
        label="教练标题"
        disabled
        class="coach-title green-require"
      />
      <!-- <h3 class="cell-title">教练标题</h3>
      <van-field
        v-model="formData.teachTitle"
        show-word-limit
        maxlength="20"
        placeholder="请输入教练标题"
        class="coach-title"
      /> -->
      <h3 class="cell-title"><span class="green">*</span>教学特色</h3>
      <!-- <van-field
        v-model="formData.teachDescription"
        show-word-limit
        maxlength="30"
        class="coach-title"
        placeholder="请输入教练描述…"
      /> -->
      <!-- class="textarea-wrapper" -->
      <van-field
        class="descript green-require"
        v-model="formData.teachDescription"
        autosize
        rows="2"
        type="textarea"
        maxlength="30"
        show-word-limit
        placeholder="请用1-2句话总结自己的教学特色，吸引学员眼球！"
      />

      <div class="form-item-certificate">
        <label>
          <span class="cLabel">荣誉证书</span>
          <!-- <span class="desc">*建议仅放一张，&lt;10M/张， 800x800，jpg/png/gif格式</span> -->
          <span class="desc">&lt;10M/张，jpg/jpeg/png/gif格式</span>
        </label>
        <div class="file" v-for="(item, index) in formData.certificateImages" :key="index">
          <upload-file
            v-model="item.images"
            file-type="jpg|png|gif|jpeg"
            :max-size="10240 * 1024"
            :max-count="1"
          />
          <van-field
            v-model="item.name"
            class="textarea-wrapper textarea1"
            rows="2"
            autosize
            type="textarea"
            maxlength="20"
            placeholder="请输入证书名称"
          />
          <div class="icon-close" @click="deleteCertificate(index)"></div>
        </div>
        <div>
          <div class="add-file" @click="addCertificate">
            <van-icon name="plus" />
            <span>添加</span>
          </div>
        </div>
      </div>
      <h3 class="cell-title">教学成就</h3>
      <van-field
        v-model="formData.teachAchievement"
        autosize
        class="textarea-wrapper"
        rows="3"
        maxlength="500"
        type="textarea"
        placeholder="请写下自己的教学成果，展示自己的教学实力，建立学员信任！"
      />
      <h3 class="cell-title"><span class="green">*</span>教学说明</h3>
      <quill-editor
        v-model="formData.content"
        class="editor"
        height="2.7rem"
        @ready="editorReady"
        placeholder="新编原创内容有利于百度收录和宣传。请详细说明您的受训经历、教学范围、收费标准等等。"
      />
      <!-- <div class="rich-context" @click="contentFocus">
        <div class="rich-box" v-if="formData.content" v-html="formData.content"></div>
        <div class="tips" v-else>输入教学说明，吸引更多学员</div>
      </div> -->
      <div v-if="formatData.isShowTitle" class="fixed-bottom">
        <Checkbox v-model="checked" checked-color="#FF9B26" shape="square">
          我已阅读并同意
          <span class="protocol" @click="$router.push({ path: '/help', query: { tabIndex: 6 } })">
            《服务协议》
          </span>
        </Checkbox>
      </div>
    </van-form>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, watch } from 'vue'
  import { updateCoachQualificationInfo } from '@/api/coach-server'
  import { Toast, Checkbox } from 'vant'
  import UploadFile from '@/components/upload-file'
  import { useRoute } from 'vue-router'
  import QuillEditor from '@/components/quill-editor'

  const route = useRoute()
  const isCoachIdentity = route.query.isCoachIdentity // 是否已经是教练身份

  const props = defineProps({
    coachTitle: {
      type: String,
      default: '',
    },
  })

  const formData = reactive({
    coachIdentity: '', // 教练身份：1专职；2兼职
    teachYear: '', // 从业年限
    teachTitle: '', //教练标题
    teachDescription: '', // 教练描述
    certificateImages: [], // 荣誉证书图
    teachAchievement: '', // 教学成就
    content: '', // 教学说明
    coachId: null, // 教练id
  })
  const formatData = reactive({
    isUpdateTeachTitle: true, //是否能更新教练标题字段
    isShowTitle: true,
  })
  watch(
    () => props.coachTitle,
    (newVal) => {
      formData.teachTitle = newVal
    },
  )

  // 添加证书
  const addCertificate = () => {
    if (formData.certificateImages.length < 9) {
      formData.certificateImages.push({
        url: '',
        images: [],
        name: '',
      })
    } else {
      Toast('最多上传9张')
    }
  }

  const deleteCertificate = (index) => {
    formData.certificateImages.splice(index, 1)
  }
  const handleFormData = () => {
    let data = JSON.parse(JSON.stringify(formData))

    let certificateImages = []
    data.certificateImages.forEach((item) => {
      if (Array.isArray(item.images)) {
        if (item.images.length > 0) {
          certificateImages.push({
            name: item.name,
            url: item.images[0].path,
          })
        }
      }
    })

    data.certificateImages = certificateImages
    return data
  }
  const submit = (callback) => {
    if (formatData.isShowTitle && !checked.value) {
      Toast('请阅读并勾选服务协议~')
      return
    }

    let formData = handleFormData()

    updateCoachQualificationInfo(formData, isCoachIdentity)
      .then((res) => {
        callback(true, res)
      })
      .catch((error) => {
        callback(false, error)
      })
  }

  const editorReady = (editor) => {
    formData.content = editor
  }
  defineExpose({
    formData,
    formatData,
    submit,
  })

  const checked = ref(false)

  onMounted(() => {
    // nextTick(() => {
    //   console.log(formData.certificateImages, "999");
    //   if (formData.certificateImages.length === 0) {
    //     addCertificate();
    //   }
    // });
  })
</script>

<style scoped lang="scss">
  .van-form {
    // padding-bottom: 0.15rem;
    padding: 0 0.2rem 0.15rem;
  }

  .title-box {
    border-bottom: 1px solid #eee;
    padding: 0 0.1rem;
  }

  .form-tip {
    padding-left: 0.12rem;
    font-size: 0.12rem;
    margin-bottom: 0.12rem;
  }

  .red {
    color: #ff6445;
  }

  .green {
    color: #0abb08;
  }

  .green-require {
    :deep(.van-field__label) {
      &::before {
        margin-right: 2px;
        color: #0abb08;
        content: '*';
      }
    }
  }

  .item-title {
    position: relative;
    padding: 0.12rem 0.12rem 0.06rem 0.12rem;
    font-size: 0.14rem;
    font-weight: 600;
    color: #616568;
    &::before,
    &::after {
      position: absolute;
      content: '';
      display: block;
    }
    //border-bottom: 1px solid #eee;
    &::before {
      left: 0;
      top: 50%;
      transform: translate(0, -60%);
      width: 0.03rem;
      height: 0.14rem;
      background-color: #ff9b26;
      border-radius: 0.03rem;
    }
  }
  .form-item-certificate {
    padding: 0.17rem 0rem;
    font-size: 0.14rem;
    border-bottom: 1px solid #eeeeee;

    :deep(.van-cell) {
      border: none;
    }

    label {
      color: #453938;
      display: flex;
      align-items: center;
    }
    .cLabel {
      width: 0.8rem;
    }

    .desc {
      font-size: 0.1rem;
      color: #959595;
      margin-left: 0.2rem;
      vertical-align: bottom;
    }

    .file {
      margin-top: 0.1rem;
      display: flex;
      align-items: center;
    }

    :deep(.van-uploader) {
      height: 0.8rem;
    }

    .textarea-wrapper {
      flex: 1;
      padding: 0;
      height: 0.8rem;

      :deep(.van-cell__value) {
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 0.04rem;
        padding: 0.08rem;
      }

      :deep(.van-field__word-limit) {
        font-size: 0.12rem;
        color: #ccc;
      }

      :deep(.van-field__control) {
        color: #616568;
      }

      :deep(.van-field__control--min-height) {
        min-height: 0.6rem;
      }
    }
    .textarea1 {
      height: 0.8rem;
    }

    .add-file {
      display: inline-block;
      width: 0.8rem;
      height: 0.36rem;
      line-height: 0.36rem;
      text-align: center;
      color: #868686;
      font-weight: bold;
      margin-top: 0.1rem;
      background: #f5f5f5;
      border-radius: 0.02rem;
      border: 1px solid #e8e8e8;
      i {
        font-size: 0.12rem;
      }
      span {
        font-size: 0.12rem;
        font-weight: 400;
        color: #616568;
      }
    }
  }
  .cell-title {
    padding-top: 0.17rem;
    // padding-left: 0.2rem;
    font-size: 0.14rem;
    color: #646566;
    font-weight: 400;
  }
  :deep(.van-field__word-limit) {
    color: #b2b1b7;
  }
  .coach-title :deep(.van-field__word-limit) {
    position: absolute;
    right: 0;
    top: -0rem;
  }
  .descript :deep(.van-field__word-limit) {
    position: absolute;
    right: 0;
    bottom: 0.04rem;
    right: 0.08rem;
  }
  .icon-close {
    width: 0.4rem;
    height: 0.4rem;
    display: inline-block;
    background: url('../../../assets/images/icon/icon-close.png');
    background-size: 100% 100%;
  }
  .textarea-wrapper :deep(.van-cell__value--alone) {
    background-color: #fafafa;
    border-radius: 0.05rem;
    padding: 0.12rem;
    border: 0.01rem solid #e8e8e8;
  }
  .textarea-wrapper:after {
    border-bottom: 0px solid #000;
  }
  .unit {
    color: #453938;
  }
  .rich-context {
    overflow-y: scroll;
    height: 5rem;
    margin: 0 0.16rem;
    margin-top: 0.1rem;
    padding: 0.12rem;
    // min-height: 5rem;
    background: #fafafa;
    border-radius: 0.05rem;
    border: 0.01rem solid #e8e8e8;
    .tips {
      color: #b2b1b7;
    }
    :deep(img) {
      width: 100%;
    }
  }
  .fixed-bottom {
    // margin: 0.3rem 0.16rem 0rem;
    padding: 0 0.16rem;
    position: fixed;
    bottom: 0.75rem;
    width: 3.75rem;
    background: #fff;
    padding-top: 0.1rem;
    :deep(.van-checkbox__label) {
      color: #999999;
    }
  }
  .protocol {
    color: #e02525;
  }
  :deep(.van-field__control) {
    font-size: 0.14rem;
  }
  .cell-title,
  .cLabel,
  :deep(.van-field__label) {
    color: #453938;
    font-size: 0.14rem;
  }
  :deep(.van-uploader__upload) {
    height: 0.8rem;
  }
  .editor {
    margin-top: 0.05rem;
    // padding: 0 0.2rem;
  }
  :deep(.van-cell) {
    padding: 0.17rem 0rem;
  }
  :deep(.van-uploader__input),
  :deep(.van-uploader__preview-image) {
    width: 0.8rem;
    height: 0.8rem;
  }
</style>
