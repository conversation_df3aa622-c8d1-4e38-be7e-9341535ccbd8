<template>
  <van-overlay :show="show" z-index="1000" @click="close">
    <div class="main">
      <div class="title">{{ posters[selectIndex].title }}</div>
      <div class="tip">-长按下方图片保存并分享-</div>

      <div class="poster-images">
        <img v-if="imgDataURL" :src="imgDataURL" alt="" />
        <van-loading class="poster-loading" v-else vertical color="#1989fa">
          海报生成中...
        </van-loading>
      </div>
    </div>

    <div class="footer" v-if="showBottom">
      <div class="close" @click="close">
        <van-icon name="clear" color="#DFDFDF" size="0.17rem" />
      </div>
      <div class="title">- 选择海报 -</div>
      <div class="select">
        <div
          v-for="item in posters"
          :key="item.name"
          :class="['item', { active: selectIndex === item.name }]"
          @click.stop="togglePoster(item.name)"
        >
          <div class="icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="label">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <div class="poster-box" ref="posterSourceRef">
      <div class="flag">
        <div class="user-avatar" :style="avatarStyle" />
        <div class="user-info">
          <div class="username">{{ userInfo.realName || userInfo.userName }}</div>
          <div class="text">{{ posters[selectIndex].shareDesc }}</div>
        </div>
      </div>

      <!-- 爱教练海报 -->
      <div v-if="selectIndex === 0" class="poster-source">
        <img class="poster-cover" src="../../assets/images/user/poster-1.png" alt="" />
        <div class="poster-content-1">
          <div class="remarks">
            <p class="text1">我已影响了{{ inviteCount }}个人爱上运动</p>
            <p class="text1">爱教练，帮你找到有爱的教练</p>
            <p class="text2">长按识别二维码，了解爱教练</p>
          </div>
          <div class="qrcode"></div>
        </div>
      </div>

      <!-- 邀请入职教练海报 -->
      <div v-if="selectIndex === 1" class="poster-source">
        <img class="poster-cover" src="../../assets/images/user/poster-2.png" alt="" />
        <div class="poster-content-2">
          <div class="remarks">
            <p class="text1">爱教练</p>
            <p class="text1">一个超赞的私教预约平台</p>
            <p class="text2">长按扫码，填写入驻资料</p>
          </div>
          <div class="qrcode"></div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
  import { nextTick, ref, watch, reactive, computed } from 'vue'
  import QRCode from 'qrcodejs2'
  import html2canvas from 'html2canvas'
  import { getUserInviteCount, getUserInfo } from '@/api/user-server'
  import { getOssURL } from '@/common'
  import { localProxyStorage } from '@/utils/storage'
  import { baseURL } from '@/config'
  import { getBase64Image } from '@/utils'

  const selectIndex = ref(0)
  const posterSourceRef = ref(null)
  const imgDataURL = ref(null)
  const inviteCount = ref(0)
  const userInfo = ref({})
  const userAvatarDataURL = ref(null)

  const posters = reactive([
    {
      name: 0,
      title: '分享爱教练',
      shareDesc: '给你分享一个超赞的私教预约平台',
      imgDataURL: null,
      link: baseURL,
      icon: require('../../assets/images/user/poster-thumbnail-1.png'),
      status: '',
    },
    {
      name: 1,
      title: '邀请教练入驻',
      shareDesc: '诚挚邀请你入驻爱教练',
      imgDataURL: null,
      link: baseURL + '/account/coach-apply-form',
      icon: require('../../assets/images/user/poster-thumbnail-2.png'),
      status: '',
    },
  ])

  let props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    showBottom: {
      type: Boolean,
      default: true,
    },
    showSelectIndex: {
      type: Number,
      default: 0,
    },
  })

  const emit = defineEmits(['update:show'])

  // 加载用户头像
  const loadUserAvatar = (url, isReload = true) => {
    getBase64Image(url).then((imgDataURL) => {
      if (!imgDataURL && isReload) {
        loadUserAvatar(getOssURL('user/default_man_3x.png'), false)
        return
      }

      userAvatarDataURL.value = imgDataURL
    })
  }

  const avatarStyle = computed(() => {
    return {
      background: userAvatarDataURL.value
        ? `url("${userAvatarDataURL.value}") no-repeat`
        : 'transparent',
      'background-size': 'cover',
      'background-position': 'center',
    }
  })

  const genQrCode = (elem, link) => {
    elem.innerHTML = ''
    new QRCode(elem, {
      width: 60,
      height: 60,
      text: link,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correctLevel: QRCode.CorrectLevel.L,
    })
  }

  const genPoster = () => {
    return new Promise((resolve, reject) => {
      let elem = posterSourceRef.value
      let devicePixelRatio = window.devicePixelRatio
      // 用getBoundingClientRect 是为了解决白边问题
      let width = elem.getBoundingClientRect().width * 2
      let height = elem.getBoundingClientRect().height * 2

      let canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height

      html2canvas(elem, {
        canvas: canvas,
        width: width,
        height: height,
        scale: 2,
        dpi: devicePixelRatio,
        useCORS: true,
      })
        .then((canvas) => {
          resolve(canvas.toDataURL())
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const togglePoster = (index) => {
    selectIndex.value = index
    if (posters[index].status === 'loading') {
      return
    }

    if (posters[index].status === 'done') {
      imgDataURL.value = posters[index].imgDataURL
      return
    }

    posters[index].status = 'loading'
    imgDataURL.value = null

    nextTick(() => {
      const qrcodeEl = document.querySelector('.qrcode')
      const link = `${posters[index].link}?shareId=${localProxyStorage.user?.shareId}`

      genQrCode(qrcodeEl, link)

      genPoster().then((dataURL) => {
        imgDataURL.value = dataURL
        posters[index].imgDataURL = dataURL
        posters[index].status = 'done'
      })
    })
  }

  const close = () => {
    emit('update:show', false)
  }

  async function initPoster() {
    // 获取登录人信息
    const { data: userData } = await getUserInfo()
    userInfo.value = userData
    // 获取邀约人总数
    const { data: count } = await getUserInviteCount()
    inviteCount.value = count

    if (userAvatarDataURL.value) {
      // 如果有指定选中分享优先取指定选中
      togglePoster(props.showSelectIndex ? props.showSelectIndex : selectIndex.value)
    } else {
      let avatar = `${getOssURL(
        userData.headImg,
      )}?x-oss-process=image/resize,m_fill,h_480,w_480&v=${new Date().getTime()}`

      loadUserAvatar(avatar)

      // 等待
      const timer = setInterval(() => {
        console.log('setInterval')
        if (userAvatarDataURL.value) {
          clearInterval(timer)
          togglePoster(props.showSelectIndex ? props.showSelectIndex : selectIndex.value)
        }
      }, 200)
    }
  }

  const handleOpen = (value) => value && initPoster()

  watch(() => props.show, handleOpen)
</script>

<style lang="scss" scoped>
  .poster-wrap {
    width: 100%;
    height: 100vh;
  }

  .main {
    overflow: auto;
    height: calc(100vh - 1.1rem);

    .title {
      font-size: 0.18rem;
      font-weight: 600;
      text-align: center;
      color: #fff;
      padding-top: 0.3rem;
    }
  }

  .poster-images {
    width: 100%;
    height: 4.29rem;
    margin-top: 0.1rem;
    margin-bottom: 0.15rem;

    img {
      width: 2.57rem;
      height: 100%;
      margin: 0 auto;
      display: block;
    }

    .poster-item {
      width: 2.57rem;
      height: 4.58rem;
      border: 1px solid red;
      margin: 0 auto;
    }
  }

  .tip {
    margin-top: 0.08rem;
    text-align: center;
    font-size: 0.12rem;
    font-weight: 400;
    color: #ffffff;
  }

  .poster-loading {
    text-align: center;
    padding-top: 1.6rem;
  }

  .poster-box {
    position: absolute;
    left: -500%;
    top: -500%;
    //top: 0;
    width: 375px;
    z-index: 1;

    .flag {
      width: 100%;
      height: 58px;
      display: flex;
      align-items: center;
      background: url('../../assets/images/inviter-top-bg.png') no-repeat;
      background-size: 100% 100%;

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 0 8px 0 10px;
      }

      .username {
        font-size: 15px;
        color: #170606;
      }

      .text {
        font-size: 12px;
        color: #453838;
      }
    }

    .poster-source {
      width: 100%;
      height: 567px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .poster-content-1,
    .poster-content-2 {
      width: 100%;
      position: absolute;
      bottom: 55px;
      padding: 0 43px;
      //right: 43px;
      left: 0;

      .remarks {
        float: left;
      }

      .text1 {
        font-size: 15px;
        color: #1a1b1d;
        line-height: 21px;
      }

      .text2 {
        font-size: 12px;
        color: #b2b1b7;
      }

      .qrcode {
        width: 60px;
        height: 60px;
        float: right;
      }
    }
  }

  .footer {
    width: 100%;
    height: 1.1rem;
    background: #ffffff;
    border-radius: 0.1rem 0.1rem 0 0;
    position: absolute;
    bottom: 0;

    .title {
      padding: 0.1rem 0;
      text-align: center;
      font-size: 0.14rem;
      color: #616568;
    }

    .close {
      position: absolute;
      right: 0.1rem;
      top: 0.12rem;
    }

    .select {
      display: flex;
      justify-content: center;

      .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 0.15rem;

        .icon {
          width: 0.4rem;
          height: 0.4rem;
          border: 2px solid transparent;
          box-sizing: content-box;

          img {
            width: 100%;
            height: 100%;
          }
        }
        .label {
          margin-top: 0.04rem;
          font-size: 0.12rem;
          color: #1a1b1d;
        }
      }

      .active {
        .icon {
          border: 2px solid #ff9b26;
          border-radius: 0.02rem;
          position: relative;

          &:after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 0.2rem;
            height: 0.2rem;
            background: url('../../assets/images/icon/icon-sucess-2.png') no-repeat;
            background-size: 100% 100%;
            z-index: 2;
          }

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0.4rem;
            height: 0.4rem;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
          }
        }

        .label {
          color: #ff9b26;
        }
      }
    }
  }
</style>
