<template>
  <Teleport to="body">
    <div class="321ActivePopup">
      <van-popup v-model:show="show">
        <div class="popup-content">
          <div class="activity-theme-wrapper">
            <img class="activity-theme" src="../images/active-popup-img.png" />
            <SolidButton width="3.34rem" class="act-button" @click="toActiveHome">
              马上参与
            </SolidButton>
          </div>

          <div class="close" @click="onClose" />
        </div>
      </van-popup>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { localProxyStorage } from '@/utils/storage'
  import SolidButton from './SolidButton'

  const router = useRouter()
  const show = ref(false)

  const updateShow = (state) => (show.value = state)

  const toActiveHome = () => {
    updateShow(false)
    router.push({ name: 'a20230510jlzm' })
  }

  const onClose = () => {
    updateShow(false)
  }

  const initialize = () => {
    let actEndTime = 1684987200000 // 2023.05.25 12:00:00
    let nowTime = new Date().getTime()

    // 活动结束不弹窗
    if (nowTime > actEndTime) return

    const dayEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime().toString()
    const JLZM_ACT_POPUP_2 = localProxyStorage.JLZM_ACT_POPUP_2

    // 每天只弹一次，活动期间做多三次
    if (JLZM_ACT_POPUP_2 && typeof JLZM_ACT_POPUP_2 === 'string') {
      const data = localProxyStorage.JLZM_ACT_POPUP_2.split('|')
      if (nowTime > data[data.length - 1] && data.length < 7) {
        updateShow(true)
        data.push(dayEndTime)
        localProxyStorage.JLZM_ACT_POPUP_2 = data.join('|')
      }
    } else {
      localProxyStorage.JLZM_ACT_POPUP_2 = dayEndTime
      updateShow(true)
    }
  }

  initialize()
</script>

<style lang="scss" scoped>
  .popup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .activity-theme-wrapper {
    position: relative;

    .activity-theme {
      width: 3.75rem;
      height: 2.96rem;
    }

    .act-button {
      position: absolute;
      bottom: 0.16rem;
      left: 0.2rem;
    }
  }

  .close {
    width: 0.6rem;
    height: 0.6rem;
    background: url('../images/active-popup-close.png') no-repeat;
    background-size: 100% 100%;
  }

  :deep(.van-popup) {
    background: transparent;
  }
</style>
