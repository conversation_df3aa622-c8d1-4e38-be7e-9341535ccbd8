<template>
  <div class="i-upload-video">
    <!-- 预览区 -->
    <div v-if="!inputShow" class="i-preview" :style="getSizeStyle()">
      <div v-if="deletable" class="i-preview-delete" @click="onBeforeDelete">
        <van-icon class="icon" name="cross" />
      </div>
      <div v-if="uploaderMaskShow" class="i-uploader__mask">
        <div v-if="modelValue.status === 'uploading'" class="i-upload__loading">
          <van-loading />
        </div>
        <div v-if="modelValue.status === 'failed'">
          <van-icon size="0.3rem" name="close" />
        </div>
        <div class="uploader__mask-message">{{ modelValue.message }}</div>
      </div>
      <xg-player class="i-preview__video" :url="modelValue.url" :poster="modelValue.poster" />
    </div>
    <!-- 上传区 -->
    <div class="i-uploader__upload" :style="getSizeStyle()">
      <div class="i-badge__wrapper">
        <slot>
          <div class="i-upload-icon">
            <img src="../../assets/images/icon/icon-upload-video.png" alt="" />
          </div>
          <p class="i-upload-text">上传视频</p>
          <slot name="tip">
            <p class="i-upload-tip">{{ tip }}</p>
          </slot>
        </slot>
      </div>
      <input
        v-show="inputShow"
        ref="inputRef"
        type="file"
        :disabled="disabled"
        @change="onChange"
        class="i-uploader__input"
        accept="video/*"
      />
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue'
  import XgPlayer from '@/components/xg-player'

  const inputRef = ref('')
  const inputShow = ref(true)

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {},
    },
    width: String,
    height: String,
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否展示删除按钮
    deletable: {
      type: Boolean,
      default: true,
    },
    // 文件读取前的回调函数
    beforeRead: Function,
    afterRead: Function,
    beforeDelete: Function,
    tip: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const uploaderMaskShow = computed(() => {
    return !(
      props.modelValue.status === 'done' ||
      props.modelValue.status === '' ||
      props.modelValue.status === null ||
      props.modelValue.status === undefined
    )
  })

  // 处理传入的视频链接预览操作
  const renderPreviewVideo = () => {
    if (props.modelValue && props.modelValue.url) {
      inputShow.value = false
    }
  }

  watch(
    () => props.modelValue,
    (newVal, oldVal) => {
      if (Object.prototype.toString.call(newVal) === '[object Object]') {
        if (oldVal) {
          if (newVal.url === oldVal.url) return
        }

        renderPreviewVideo()
      }
    },
    {
      immediate: true,
    },
  )

  const resetInput = () => {
    if (inputRef.value) {
      inputRef.value.value = ''
    }
  }

  const onAfterRead = (file) => {
    file = reactive(file)
    emit('update:modelValue', file)
    if (props.afterRead) {
      props.afterRead(file)
    }
  }

  const onBeforeDelete = () => {
    if (props.beforeDelete) {
      let response = props.beforeDelete(props.modelValue)
      if (!response) return
    }

    resetInput()
    inputShow.value = true
    emit('update:modelValue', {})
  }

  const readFile = (file) => {
    const result = {
      file: file,
      url: URL.createObjectURL(file),
      status: '',
      message: '',
      poster: '',
    }

    inputShow.value = false
    // 在这里可以做，上传数量限制等。
    onAfterRead(result)
  }

  const onChange = (event) => {
    const { files } = event.target
    if (props.disabled || !files || !files.length) return

    const file = files.length === 1 ? files[0] : [].slice.call(files)

    if (props.beforeRead) {
      let response = props.beforeRead(file)
      if (!response) {
        resetInput()
        return
      }
    }

    readFile(file)
  }

  const getSizeStyle = () => {
    return {
      width: props.width,
      height: props.height,
    }
  }
</script>

<style lang="scss" scoped>
  .i-upload-video {
    position: relative;
    user-select: none;
    background: #f8f8f8;
    overflow: hidden;
    border-radius: 0.04rem;
  }

  .i-uploader__upload {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    border: 1px dashed #ececec;
    align-items: center;
    justify-content: center;
  }

  .i-badge__wrapper {
    text-align: center;
  }

  .i-upload-text {
    font-size: 0.14rem;
    color: rgba(0, 0, 0, 0.65);
  }

  .i-upload-tip {
    margin-top: 0.08rem;
    font-size: 0.12rem;
    color: #b2b1b7;
  }

  .i-uploader__mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: rgba(50, 50, 51, 0.88);
    z-index: 999;
  }

  .uploader__mask-message {
    margin-top: 0.06rem;
  }

  .i-upload-icon {
    img {
      width: 0.56rem;
      height: 0.56rem;
    }
  }

  .i-uploader__input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;
    opacity: 0;
  }

  .i-preview {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
  }

  .i-preview-delete {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 9999;
    width: 0.26rem;
    height: 0.26rem;
    background: #000000;
    border-radius: 0 0 0 1rem;
    opacity: 0.7;
    color: #ffffff;
    font-size: 0.12rem;

    .icon {
      transform: translate(0.1rem, 0.02rem);
    }
  }

  .i-preview__video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
