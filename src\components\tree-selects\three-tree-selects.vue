<template>
  <div class="contain">
    <van-tree-select
      height="3.85rem"
      class="first-select"
      v-model:main-active-index="activeIndex"
      :items="formData.allDatas"
      @click-nav="firstSelect"
    >
      <template #content>
        <van-tree-select
          v-model:main-active-index="secendIndex"
          :items="formData.secendList"
          height="3.85rem"
          @click-nav="secendSelect"
        >
          <template #content>
            <van-checkbox-group
              v-if="isMoreSelect"
              v-model="threeChecked"
              ref="checkboxGroup"
              checked-color="#ff6445"
            >
              <van-checkbox
                class="checkbox-item"
                v-for="(item, index) in formData.threeList"
                :key="item"
                :name="item.id"
                label-position="left"
                shape="square"
                :ref="(el) => (checkboxRefs[index] = el)"
                :class="{ 'select-cell': formData.threeCheckedId.includes(item.id) }"
                @click="toggle(index, item)"
                >{{ item.name }}</van-checkbox
              >
            </van-checkbox-group>
            <div v-if="!isMoreSelect" class="category-box">
              <div
                v-for="(item, index) in formData.threeList"
                :key="item"
                @click="singleClick(item, index)"
              >
                <van-cell
                  :title="item.name"
                  :class="{ 'select-cell': formData.thirdIndex === index }"
                  center
                >
                  <template #right-icon>
                    <i v-if="formData.thirdIndex === index" class="icon icon-selected" />
                  </template>
                </van-cell>
              </div>
            </div>
          </template>
        </van-tree-select>
      </template>
    </van-tree-select>
  </div>
</template>

<script setup>
  import { ref, watch, reactive, onMounted } from 'vue'

  const props = defineProps({
    dataList: {
      type: Array,
      default: () => [],
    },
    isMoreSelect: {
      type: Boolean,
      default: false,
    },
    clear: {
      type: Boolean,
      default: false,
    },
    searchVal: {
      type: String,
      default: '',
    },
  })

  watch(
    () => props.clear,
    () => {
      clear()
    },
  )

  const emit = defineEmits(['selectChange'])

  const activeIndex = ref(0)
  const secendIndex = ref(0)
  const thirdIndex = ref(0)

  const cateIdObj = ref({
    firstCategoriesId: '',
    secondCategoriesId: '',
    thirdlyCategoriesId: '',
    thirdlyCategoriesIds: [],
  })

  const lastSecendIndex = ref(null)

  // 多选框选中数据
  const threeChecked = ref([])
  const checkboxRefs = ref([])

  const checkboxGroup = ref(null)

  watch(
    () => [...threeChecked.value],
    (nowList) => {
      formData.threeCheckedId = nowList
    },
  )
  const formData = reactive({
    threeCheckedId: [],
    thirdIndex: '',
    secendList: [],
    threeList: [],
    allDatas: [],
    allReturnObj: {},
    status: true,
  })

  onMounted(() => {
    formData.allDatas = props.dataList
    formData.secendList = formData.allDatas[0]?.childCategoriesVos
    formData.threeList = formData.allDatas[0]?.childCategoriesVos[0]?.childCategoriesVos
    // 首次点击首页分类
    console.log(formData.allDatas, 'formData.allDatas111')
    formData.allDatas.map((item, index) => {
      item.childCategoriesVos?.map((childItem, childIndex) => {
        // console.log(childItem, "childCategoriesVos");
        childItem.childCategoriesVos?.map((provideItem, proIndex) => {
          if (props.searchVal === provideItem.name) {
            activeIndex.value = index
            secendIndex.value = childIndex
            thirdIndex.value = proIndex

            lastSecendIndex.value = childIndex
            cateIdObj.value.firstCategoriesId = item.id
            cateIdObj.value.secondCategoriesId = childItem.id
            if (props.isMoreSelect) {
              cateIdObj.value.thirdlyCategoriesIds.push(provideItem.id)
            } else {
              cateIdObj.value.thirdlyCategoriesId = provideItem.id
            }
            formData.thirdIndex = proIndex
            formData.secendList = item.childCategoriesVos
            formData.threeList = childItem.childCategoriesVos
            threeChecked.value.push(provideItem.id)
          }
        })
      })
    })
    // 首次点击首页分类
    if (props.searchVal) {
      console.log('首次点击首页分类')
      if (props.isMoreSelect) {
        const idObj = {}
        idObj['firstCategoriesId'] = formData.allDatas[activeIndex.value]?.id || ''
        idObj['secondCategoriesId'] = formData.secendList[secendIndex.value]?.id || ''
        idObj['thirdlyCategoriesIds'] = []
        idObj['thirdlyCategoriesIds'][0] = formData.threeList[thirdIndex.value]?.id || ''
        formData.allReturnObj['thirdlyCategoriesId'] = threeChecked
        console.log(idObj, 'idObj')

        formData.allReturnObj = idObj
        emit('selectChange', formData.allReturnObj)
      } else {
        // const idObj = {};
        // idObj["firstCategoriesId"] = formData.allDatas[activeIndex.value].id;
        // idObj["secondCategoriesId"] =
        //   formData.allDatas[activeIndex.value].childCategoriesVos[secendIndex.value].id;
        // idObj["thirdlyCategoriesId"] = formData.threeList[formData.thirdIndex].id;
        // console.log(cateIdObj.value, "cateIdObj11111111111");
        // formData.allReturnObj = idObj;
        emit('selectChange', cateIdObj.value)
      }
    }
  })

  // 一级菜单
  const firstSelect = (index) => {
    secendIndex.value = null
    formData.secendList = formData.allDatas[index].childCategoriesVos
    formData.threeList = formData.allDatas[index].childCategoriesVos[0].childCategoriesVos

    const idObj = {}
    idObj['firstCategoriesId'] = formData.allDatas[index].id
    if (formData.allDatas[index].id === '0') {
      idObj['firstCategoriesId'] = ''
    }
    idObj['secondCategoriesId'] = ''
    if (props.isMoreSelect) {
      idObj['thirdlyCategoriesIds'] = []
    } else {
      idObj['thirdlyCategoriesId'] = ''
    }
    formData.allReturnObj = idObj
    emit('selectChange', formData.allReturnObj)
  }
  // 二级菜单
  const secendSelect = (index) => {
    secendIndex.value = index

    formData.thirdIndex = ''

    // 切换二级菜单，清除其余二级全部选中状态
    console.log(lastSecendIndex.value, 'lastSecendIndex.value')
    if (lastSecendIndex.value && lastSecendIndex.value !== index) {
      // formData.secendList[lastSecendIndex.value].isAllSelect = false;
      threeChecked.value = []
    }
    formData.threeList = formData.secendList[index].childCategoriesVos

    const idObj = {}
    idObj['firstCategoriesId'] = formData.allDatas[activeIndex.value].id
    idObj['secondCategoriesId'] = formData.secendList[index].id
    if (props.isMoreSelect) {
      idObj['thirdlyCategoriesIds'] = []
    } else {
      idObj['thirdlyCategoriesId'] = ''
    }

    // 全部
    if (index === 0) {
      idObj['secondCategoriesId'] = ''
      if (props.isMoreSelect) {
        idObj['thirdlyCategoriesIds'] = []
      } else {
        idObj['thirdlyCategoriesId'] = ''
      }
    }
    formData.allReturnObj = idObj
    emit('selectChange', formData.allReturnObj)
    lastSecendIndex.value = index
    console.log(threeChecked.value, 'threeChecked.value')
  }
  // 三级菜单 多选
  const toggle = (index) => {
    // formData.threeCheckedId = threeChecked.value.map((item) => item.id);
    formData.threeCheckedId = threeChecked.value

    if (index === 0) {
      // 全选, 与其余三级互斥
      formData.allDatas[activeIndex.value].childCategoriesVos[secendIndex.value].isAllSelect =
        !formData.allDatas[activeIndex.value].childCategoriesVos[secendIndex.value].isAllSelect
      threeChecked.value = []
      threeChecked.value.push('0')
    } else if (threeChecked.value.length > 1 && threeChecked.value.includes('0')) {
      threeChecked.value.splice(threeChecked.value.indexOf('0'), 1)
    }

    const idObj = {}
    console.log(formData.allDatas, 'formData.allDatas')
    console.log(activeIndex.value, secendIndex.value)
    idObj['firstCategoriesId'] = formData.allDatas[activeIndex.value].id
    idObj['secondCategoriesId'] = formData.secendList[secendIndex.value].id
    idObj['thirdlyCategoriesIds'] = threeChecked.value
    // 获取三级选中信息
    if (threeChecked.value.length === 1 && threeChecked.value[0] !== '0') {
      const thirdList = formData.allDatas[activeIndex.value].childCategoriesVos[secendIndex.value]
      thirdList.childCategoriesVos.map((item) => {
        if (item.id === threeChecked.value[0]) {
          idObj['thirdSingle'] = item.name
        }
      })
    }
    formData.allReturnObj = idObj
    emit('selectChange', formData.allReturnObj)

    // let arr = checked.value;
    // console.log(checkboxRefs.value[index], "checkboxRefs");
    // checkboxRefs.value[1].toggle();
  }

  // 三级菜单 单选
  const singleClick = (item, index) => {
    formData.thirdIndex = index

    const idObj = {}
    idObj['firstCategoriesId'] = formData.allDatas[activeIndex.value].id
    idObj['secondCategoriesId'] = formData.secendList[secendIndex.value].id
    idObj['thirdlyCategoriesId'] = item.id
    idObj['thirdlyCategoriesName'] = item.name
    idObj['indexObj'] = {
      fisrtIndex: activeIndex.value,
      secendIndex: secendIndex.value,
      thirdIndex: formData.thirdIndex,
    }
    formData.allReturnObj = idObj

    emit('selectChange', formData.allReturnObj)
  }
  const clear = () => {
    if (props.isMoreSelect) {
      secendIndex.value = null
      activeIndex.value = 0
      threeChecked.value = []
      formData.secendList = formData.allDatas[0]?.childCategoriesVos
      formData.threeList = []
    } else {
      secendIndex.value = null
      activeIndex.value = 0
      formData.secendList = formData.allDatas[0]?.childCategoriesVos
      formData.threeList = []
      formData.thirdIndex = null
    }
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';
  @include Icon('selected', 0.15rem, 0.15rem);
  .first-select {
    width: 100vw;
    // box-shadow: 0rem 0.02rem 0.1rem 0rem rgba(0, 0, 0, 0.1);
  }
  :deep(.van-popup--center) {
    top: auto;
    left: auto;
    transform: translate(-5%, 0%);
  }
  :deep(.van-sidebar-item--select:before) {
    display: none;
  }
  :deep(.van-tree-select__nav-item) {
    padding: 0.1rem;
  }

  :deep(.van-tree-select__nav) {
    width: 0.9rem;
  }
  :deep(.van-tree-select__nav),
  :deep(.van-sidebar-item) {
    background-color: #fff;
    flex: none;
  }
  :deep(.van-sidebar-item) {
    background-color: #fff;
    border-right: 0.01rem solid #f5f5f5;
  }
  :deep(.van-sidebar-item--select),
  :deep(.van-sidebar-item--select:active) {
    color: #ff6445ff;
    font-weight: 600;
    background: #fff7f5ff;
  }
  // :deep(.van-cell--center),
  // :deep(.select-cell) {
  //   height: 0.4rem;
  //   line-height: 0.24rem;
  // }
  :deep(.select-cell) {
    padding: 0.1rem;
  }

  .checkbox-item {
    padding: 0.1rem 0.3rem;
    width: 100%;
    height: 0.4rem;
    display: flex;
    justify-content: space-between;
  }
  :deep(.van-checkbox__icon) {
    height: 0.7em;
  }
  :deep(.van-icon) {
    width: 0.15rem;
    height: 0.15rem;
    line-height: 1;
    font-size: 0.7em;
  }
  .category-box {
    position: relative;
    width: 100%;
  }
  .select-cell {
    color: #ff6445;
    font-weight: 600;
    background-color: #fff7f5;
  }
  .van-cell {
    padding: 0.1rem;
    font-size: 0.14rem;
    line-height: 0.2rem;
  }
  .select-cell :deep(.van-checkbox__label--left) {
    color: #ff6445;
  }
  // :deep(.van-sidebar-item--select) {
  //   line-height: 0.2rem;
  // }
  :deep(.van-sidebar-item) {
    line-height: 0.2rem;
  }
  :deep(.van-cell__title) {
    width: 1rem;
    flex-shrink: 0;
  }
</style>
