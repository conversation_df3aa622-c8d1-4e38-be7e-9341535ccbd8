<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="keywords" content="私人教练,私人教练培训,高级私人教练网,爱教练私教网">
    <meta name="description" content="爱教练私教网高级私人教练网提供私人教练培训信息,囊括网球教练,羽毛球教练,乒乓球教练,游泳教练,瑜伽教练,高尔夫教练等各类私人教练培训信息,私人教练一站式服务">
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no,minimal-ui, viewport-fit=cover"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>私人教练,私人教练培训,高级私人教练网,爱教练私教网</title>
    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>

    <!-- 埋点 SDK -->
    <% if (process.env.VUE_APP_RUN_ENV === 'development' || process.env.VUE_APP_RUN_ENV === 'testing' || process.env.VUE_APP_RUN_ENV === 'production') { %>
    <script charset='UTF-8' src="/static/sensorsdata/sensorsdata.min.js"></script>
    <script>
      var sensors = window['sensorsDataAnalytic201505'];
      sensors.init({
        server_url: '<%= process.env.VUE_APP_TRACK_SERVER_URL %>',
        is_track_single_page: true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
        use_client_time: true,
        // 默认 image 支持使用 'ajax' 和 'beacon'
        send_type: 'image',
        // 开启批量发送
        batch_send: false,
        heatmap: {
          //是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
          clickmap: 'not_collect',
          //是否开启触达图，not_collect 表示关闭，不会自动采集 $WebStay 事件，可以设置 'default' 表示开启。
          scroll_notice_map: 'not_collect',
          // collect_tags 配置其他元素类型的元素点击事件采集
          collect_tags: {}
        },
        // 日志输出功能
        show_log: false,
      });

      // 注册公共属性
      sensors.registerPage({
        "$platform": "h5",
        "$project": "ijiaolian"
      });

      sensors.quick('autoTrack');
    </script>
    <% } %>
    <!-- 埋点 SDK END -->

  </head>
  <body>
    <noscript>
      <strong>
        We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.
      </strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
