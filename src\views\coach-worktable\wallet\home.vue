<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="container">
        <div class="balance-wrap">
          <div class="balance-top">
            <div class="balance-money">{{ wallet?.accountBalance || 0 }}</div>
            <div class="balance-text">钱包余额（元）</div>
            <div class="balance-operation">
              <button class="withdrawal-btn" @click="handleExtract">我要提现</button>
            </div>
          </div>

          <div class="balance-bottom">
            <div class="total-money">
              <div class="value">{{ recentEarning?.rangeIncome || 0 }}</div>
              <div class="label">7日新增收益（元）</div>
            </div>
            <div class="total-money">
              <div class="value">{{ recentEarning?.totalIncome || 0 }}</div>
              <div class="label">累计收益（元）</div>
            </div>
          </div>
        </div>
        <div class="bill-list">
          <div class="collapse">
            <div class="collapse-item" v-for="(item, index) in bill" :key="index">
              <div class="collapse-item__title" @click="toggle(item, index)">
                <div class="month">
                  <span>{{ item.month }}</span>
                  <van-icon :class="[item.open ? 'open' : 'close']" name="play" size="0.12rem" />
                </div>
                <div class="total">
                  <span>收益￥{{ item.incomeAmount }}</span>
                  <span>提现 ￥{{ item.withdrawalAmount }}</span>
                </div>
              </div>
              <div v-show="item.open" class="collapse-item__wrapper">
                <div class="collapse-item__content">
                  <van-list
                    v-model:loading="item.loading"
                    :finished="item.finished"
                    @load="() => onLoadMonthlyBill(index)"
                    ref="listRef"
                  >
                    <div class="bill-item" v-for="item in item.list" :key="item">
                      <div class="operate-log">
                        <div :class="billClassName(item)"></div>
                        <div class="operate-content">
                          <p class="operate-text">{{ item.title }}</p>
                          <p v-if="item.remarks" class="username">{{ item.remarks }}</p>
                          <p class="operate-time">{{ item.createTime }}</p>
                        </div>
                      </div>
                      <div class="money-log">
                        <div :class="['value', { yellow: item.businessType === 'CONSUME_INCOME' }]">
                          {{ plusOrMinusAmount(item) }}
                        </div>
                        <div class="label">{{ item.billStateName }}</div>
                      </div>
                    </div>
                  </van-list>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import dayjs from 'dayjs'
  import {
    reqCoachWallet,
    reqRecentEarnings,
    reqAllMonthlyRevenue,
    reqMonthlyBill,
  } from '@/api/coach-worktable'

  const router = useRouter()
  const listRef = ref(null)
  const wallet = ref(null) // 钱包数据
  const recentEarning = ref(null) // 最近收益
  const bill = ref([])

  const toggle = (bill, index) => {
    bill.open = !bill.open
    listRef.value[index]?.check()
  }

  const billClassName = (bill) => {
    let className = ['operate-icon']
    className.push({
      hx: bill.businessType === 'CONSUME_INCOME',
      tx: bill.businessType === 'WITHDRAWAL',
      failure: bill.businessType === 'WITHDRAWAL_FAIL_REFUND',
    })
    return className
  }

  const plusOrMinusAmount = (bill) => {
    // 核销
    if (bill.businessType === 'CONSUME_INCOME') {
      return '+' + bill.amount
    }
    // 提现
    if (bill.businessType === 'WITHDRAWAL') {
      return '-' + bill.amount
    }
    // 提现失败
    if (bill.businessType === 'WITHDRAWAL_FAIL_REFUND') {
      return bill.amount
    }
  }

  const onLoadMonthlyBill = (index) => {
    let monthly = bill.value[index]

    let params = {
      lastBillId: monthly.list[monthly.list.length - 1]?.billId,
      month: monthly.groupField,
      pageSize: 7,
    }

    reqMonthlyBill(params).then((res) => {
      const { data } = res
      monthly.list = monthly.list.concat(data)
      // 加载状态结束
      monthly.loading = false

      // 当月流水数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        monthly.finished = true

        // 加载下一个月的流水数据
        if (index + 1 <= bill.value.length - 1) {
          bill.value[index + 1].open = true
          listRef.value[index + 1]?.check()
        }
      }
    })
  }

  // 获取教练钱包信息
  const getCoachWallet = () => {
    reqCoachWallet().then((res) => {
      const { data } = res
      wallet.value = data
    })
  }

  // 获取最近收益
  const getRecentEarnings = () => {
    let params = { dayRange: 7 }
    reqRecentEarnings(params).then((res) => {
      const { data } = res
      recentEarning.value = data
    })
  }

  const getAllMonthlyRevenue = () => {
    reqAllMonthlyRevenue().then((res) => {
      const { data } = res
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
          let curYear = dayjs().get('year')
          let billYear = dayjs(item.groupField).get('year')

          if (curYear === billYear) {
            let month = dayjs(item.groupField).get('month')
            item.month = month + 1 + '月份'
          } else {
            item.month = dayjs(item.groupField).format('YYYY年MM月')
          }

          item.open = index === 0
          item.loading = false
          item.finished = false
          item.list = []
        })

        bill.value = data
      }
    })
  }

  const handleExtract = () => {
    router.push({
      name: 'myWorktableWalletExtract',
    })
  }

  const init = () => {
    getCoachWallet()
    getRecentEarnings()
    getAllMonthlyRevenue()
  }

  init()
</script>

<style lang="scss" scoped>
  .balance-wrap {
    padding: 0 0.15rem;
    background: #fff;

    .balance-top {
      border-bottom: 1px solid #eeeeee;
      padding: 0.2rem 0 0.15rem 0;
    }

    .balance-bottom {
      display: flex;
    }

    .total-money {
      flex: 1;
      padding: 0.12rem 0;

      .label {
        color: #b2b1b7;
        font-size: 0.12rem;
        text-align: center;
      }
      .value {
        font-size: 0.18rem;
        color: #1a1b1d;
        text-align: center;
      }
    }

    .balance-money {
      font-size: 0.3rem;
      font-weight: 600;
      color: #1a1b1d;
      text-align: center;
    }

    .balance-text {
      font-size: 0.12rem;
      color: #616568;
      text-align: center;
    }

    .balance-operation {
      margin-top: 0.15rem;
      text-align: center;
    }

    .withdrawal-btn {
      width: 0.92rem;
      height: 0.32rem;
      background: #ff9b26;
      border-radius: 0.16rem;
      font-size: 0.14rem;
      color: #fff;
    }
  }

  .bill-list {
    margin-top: 0.08rem;
    background: #fff;

    .collapse {
      .collapse-item {
        border-bottom: 0.05rem solid #f7f7f7;
      }

      .collapse-item__title {
        padding: 0.1rem 0.15rem;
      }

      .collapse-item__wrapper {
        overflow: hidden;
        transition: height 0.3s ease-in-out;
        will-change: height;
        border-top: 1px solid #eeeeee;
      }

      .bill-item {
        padding: 0.1rem 0.15rem;
        border-bottom: 1px solid #eeeeee;
        display: flex;
        justify-content: space-between;

        .operate-log {
          display: flex;
        }

        .operate-icon {
          width: 0.4rem;
          height: 0.4rem;
          background: #ff9b26;
          border-radius: 50%;
        }

        .failure {
          background: url('../../../assets/images/coach-worktable/icon-txsb.png') no-repeat;
          background-size: 100% 100%;
        }

        .tx {
          background: url('../../../assets/images/coach-worktable/icon-tx.png') no-repeat;
          background-size: 100% 100%;
        }

        .hx {
          background: url('../../../assets/images/coach-worktable/icon-hx.png') no-repeat;
          background-size: 100% 100%;
        }

        .operate-text {
          font-size: 0.14rem;
          color: #1a1b1d;
        }

        .operate-time {
          margin-top: 0.02rem;
          font-size: 0.12rem;
          color: #b2b1b7;
        }

        .username {
          margin-top: 0.02rem;
          font-size: 0.12rem;
          color: #616568;
          word-break: break-all;
        }

        .operate-content {
          margin-left: 0.1rem;
          flex: 1;
        }

        .money-log {
          text-align: right;

          .label {
            font-size: 0.12rem;
            color: #979797;
          }
          .value {
            font-size: 0.16rem;
            font-weight: bold;
            color: #1a1b1d;
          }

          .yellow {
            color: #ff9b26;
          }
        }
      }

      .month {
        font-size: 0.18rem;
        font-weight: 600;
        color: #1a1b1d;

        .van-icon {
          margin-left: 0.02rem;
        }
      }

      .total {
        margin-top: 0.03rem;
        font-size: 0.14rem;
        color: #1a1b1d;

        span {
          margin-right: 0.39rem;
        }
      }

      .open {
        transform: rotate(90deg);
      }

      .close {
        transform: rotate(0);
      }
    }
  }
</style>
