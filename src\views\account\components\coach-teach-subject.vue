<template>
  <div>
    <div v-if="formatData.isShowTitle" class="title-box">
      <div class="item-title">授课信息</div>
      <div class="form-tip">
        说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
        表示审核必须完善的资料
      </div>
    </div>

    <div v-else class="form-tip" style="padding: 0.08rem 0 0 0.2rem; margin-bottom: 0">
      说明：“<span class="red">*</span>”表示必填项，“<span class="green">*</span>”
      表示审核必须完善的资料
    </div>
    <div class="coach-teach-subject">
      <van-form>
        <van-field
          v-model="formatData.skillType"
          required
          @click="skillPickerShow = true"
          is-link
          readonly
          label="授课科目"
          placeholder="请选择授课科目"
        ></van-field>
        <van-field class="adress" label="适用人群">
          <template #input>
            <van-checkbox-group v-model="formData.forTheCrowd" direction="horizontal">
              <van-checkbox class="checkbox" name="1" shape="square" checked-color="#FF9B26">
                初阶
              </van-checkbox>
              <van-checkbox class="checkbox" name="2" shape="square" checked-color="#FF9B26">
                中阶
              </van-checkbox>
              <van-checkbox class="checkbox" name="3" shape="square" checked-color="#FF9B26">
                高阶
              </van-checkbox>
              <van-checkbox class="checkbox" name="4" shape="square" checked-color="#FF9B26">
                体考
              </van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>

        <week-picker
          class="teach-data"
          v-model="formData.teachingWeek"
          label="授课时间"
          placeholder="请选择工作日"
          is-link
        />
        <hours-picker label=" " placeholder="请选择工作时间" v-model="formData.teachingTimeRange" />
        <van-field class="adress" label="授课地址">
          <template #input>
            <van-checkbox-group v-model="formData.trainPlace" direction="horizontal">
              <van-checkbox class="checkbox" name="1" shape="square" checked-color="#FF9B26">
                上门服务
              </van-checkbox>
              <van-checkbox class="checkbox" name="2" shape="square" checked-color="#FF9B26">
                教练指定
              </van-checkbox>
              <van-checkbox class="checkbox" name="3" shape="square" checked-color="#FF9B26">
                双方协商
              </van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
        <van-field required label="收费设置" />
        <div class="add-charge" @click="addCharge">+ 去添加</div>
        <div class="class-box" v-if="chargeList.length > 0">
          <div class="class-box-item mb10" v-for="(item, index) in chargeList" :key="index">
            <div class="header flex">
              <div class="header-l flex">
                <img
                  v-if="Array.isArray(item.feesImageList) && item.feesImageList[0]?.content"
                  :src="item.feesImageList[0]?.content || ''"
                  v-error-img
                  alt=""
                />
                <img v-else-if="item.feesImage" :src="ossURLJoin(item.feesImage)" alt="" />
                <h3>{{ classType(item.feesType) }}</h3>
              </div>
              <div class="header-r">
                <p class="real-price">￥{{ item.feesMoney.toFixed(2) }}/课时</p>
                <span v-if="item.feesVirtualMoney" class="s-price"
                  >￥{{ item.feesVirtualMoney.toFixed(2) }}/课时</span
                >
              </div>
            </div>
            <div class="footer">
              <div v-for="(cItem, index) in item.feesClassHour" :key="index" class="class-item">
                <span class="classTime">{{ cItem }}个课时</span>
                <span class="s-price">
                  <template v-if="item.feesVirtualMoney">
                    ￥{{ (cItem * item.feesVirtualMoney).toFixed(2) }}
                  </template>
                </span>
                <span class="real-price">￥{{ (cItem * item.feesMoney).toFixed(2) }}</span>
              </div>
            </div>
            <div class="update-btn flex">
              <span @click="delectCharge(item, index)">删除</span>
              <span @click="updateCharge(item)">修改</span>
            </div>
          </div>
        </div>
        <!-- 技能选择器 -->
        <skill-type
          :selectedDefault="formatData.selectedSkillType"
          v-model:show="skillPickerShow"
          radio
          @confirm="handleSkillConfirm"
        />
      </van-form>
      <SetCharge
        v-model:show="showCharge"
        v-model:isEdit="isEdit"
        :classData="selectClassObj"
        :teachSelectList="teachWayList"
        @getClassData="getClassData"
      />
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { Dialog } from 'vant'
  import SkillType from '@/components/skill-type'
  import SetCharge from './setCharge.vue'
  import Schema from 'async-validator'
  import { updateCoachTeachingInfo } from '@/api/coach-server'
  import WeekPicker from '@/components/form/week-picker'
  import HoursPicker from '@/components/form/hours-picker'
  import { Toast } from 'vant'
  import { useRoute } from 'vue-router'
  import { ossURLJoin } from '@/common'
  // import { ossURLCompress } from "@/common";

  const route = useRoute()
  const isCoachIdentity = route.query.isCoachIdentity // 是否已经是教练身份

  const validator = new Schema({
    levelIds: {
      message: '请选择授课科目',
      validator: function (rule, value) {
        return value.length !== 0
      },
    },
  })
  const formData = reactive({
    levelIds: [], //授课类型
    teachingWay: '', // 授课方式
    teachingWeek: [], // 教学工作日
    teachingTimeRange: [], // 教学时间范围
    trainPlace: [], // 培训场所
    forTheCrowd: [], // 使用人群
    coachFeesSetVOList: [], //课时收费
    feesMoney: null, // 课时价格
    coachId: null, // 教练id
  })
  const formatData = reactive({
    address: '', // 省/市/区名称拼接
    lastAreaCode: '',
    skillType: '',
    selectedSkillType: {},
    isUpdateTeachTitle: true, //是否能更新教练标题字段
    isShowTitle: true,
  })

  const classType = (item) => {
    const obj = {
      1: '私教1对1',
      2: '小班1对2',
      3: '小班1对4',
      4: '亲子班',
    }
    return obj[item]
  }
  const skillPickerShow = ref(false)
  // #授课类型选择
  const handleSkillConfirm = ({ selected }) => {
    let types = selected[0] || []
    formData.levelIds = [] // 重置
    types.forEach((obj) => {
      formData.levelIds.push(obj.id)
    })
    formatData.skillType = types?.[2]?.name
    skillPickerShow.value = false
  }

  const handleFormData = () => {
    let data = JSON.parse(JSON.stringify(formData))
    formData.feesImageList = []
    data.teachingWeek = data.teachingWeek.join(',')
    data.coachFeesSetVOList = JSON.parse(JSON.stringify(chargeList.value))
    data.coachVideos = data.coachVideos?.[0]?.path
    data.trainPlace = data.trainPlace.join('')
    data.forTheCrowd = data.forTheCrowd.join(',')

    return data
  }

  const delectCharge = (item, index) => {
    Dialog.confirm({
      message: `确定要删除${classType(item.feesType)}的收费吗？`,
    })
      .then(() => {
        chargeList.value.splice(index, 1)
        teachWayList.value.splice(index, 1)
      })
      .catch(() => {})
  }

  const isEdit = ref(false)
  const updateCharge = (item) => {
    console.log(item, 'updateCharge')
    console.log(chargeList.value, 'chargeList')
    selectClassObj.value = item
    isEdit.value = true
    showCharge.value = true
  }

  const teachWayList = ref([]) // 已选中授课方式
  const chargeList = ref([]) //已选中表单数据
  const selectClassObj = ref({}) //已选中课时当前对象
  const getClassData = (data) => {
    console.log('选中授课方式data', data)
    // 新增
    if (teachWayList.value.length === 0 || !teachWayList.value.includes(data.feesType)) {
      teachWayList.value.push(data.feesType)
      chargeList.value.push(data)
      selectClassObj.value = data
      console.log(chargeList.value, 'chargeList.value1111111111')
    }
    // 修改、替换
    if (teachWayList.value.includes(data.feesType)) {
      chargeList.value = chargeList.value.map((item) => {
        if (item.feesType === data.feesType) {
          return (item = data)
        } else {
          return item
        }
      })
    }
    // console.log(chargeList.value, "修改后的单个表单chargeList");
  }
  const submit = (callback) => {
    let formData = handleFormData()
    validator
      .validate(formData)
      .then(() => {
        updateCoachTeachingInfo(formData, isCoachIdentity)
          .then((res) => {
            callback(true, res)
          })
          .catch((error) => {
            callback(false, error)
          })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }
  const showCharge = ref(false)
  const addCharge = () => {
    if (teachWayList.value.length === 4) {
      Toast('已添加全部授课方式')
      return
    }
    // console.log("showCharge.value:" + showCharge.value);
    showCharge.value = true
  }

  defineExpose({
    formData,
    formatData,
    chargeList,
    teachWayList,
    submit,
  })
</script>

<style scoped lang="scss">
  .flex {
    display: flex;
    align-items: center;
  }
  .mb10 {
    margin-bottom: 0.1rem;
  }

  .coach-teach-subject {
    padding: 0 0.2rem 0.18rem;
  }

  .title-box {
    border-bottom: 1px solid #eee;
    padding: 0 0.1rem;
  }

  .form-tip {
    padding-left: 0.12rem;
    font-size: 0.12rem;
    margin-bottom: 0.12rem;
  }

  .red {
    color: #ff6445;
  }

  .green {
    color: #0abb08;
  }

  .green-require {
    .van-cell__title {
      padding-left: 0.1rem;
      span {
        padding-left: 0.1rem;
      }
    }
    // :deep(.van-field__label),
    // :deep(.van-cell__title) {
    //   &::before {
    //     margin-right: 2px;
    //     color: #0abb08;
    //     content: "*";
    //   }
    // }
  }

  .item-title {
    position: relative;
    padding: 0.12rem 0.12rem 0.06rem 0.12rem;
    font-size: 0.14rem;
    font-weight: 600;
    color: #616568;
    &::before,
    &::after {
      position: absolute;
      content: '';
      display: block;
    }
    //border-bottom: 1px solid #eee;
    // &::after {
    //   bottom: 0;
    //   left: -3%;
    //   width: 105.5%;
    //   height: 0.01rem;
    //   background-color: #eee;
    // }
    &::before {
      left: 0;
      top: 50%;
      transform: translate(0, -60%);
      width: 0.03rem;
      height: 0.14rem;
      background-color: #ff9b26;
      border-radius: 0.03rem;
    }
  }
  .checkbox {
    margin-bottom: 0.1rem;
    width: 2rem;
    flex-shrink: 0;
    &:first-child {
      margin-top: 0.05rem;
    }
  }
  .unit {
    color: #453938;
    // span {
    //   font-size: 0.15rem;
    // }
  }
  :deep(.van-cell) {
    padding-left: 0;
    padding-right: 0;
    // border-bottom: 0.01rem solid #eee;
  }
  // .teach-data :deep(.van-cell),
  .i-select :deep(.van-cell) {
    border-bottom: 0.01rem solid #f2f2f2;
  }
  .teach-data {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      display: block;
      left: 28%;
      bottom: 0;
      width: 2.25rem;
      height: 0.01rem;
      z-index: 9;
      background-color: #f5f5f7;
    }
    :deep(.van-cell:after) {
      border-bottom: none;
    }
    // border-bottom: 0.01rem solid #f2f2f2;
  }
  // .adress {
  //   border-top: 0.01rem solid #f2f2f2;
  // }
  :deep(.van-field__label) {
    color: #453938;
    font-size: 0.14rem;
  }
  :deep(.van-field__control) {
    font-size: 0.14rem;
  }
  :deep(.van-cell) {
    padding: 0.17rem 0;
  }
  .add-charge {
    padding-left: 0.23rem;
    display: flex;
    align-items: center;
    height: 0.41rem;
    background: #f8f8f8;
    border-radius: 0.07rem;
    font-size: 0.14rem;
    color: rgba(0, 0, 0, 0.65);
    border: 0.01rem dashed #dddddd;
  }

  .class-box {
    margin-top: 0.1rem;

    .class-box-item {
      background: #f7f7f7;
      border-radius: 0.06rem;
      overflow: hidden;
    }
    .s-price {
      text-decoration: line-through;
      color: #b2b1b7;
    }
    .real-price {
      color: #ff6445;
    }
    .header {
      padding: 0.1rem 0.12rem;
      border-bottom: 0.01rem solid #eee;
      justify-content: space-between;
      .header-l {
        img {
          object-fit: cover;
          margin-right: 0.1rem;
          width: 0.4rem;
          height: 0.4rem;
          border: 0.01rem solid #e5e5e5;
        }
        h3 {
          font-size: 0.16rem;
          font-weight: 600;
          color: #1a1b1d;
        }
      }
      .header-r {
        text-align: right;
        p {
          font-weight: 600;
          font-size: 0.14rem;
        }
        span {
          font-size: 0.12rem;
        }
      }
    }
    .footer {
      padding: 0.1rem 0.12rem 0.05rem;
      .class-item {
        margin-bottom: 0.05rem;
        padding: 0.1rem 0.12rem;
        border-radius: 0.04rem;
        background-color: #fff;
        display: flex;
        // justify-content: space-between;
        span {
          flex: 1;
          text-align: left;
          font-size: 0.14rem;
        }
        .classTime {
          color: #494949;
        }
      }
    }
    .update-btn {
      position: relative;
      padding: 0.1rem 0;
      span {
        flex: 1;
        text-align: center;
        font-size: 0.14rem;
        color: #616568;
      }
      &::before {
        content: '';
        position: absolute;
        display: block;
        width: 0.01rem;
        height: 0.14rem;
        background: #ebebeb;
        left: 50%;
      }
    }
  }
  :deep(.i-action-sheet) {
    min-height: 88%;
  }
</style>
