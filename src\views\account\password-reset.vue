<template>
  <div class="page p25">
    <div class="logo-box">
      <img class="logo" src="../../assets/images/logo.png" alt="logo" />
    </div>

    <Form class="form">
      <Field v-model="formData.mobile" maxlength="11" placeholder="请输入手机号" @blur="mobileBlur">
        <template #label>
          <i class="icon icon-phone" />
        </template>
      </Field>
      <ErrorTip v-if="isShowPhoneTip" :tipTxt="mobileTxt" />
      <Field v-model="formData.imageCode" label="图形验证码" placeholder="请输入图形验证码">
        <template #label>
          <i class="icon icon-shield" />
        </template>
        <template #button>
          <ImageCaptcha class="image-Captcha" />
        </template>
      </Field>
      <Field v-model="formData.verifyCode" label="验证码" placeholder="请输入验证码">
        <template #label>
          <i class="icon icon-captcha" />
        </template>
        <template #button>
          <span v-if="!isSendSMSCode" class="field-right" @click="getVerifyCode">获取验证码</span>
          <CountDown
            class="count-down"
            v-else
            :time="time"
            format="ss 秒"
            @finish="handleCountDownFinish"
          />
        </template>
      </Field>
      <Field
        v-model="formData.password"
        type="password"
        placeholder="请设置密码，至少6个数字或字母"
      >
        <template #label>
          <i class="icon icon-password" />
        </template>
      </Field>
      <Field v-model="formData.confirmPassword" type="password" placeholder="请再次确定密码">
        <template #label>
          <i class="icon icon-password" />
        </template>
      </Field>
    </Form>
    <button class="submit-btn" @click="submit">提交</button>
    <div class="back-login" @click="$router.push('/login')">返回登录>></div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { Form, Field, CountDown, Toast } from 'vant'
  import ImageCaptcha from '@/components/image-captcha'
  import { resetPassword } from '@/api/user-server'
  import Schema from 'async-validator'
  import { useSendResetPasswordSMSCode } from '@/use/useSendVerifyCode'
  import { checkEmpty, checkPassword, checkPhone, validate } from '@/utils/validate'
  import { verifyUserInfoByMobile } from '@/api/user-server'
  import ErrorTip from '../account/components/ErrorTip.vue'

  const isSendSMSCode = ref(false)

  const router = useRouter()

  const formData = reactive({
    mobile: '',
    password: '',
    verifyCode: '',
    imageCode: '',
    confirmPassword: '',
  })

  let validator = new Schema({
    mobile: { message: '请输入手机号', validator: checkPhone },
    imageCode: { message: '请输入图形验证码', validator: checkEmpty },
    verifyCode: { message: '请输入短信验证码', validator: checkEmpty },
    password: { message: '请输入密码', validator: checkPassword },
    confirmPassword: {
      message: '',
      validator: function (rule, value) {
        if (value !== formData.password) {
          this.message = '两次输入的密码不一致'
          return false
        }
        return true
      },
    },
  })

  const time = ref(60 * 1000)

  // 校验手机号是否已注册
  // watch(
  //   () => formData.mobile,
  //   (newVal) => {
  //     if (newVal && newVal.length === 11) {
  //       verifyUserInfoByMobile({ mobile: formData.mobile }).then((res) => {
  //         console.log(res);
  //         isShowPhoneTip.value = res.data ? false : true;
  //       });
  //     }
  //   }
  // );

  const mobileTxt = ref(null)
  const isShowPhoneTip = ref(false)
  const mobileBlur = () => {
    if (!validate('mobile', formData.mobile) && formData.mobile) {
      mobileTxt.value = '手机号码不正确'
      isShowPhoneTip.value = true
    } else {
      isShowPhoneTip.value = false
    }
    if (formData.mobile && formData.mobile.length === 11) {
      verifyUserInfoByMobile({ mobile: formData.mobile }).then((res) => {
        isShowPhoneTip.value = res.data ? false : true
        if (res.data) {
          mobileTxt.value = '手机号码不存在'
        }
        // isShowPhoneTip.value = res.data ? true : false;
      })
    }
  }
  // 倒计时结束后
  const handleCountDownFinish = () => {
    isSendSMSCode.value = false
  }

  // 获取验证码
  const getVerifyCode = () => {
    useSendResetPasswordSMSCode(formData, () => {
      isSendSMSCode.value = true
    })
  }

  const submit = () => {
    validator
      .validate(formData)
      .then(() => {
        let params = {
          mobile: formData.mobile,
          password: formData.password,
          verifyCode: formData.verifyCode,
        }
        resetPassword(params).then(() => {
          Toast('密码重置成功')
          router.push({
            path: '/account/password-reset-success',
          })
        })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';

  @include Icon('phone', 0.24rem, 0.24rem);
  @include Icon('password', 0.24rem, 0.24rem);
  @include Icon('captcha', 0.24rem, 0.24rem);
  @include Icon('shield', 0.24rem, 0.24rem);
  @include Icon('error-tip', 0.13rem, 0.13rem);
  @include Button('submit-btn', 3.25rem) {
    margin-top: 0.42rem;
  }

  .p25 {
    padding: 0 0.25rem;
  }

  .logo-box {
    text-align: center;
    padding: 0.4rem 0 0.56rem 0;

    .logo {
      width: 0.76rem;
      height: 0.9rem;
    }
  }
  .error {
    display: flex;
    align-items: center;
    padding-top: 0.02rem;
    span {
      margin-left: 0.05rem;
      font-size: 0.12rem;
      color: #ff6445;
    }
  }

  .form {
    :deep(.van-cell) {
      padding: 0.17rem 0 0.17rem 0;
      font-size: 0.16rem;
      border-bottom: 1px solid #eeeeee;
      align-items: center;

      &:after {
        border: none;
      }
    }

    :deep(.van-field__label) {
      width: 0.4rem;
      padding-left: 0.1rem;
      color: #453938;

      .icon {
        vertical-align: top;
      }
    }
  }

  .back-login {
    margin-top: 0.12rem;
    text-align: center;
    font-size: 0.12rem;
    color: #2c71a5;
  }
</style>
