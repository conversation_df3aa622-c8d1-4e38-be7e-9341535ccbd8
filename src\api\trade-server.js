// # 上课服务

import http from '@/utils/axios'

// 获取教练列表
export const getMyCoachList = (params) => {
  return http.get('/trade-server/api/member/study/my-coach-list', { params })
}

// 用户端拒绝核销
export const refuseConsume = (params) => {
  return http.post('/trade-server/api/member/classes-consume/refuse', params)
}

// 用户端同意核销
export const agreeConsume = (params) => {
  return http.post('/trade-server/api/member/classes-consume/agree', params)
}

// 获取教练详情
export const getMyCoachDetail = (params) => {
  return http.get('/trade-server/api/member/study/my-coach', { params })
}

// 获取核销详情列表
export const getClassesConsumeList = (params) => {
  return http.get('/trade-server/api/member/classes-consume/list', { params })
}

// 历史订单
export const getBuyHistory = (params) => {
  return http.get('/trade-server/api/member/study/buy-history', { params })
}
// 用户端-订单列表
export const getOrderList = (params) => {
  return http.get('/trade-server/api/member/order/list', { params })
}

// 用户端-订单列表
export const getStuOrderDetail = (params) => {
  return http.get('/trade-server/api/member/order/detail', { params })
}

// 用户端-售后列表
export const getAfterSaleList = (params) => {
  return http.get('/trade-server/api/member/after-sale/list', { params })
}

// 用户端-售后明细
export const getAfterSaleDetail = (params) => {
  return http.get('/trade-server/api/member/after-sale/detail', { params })
}

// 用户端售后申请预览
export const getStuApplyPreview = (params) => {
  return http.get('/trade-server/api/member/after-sale/apply-preview', { params })
}

// 用户端-售后申请
export const getStuAfterSaleApply = (params) => {
  return http.post('/trade-server/api/member/after-sale/apply', params)
}
// 用户端-取消申请售后
export const getStuAfterSaleCancel = (params) => {
  return http.post('/trade-server/api/member/after-sale/cancel', params)
}

// 用户端-退款原因列表
export const getStuRefundReasons = () => {
  return http.get('/trade-server/api/member/after-sale/refund-reasons/nL')
}

// 获取用户是否购买过教练的课程商品
export const reqUserIsBuyCoachGoods = (params) => {
  return http.get('/trade-server/api/member/study/contact-coach', { params })
}

// 是否可以重新申请
export const checkCanApply = (params) => {
  return http.get('/trade-server/api/member/after-sale/check-can-apply', { params })
}
