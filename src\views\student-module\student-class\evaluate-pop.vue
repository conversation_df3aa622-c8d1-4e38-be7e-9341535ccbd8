<template>
  <ijl-action-sheet
    :title="isEdit ? '课后评价' : '查看评价'"
    v-model:show="showPop"
    :showConfirmButton="isEdit"
    :showCancelButton="isEdit"
    @open="open"
    @confirm="submit"
    @cancel="onClose"
    @close="onClose"
  >
    <div class="evaluate-box">
      <div v-if="!isEdit" class="label">教练评分</div>
      <div class="score-box">
        <van-rate
          :readonly="!isEdit"
          v-model="formData.score"
          void-color="#eee"
          void-icon="star"
          color="#ffd21e"
          size="0.28rem"
        />
        <span>{{ rateLabel }}</span>
      </div>
      <div v-if="isEdit" class="class-late">
        <div class="label">课后评价</div>
        <van-checkbox-group v-model="formData.tags" direction="horizontal">
          <van-checkbox v-for="item in tagList" :key="item.tagName" :name="item.tagName">
            <template #icon="props">
              <div class="tag-check-item">
                <span :class="{ 'tag-check-checked': props.checked }">{{ item.tagName }}</span>
              </div>
            </template>
          </van-checkbox>
        </van-checkbox-group>
      </div>
      <van-field
        :readonly="!isEdit"
        v-model="formData.content"
        rows="5"
        type="textarea"
        maxlength="100"
        placeholder="说说你的上课感受…"
        show-word-limit
      />
      <div v-if="isEdit">
        <upload-file
          class="upload-file"
          preview-size="0.73rem"
          file-type="jpg|png|gif|jpeg"
          :max-size="10240 * 1024"
          v-model="formData.picturesUrls"
          :max-count="9"
        />
        <van-checkbox
          class="anonymous"
          v-model="formData.anonymousState"
          shape="square"
          checked-color="#ff9b26"
          icon-size="0.16rem"
        >
          匿名评价
        </van-checkbox>
      </div>
      <div v-if="!isEdit">
        <div class="image-box">
          <image-preview-wrapper multiple>
            <ijl-image
              v-for="(url, index) in formData.picturesUrls"
              :src="url"
              :key="index"
              class="item"
              width="0.72rem"
              height="0.72rem"
              fit="cover"
            />
          </image-preview-wrapper>
        </div>
        <div class="evaluate-time">{{ comment?.createTime }}</div>
      </div>
    </div>
  </ijl-action-sheet>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import UploadFile from '@/components/upload-file'
  import { reqGetCommentDetails, reqUserSubmitComment } from '@/api/user-server'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import Schema from 'async-validator'
  import { Toast } from 'vant'

  const emit = defineEmits(['close', 'update:show', 'refresh'])

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default() {
        return {}
      },
    },
  })

  const showPop = ref(props.show)
  const comment = ref(null)

  watch(
    () => props.show,
    (newVal) => (showPop.value = newVal),
    { immediate: true },
  )

  const initFormData = () => {
    return {
      type: 'CLASSES_CONSUME',
      score: 4, // 默认四星
      content: '',
      picturesUrls: [],
      anonymousState: false,
      tags: [],
    }
  }

  const formData = ref(initFormData())

  let formValidator = new Schema({
    tags: { type: 'array', required: true, message: '请选择课后评价标签' },
  })

  const isEdit = computed(() => {
    if (!props.rowData) return false
    return props.rowData.evaluateId === '0'
  })

  const rateLabel = computed(() => {
    const obj = {
      1: '非常差',
      2: '差',
      3: '一般',
      4: '满意',
      5: '非常满意',
    }
    return obj[formData.value.score]
  })

  const tagList = ref([
    { tagName: '专业' },
    { tagName: '有耐心' },
    { tagName: '容易上手' },
    { tagName: '收获很大' },
  ])

  const reset = () => {
    formData.value = initFormData()
  }

  const open = () => {
    if (!props.rowData) return

    reset()

    if (props.rowData.evaluateId !== '0') {
      let params = { commentId: props.rowData.evaluateId }
      reqGetCommentDetails(params).then((res) => {
        const { data } = res
        comment.value = data
        formData.value.score = data.score
        formData.value.content = data.content
        if (Array.isArray(data.pictureList)) {
          formData.value.picturesUrls = data.pictureList
        }
      })
    }
  }

  const submit = () => {
    let params = JSON.parse(JSON.stringify(formData.value))

    formValidator
      .validate(params)
      .then(() => {
        params.coachId = props.rowData.coachId
        params.mappingId = props.rowData.id
        params.content = params.tags.join('，') + '，' + params.content
        params.picturesUrls = params.picturesUrls
          .map((file) => file.path)
          .filter(Boolean)
          .join(',')

        reqUserSubmitComment(params).then(() => {
          Toast('已发布评价')
          emit('refresh')
          onClose()
        })
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  const onClose = () => {
    showPop.value = false
    emit('update:show', false)
  }
</script>

<style scoped lang="scss">
  .evaluate-box {
    padding: 0 0.15rem;

    .label {
      margin-bottom: 0.05rem;
      font-size: 0.16rem;
      font-weight: 600;
      color: #1f1f1f;
    }

    .score-box {
      margin-bottom: 0.15rem;
      display: flex;
      align-items: center;

      span {
        font-size: 0.12rem;
        color: #1f1f1f;
        margin-left: 0.1rem;
      }
    }
  }

  .class-late {
    margin-bottom: 0.08rem;

    :deep(.van-checkbox) {
      overflow: initial;
    }

    :deep(.van-checkbox__icon) {
      height: auto;
      line-height: initial;
    }

    .tag-check-item {
      span {
        padding: 0.04rem 0.08rem;
        font-size: 0.13rem;
        background: #f3f3f3;
        border-radius: 0.04rem;
        border: 0.01rem dashed #dddddd;
        display: block;
      }

      .tag-check-checked {
        color: #ff9b26;
        background-color: #fff6e9;
        border: 0.01rem dashed #ff9b26;
      }
    }
  }

  :deep(.van-cell) {
    padding: 0.1rem;
    background: #fafafa;
    border-radius: 0.06rem;
  }

  :deep(.van-field__control) {
    overflow: hidden;
  }

  :deep(.van-field__word-limit) {
    color: #b2b1b7;
  }

  .upload-file {
    margin-top: 0.08rem;
  }

  :deep(.van-uploader__wrapper) {
    flex-shrink: 0;
  }

  :deep(.van-uploader__preview) {
    margin-right: 0.1rem;
  }

  :deep(.van-checkbox__label) {
    font-size: 0.14rem;
    color: #616568;
  }

  .anonymous {
    margin-top: 0.02rem;
  }

  .image-box {
    margin-top: 0.08rem;
    padding: 0 0.1rem;

    .item {
      margin-bottom: 0.1rem;

      &:not(:nth-child(4n + 0)) {
        margin-right: 0.1rem;
      }
    }

    img {
      object-fit: cover;
      margin-right: 0.1rem;
      margin-bottom: 0.1rem;
      width: 0.73rem;
      height: 0.73rem;
      background-color: #eee;

      &:nth-child(4n) {
        margin-right: 0;
      }
    }
  }

  .evaluate-time {
    margin: 0.1rem 0 0 0.1rem;
    font-size: 0.14rem;
    color: #b2b1b7;
  }
</style>
