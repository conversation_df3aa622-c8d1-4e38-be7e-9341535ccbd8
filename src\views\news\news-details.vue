<template>
  <page :title="pageTitle">
    <template #page>
      <div class="page-content page-bg-white">
        <div v-if="pageShow" class="box">
          <div v-if="isUserPost && !article.publish" class="fixed-top">
            <post-state />
          </div>
          <div class="wrapper">
            <div class="title">{{ article.title }}</div>
            <div class="author-info">
              <div class="info-l">
                <span class="author" @click="toAuthorHome(article.mappingId, article.identityType)">
                  {{ article.realname }}
                </span>
                <span> {{ sliceStr(article.releaseTime, 0, 10) }}</span>
              </div>
              <div class="info-r">
                <span v-if="article.hits < 999">{{ article.hits }}浏览</span>
                <span v-else class="num">999+浏览</span>
              </div>
            </div>
            <image-preview-wrapper>
              <div class="content ql-editor" v-html="article.content" />
            </image-preview-wrapper>
            <post-manage v-if="isUserPost" @select="onSelect" />
          </div>
        </div>
        <details-empty v-if="emptyShow" />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import PostManage from '@/components/post/post-manage'
  import PostState from '@/components/post/post-state'
  import { getArticleDetails, delUserArticle } from '@/api/generic-server'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { getIsUserPost, ossURLJoin, toAuthorHome } from '@/common'
  import { Dialog, Toast } from 'vant'
  import useKeepAliveStore from '@/store/keepAlive'
  import DetailsEmpty from '@/views/common/components/details-empty'
  import { sliceStr } from '@/utils'

  const route = useRoute()
  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const article = ref(null)
  const articleId = route.params.articleId
  const pageTitle = ref('')

  const initialize = async () => {
    let { data } = await getArticleDetails({ id: articleId })
    pageTitle.value = data.title
    document.title = data.title + ',爱教练私教网'
    document
      .querySelector('meta[name="keywords"]')
      .setAttribute('content', `${data.keywordsList ? data.keywordsList[0] : ''},爱教练私教网`)
    document
      .querySelector('meta[name="description"]')
      .setAttribute('content', `${data.description},爱教练私教网`)

    article.value = data

    let desc = ''
    if (typeof data.description === 'string') {
      desc = data.description.replace(/(\r\n|\n|\t)/g, '')
    }

    setWxShare({
      title: '【爱教练】' + data.title,
      desc: desc,
      link: baseURL + '/news-details/' + articleId,
      imgUrl: data.imageList?.[0] ? ossURLJoin(data.imageList[0]) : ossURL + '/h5-assets/logo.png',
    })
  }

  initialize()

  const isUserPost = computed(() => {
    if (!article.value) return false
    return getIsUserPost(article.value.userId) && article.value.identityType === 'coach'
  })

  const pageShow = computed(() => {
    if (!article.value) return false

    return !!(article.value.publish || isUserPost.value)
  })

  const emptyShow = computed(() => {
    if (!article.value) return false
    return !article.value.publish && !isUserPost.value
  })

  const onEdit = () => {
    router.replace({
      name: 'coachWorktablePublishArticle',
      query: {
        articleId: articleId,
      },
    })
  }

  const onRemove = () => {
    Dialog.confirm({
      message: '你确定要删除这篇文章吗？',
    })
      .then(() => {
        delUserArticle(articleId).then(() => {
          keepAliveStore.removeKeepAlive('coachWorktableLearningPosts')
          Toast.success('删除成功')
          router.replace({
            name: 'coachWorktableLearningPosts',
            query: {
              tabIndex: '1',
            },
          })
          router.go(-1)
        })
      })
      .catch(() => {})
  }

  const onSelect = (item) => {
    if (item.code === 'edit') {
      onEdit()
    }

    if (item.code === 'delete') {
      onRemove()
    }
  }
</script>
<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: text-top;
    margin-right: 0.06rem;
  }

  .wrapper {
    padding: 0.15rem;
    background-color: #fff;
  }

  .title {
    font-size: 0.17rem;
    font-weight: 600;
    margin-bottom: 0.1rem;
    @include TextEllipsis(2);
  }

  .author-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.12rem;
    color: #b2b1b7;
    line-height: 0.17rem;
    padding-bottom: 0.1rem;
    margin-bottom: 0.1rem;
    border-bottom: 0.01rem solid #eeeeee;

    .author {
      margin-right: 0.1rem;
      color: var(--i-primary);
    }
  }

  :deep(.content) {
    font-size: 0.16rem;
    padding-bottom: 0.5rem;
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }

  .ql-editor {
    padding: 0;
  }

  .fixed-top {
    .post-state {
      margin: 0;
      line-height: 0.3rem;
      padding-left: 0.15rem;
      background: #f7f7f7;
    }
  }
</style>
