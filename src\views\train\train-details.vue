<template>
  <page :title="pageTitle">
    <template #page>
      <div class="page-content page-bg-white">
        <div v-if="pageShow" class="box">
          <div v-if="isUserPost && !train.publish" class="fixed-top">
            <post-state />
          </div>
          <div class="wrapper">
            <div class="title">{{ train.title }}</div>
            <div class="author-info">
              <div class="info-l">
                <span class="author" @click="toAuthorHome(train.mappingId, train.identityType)">
                  {{ train.realName }}
                </span>
                <span> {{ sliceStr(train.releaseTime, 0, 10) }}</span>
              </div>
              <div class="info-r">
                <span>{{ train.readCount }}浏览</span>
              </div>
            </div>
            <image-preview-wrapper>
              <p>培训科目：{{ train.thirdlyCategoriesName }}</p>
              <p>开始时间：{{ formatTiem(train.trainTime) }}</p>
              <p>培训地点：{{ getArea(train) }}</p>
              <div class="split"></div>
              <div class="content ql-editor" v-html="train.content" />
            </image-preview-wrapper>
            <post-manage v-if="isUserPost" @select="onSelect" />
          </div>
        </div>
        <details-empty v-if="emptyShow" />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import PostManage from '@/components/post/post-manage'
  import PostState from '@/components/post/post-state'
  import { delUserTrains, getTrainsDetail } from '@/api/generic-server'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { getIsUserPost, toAuthorHome } from '@/common'
  import { Dialog, Toast } from 'vant'
  import useKeepAliveStore from '@/store/keepAlive'
  import DetailsEmpty from '@/views/common/components/details-empty'
  import { sliceStr } from '@/utils'

  const route = useRoute()
  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const train = ref(null)
  const trainId = route.params.trainId
  const pageTitle = ref('')

  const initialize = async () => {
    let { data } = await getTrainsDetail({ trainsId: trainId })
    pageTitle.value = data.title
    document.title = data.title + ',爱教练私教网'
    document
      .querySelector('meta[name="keywords"]')
      .setAttribute(
        'content',
        `${data.title},${data.location}${data.thirdlyCategoriesName}培训,爱教练私教网`,
      )
    document
      .querySelector('meta[name="description"]')
      .setAttribute(
        'content',
        `${data.title},${data.location}${data.thirdlyCategoriesName}培训,爱教练私教网囊括网球,羽毛球,乒乓球,游泳,武术,瑜伽,高尔夫,音乐,舞蹈,模特,艺术等各类培训信息`,
      )

    train.value = data

    setWxShare({
      title: '【爱教练】' + data.title,
      desc: data.description,
      link: baseURL + '/train-details/' + trainId,
      imgUrl: ossURL + '/h5-assets/logo.png',
    })
  }

  initialize()

  const onEdit = () => {
    router.replace({
      name: 'coachWorktablePublishRecruit',
      query: {
        trainsId: trainId,
      },
    })
  }

  const onRemove = () => {
    Dialog.confirm({
      message: '你确定要删除这条培训信息吗？',
    })
      .then(() => {
        delUserTrains(trainId).then(() => {
          // 有工作台列表页缓存则删除缓存
          keepAliveStore.removeKeepAlive('coachWorktableRecruitPosts')
          Toast.success('删除成功')
          router.replace({
            name: 'coachWorktableRecruitPosts',
          })
          router.go(-1)
        })
      })
      .catch(() => {
        // on cancel
      })
  }

  const onSelect = (item) => {
    if (item.code === 'edit') {
      onEdit()
    }

    if (item.code === 'delete') {
      onRemove()
    }
  }

  const formatTiem = (time) => {
    if (typeof time === 'string') {
      return time.slice(0, 10)
    } else {
      return '长期有效'
    }
  }

  // 获取地区名称
  const getArea = (item) => {
    let str = ''
    if (item.cityName) {
      str += item.cityName
    }

    if (item.countyName) {
      str += item.countyName
    }
    return str
  }

  const isUserPost = computed(() => {
    if (!train.value) return false
    return getIsUserPost(train.value.userId) && train.value.identityType === 'coach'
  })

  const pageShow = computed(() => {
    if (!train.value) return false
    return !!(train.value.publish || isUserPost.value)
  })

  const emptyShow = computed(() => {
    if (!train.value) return false
    return !train.value.publish && !isUserPost.value
  })
</script>
<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: text-top;
    margin-right: 0.06rem;
  }

  .wrapper {
    padding: 0.15rem;
    background-color: #fff;
  }

  .title {
    font-size: 0.17rem;
    font-weight: 600;
    margin-bottom: 0.08rem;
    @include TextEllipsis(2);
  }

  .author-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.12rem;
    color: #b2b1b7;
    line-height: 0.17rem;
    padding-bottom: 0.06rem;

    .author {
      margin-right: 0.1rem;
      color: var(--i-primary);
    }
  }
  .split {
    margin: 0.1rem 0;
    border-bottom: 0.01rem solid #e3e3e3;
  }

  :deep(.content) {
    font-size: 0.16rem;
    padding-bottom: 0.5rem;
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }

  .ql-editor {
    padding: 0;
  }

  .fixed-top {
    .post-state {
      margin: 0;
      line-height: 0.3rem;
      padding-left: 0.15rem;
      background: #f7f7f7;
    }
  }
</style>
