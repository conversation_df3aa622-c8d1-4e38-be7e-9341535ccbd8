<template>
  <div class="swipe-wrapper">
    <van-swipe class="swipe" lazy-render :autoplay="3000" indicator-color="#FF9B26FF">
      <van-swipe-item v-for="item in bannerList" :key="item.path" @click="onBannerClick(item)">
        <img :src="getOssURL(item.bannerUrl)" alt="banner" />
      </van-swipe-item>
    </van-swipe>

    <!-- 消息轮播 -->
    <notice-swipe class="notice" />
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import NoticeSwipe from './NoticeSwipe'
  import { getBanner } from '@/api/generic-server'
  import { getOssURL } from '@/common'

  const router = useRouter()
  const bannerList = ref([])

  // 获取banner列表
  const getBannerList = () => {
    getBanner({}).then((res) => {
      const { data } = res
      bannerList.value = data
    })
  }

  // banner点击
  const onBannerClick = (value) => {
    //jumpType: 1:教练  2:链接 3: 微信文章
    if (value.jumpType === '1') {
      router.push({ path: `/coach/details/${value.jumpUrl}` })
    } else {
      location.href = value.jumpUrl
    }
  }

  onMounted(() => {
    getBannerList()
  })
</script>

<style lang="scss" scoped>
  .swipe-wrapper {
    position: relative;
  }

  .swipe {
    .van-swipe-item {
      color: #fff;
      height: 2rem;
      text-align: center;
      background-color: #eeeeee;

      img {
        width: 100%;
        height: 100%;
      }
    }

    :deep(.van-swipe__indicator) {
      width: 0.11rem;
      height: 0.02rem;
      border-radius: 0;
    }
  }

  .notice {
    position: absolute;
    left: 0.1rem;
    top: 0.04rem;
  }
</style>
