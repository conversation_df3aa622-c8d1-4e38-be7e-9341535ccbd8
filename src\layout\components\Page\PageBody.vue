<template>
  <ijiaolian-page-wrapper>
    <ijiaolian-page-body>
      <!-- 邀约人信息 -->
      <page-inviter :class="{ 'mt-nav-height': navigationBarType === 'transparent' }" />
      <slot />
    </ijiaolian-page-body>
  </ijiaolian-page-wrapper>
</template>

<script setup>
  import PageInviter from '@/components/inviter-top'

  defineProps({
    navigationBarType: {
      type: String,
    },
  })
</script>

<style lang="scss">
  .app-showtabbar {
    ijiaolian-page-wrapper {
      height: calc(100% - 0.44rem - 0.6rem);
      height: calc(
        100% - 0.44rem - constant(safe-area-inset-top) - 0.6rem - constant(safe-area-inset-bottom)
      );
      height: calc(
        100% - 0.44rem - env(safe-area-inset-top) - 0.6rem - env(safe-area-inset-bottom)
      );
      display: block;
    }

    ijiaolian-page-body {
      padding-bottom: 0.5rem;
    }
  }

  .mt-nav-height {
    margin-top: var(--nav-bar-height);
  }

  ijiaolian-page-wrapper {
    width: 100%;
    height: 100%;
    display: block;
    height: calc(100% - 0.44rem);
    height: calc(100% - 0.44rem - constant(safe-area-inset-top));
    height: calc(100% - 0.44rem - env(safe-area-inset-top));
  }

  ijiaolian-page-body {
    position: relative;
    background-color: #f5f5f5 !important;
    background-color: #f8f7f7 !important;
    min-height: 100% !important;
    height: auto !important;
    display: block;
  }
</style>
