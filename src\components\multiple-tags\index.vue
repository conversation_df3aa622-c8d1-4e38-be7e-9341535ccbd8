<template>
  <!-- 多选标签 -->
  <div class="multiple-tags">
    <h3 v-if="title">{{ title }}</h3>
    <div class="check-box">
      <span
        :class="{ 'type-selected': item.isChecked }"
        v-for="(item, index) in list"
        :key="index"
        @click="select(index, item)"
      >
        {{ item.name }}</span
      >
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue'

  const emit = defineEmits(['selectChange'])
  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    isMoreSelect: {
      type: Boolean,
      default: true,
    },
    isReturnList: {
      type: Boolean,
      default: false,
    },
  })

  const tagList = ref([])
  const indexList = ref([])
  const lastIndex = ref(null)

  watch(
    () => props.list,
    (val) => {
      tagList.value = val
      // console.log(tagList.value, "tagList.value -- watch");
      // 单选 需要回显
      if (!props.isMoreSelect) {
        props.list.map((item, index) => {
          console.log(item.isChecked)
          if (item.isChecked === true) {
            lastIndex.value = index
          }
        })
      }
    },
    {
      deep: true,
      // immediate: true,
    },
  )
  onMounted(() => {
    tagList.value = props.list
  })

  const select = (index) => {
    if (props.isMoreSelect) {
      moreSelect(index)
    } else {
      singleSelect(index)
      lastIndex.value = index
    }
  }
  // 多选
  const moreSelect = (index) => {
    tagList.value[index].isChecked = !tagList.value[index].isChecked
    indexList.value = tagList.value.filter((val) => {
      return val.isChecked === true
    })
    // 返回选中list
    if (props.isReturnList) {
      emit('selectChange', indexList.value)
      return
    }
    // 返回选中id数组
    indexList.value = indexList.value.map((indexItem) => indexItem.id)
    emit('selectChange', indexList.value)
  }

  // 单选
  const singleSelect = (index) => {
    if (lastIndex.value || lastIndex.value === 0) {
      // 点击同个元素
      if (lastIndex.value === index) {
        tagList.value[index].isChecked = !tagList.value[index].isChecked
        // 清空已选中值
        if (!tagList.value[index].isChecked) {
          emit('selectChange', '')
          return
        }
      } else {
        // 点击不同元素
        tagList.value[lastIndex.value].isChecked = false
        tagList.value[index].isChecked = true
      }
    } else {
      // 首次点击(没有初始值)
      tagList.value = tagList.value.map((item) => {
        item['isChecked'] = false
        return item
      })
      tagList.value[index].isChecked = !tagList.value[index].isChecked
    }
    // 返回选中list
    if (props.isReturnList) {
      emit('selectChange', tagList.value[index])
      return
    }
    emit('selectChange', tagList.value[index].id)
  }
</script>

<style scoped lang="scss">
  h3 {
    margin: 0.16rem 0 0.08rem;
    font-size: 0.12rem;
    font-weight: 600;
    color: #1a1b1d;
    line-height: 0.17rem;
  }

  .check-box {
    span {
      display: inline-block;
      margin-right: 0.06rem;
      padding: 0.02rem 0rem;
      width: 0.8rem;
      text-align: center;
      background: #f8f7f7;
      border-radius: 0.02rem;
      border: 0.01rem solid #eeeeee;
      font-size: 0.14rem;
      color: #616568;
      line-height: 0.2rem;
    }
    .type-selected {
      color: #ff6445;
      background-color: #fff3f1;
      border: 0.01rem solid #ff6445;
    }
  }
</style>
