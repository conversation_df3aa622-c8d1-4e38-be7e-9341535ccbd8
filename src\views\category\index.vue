<template>
  <page navigationBarType="none">
    <template #page>
      <div class="content">
        <header class="header">
          <div class="header-search">
            <router-link to="/search">
              <div class="search-input">
                <van-icon name="search" size="0.18rem" />
                <div class="search-input-placeholder">搜索感兴趣的教练</div>
              </div>
            </router-link>
          </div>
          <div class="placeholder-box"></div>
        </header>
        <div class="category-wrap">
          <div class="category-tabs">
            <ul>
              <li
                v-for="(item, index) in sportsClassify"
                :key="item.name"
                ref="tabRef"
                class="category-tab__item"
                :class="tabClassName(index)"
              >
                <a :data-hash="getAnchorId(index)" class="outline-link">
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </div>
          <div class="category-content">
            <div class="category-box" v-for="(item, index) in sportsClassify" :key="item.id">
              <h4
                :data-anchor-id="getAnchorId(index)"
                class="category-title anchor"
                ref="categoryRef"
              >
                <span>{{ item.name }}</span>
                <van-icon size="0.12rem" name="arrow" @click="onMoreClick(item)" />
              </h4>
              <ul class="category-list">
                <li
                  v-for="subItem in item.subCategoriesListVOS"
                  :key="subItem.id"
                  class="category-item"
                >
                  <router-link :to="getNavLink(item, subItem)">
                    <ijl-image class="category-image" :src="getCategoryIcon(subItem)" fit="cover" />
                    <span>{{ subItem.name }}</span>
                  </router-link>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <feedback-notice-bar class="notice-bar" />
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'category' }
</script>

<script setup>
  import { nextTick, ref } from 'vue'
  import { useRouter, onBeforeRouteLeave } from 'vue-router'
  import { reqSportsClassify } from '@/api/generic-server'
  import FeedbackNoticeBar from '@/views/help/components/FeedbackNoticeBar'
  import { useActiveAnchor } from '@/views/category/outline'
  import useKeepAliveStore from '@/store/keepAlive'

  const router = useRouter()
  const tabIndex = ref(0)
  const tabRef = ref(null)
  const categoryRef = ref(null)
  const sportsClassify = ref([])
  const { bindTabClick } = useActiveAnchor()
  const keepAliveStore = useKeepAliveStore()

  const tabClassName = (index) => {
    return {
      'category-tab__item--activity': tabIndex.value === index,
    }
  }

  const getAnchorId = (index) => {
    return '#tab-' + index
  }

  const getSportsClassify = () => {
    reqSportsClassify().then((res) => {
      sportsClassify.value = res.data.filter((item) => item.subCategoriesListVOS)
      nextTick(() => {
        bindTabClick()
      })
    })
  }

  const getNavLink = (parent, child) => {
    let classifyIds = [parent.parentId, parent.id, child.id]
    return {
      name: 'searchResult',
      query: { classifyIds: classifyIds.join(',') },
    }
  }

  const onMoreClick = (item) => {
    let classifyIds = [item.parentId, item.id]
    router.push({
      name: 'searchResult',
      query: { classifyIds: classifyIds.join(',') },
    })
  }

  const getCategoryIcon = (item) => (item.picture ? item.picture : '/categories/default.jpg')

  const initialize = () => {
    getSportsClassify()
  }

  onBeforeRouteLeave((to) => {
    let pages = ['search', 'searchResult']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('category')
    }
  })

  initialize()
</script>

<style lang="scss" scoped>
  @import '@/styles/mixins/mixins.scss';
  .content {
    .header {
      height: 100%;
      user-select: none;
      background: #fff;

      .header-search {
        position: fixed;
        top: 0;
        left: var(--window-left);
        right: var(--window-right);
        padding: 0.08rem 0;
        z-index: 10;
        background: #fff;

        .search-input {
          width: 3.45rem;
          height: 0.34rem;
          padding: 0.08rem 0.1rem;
          margin: 0 auto;
          background: #f3f3f3;
          border-radius: 0.17rem;
          display: flex;
          align-items: center;
          color: #b2b1b7;

          .search-input-placeholder {
            font-size: 0.12rem;
            margin-left: 0.05rem;
          }
        }
      }

      .placeholder-box {
        width: 100%;
        height: 0.5rem;
      }
    }

    .category-wrap {
      display: flex;
      user-select: none;

      .category-tabs {
        position: fixed;
        left: var(--window-left);
        z-index: 10;
        width: 0.82rem;
        height: calc(100vh - 1rem);
        overflow: auto;
        background: #f5f5f5;

        .category-tab__item {
          .tab-activity {
            color: #ff9b26;
            background-color: #fff;
          }

          a {
            width: 0.82rem;
            height: 0.45rem;
            display: block;
            line-height: 0.45rem;
            text-align: center;
            font-weight: bold;
            color: #606266;
            background-color: #f5f5f5;
            transition: 0.2s;
          }
        }
      }

      .category-content {
        flex: 1;
        background-color: #fff;
        min-height: 100vh;
        padding: 0.12rem 0 60vh 0;
        overflow: auto;
        margin-left: 0.82rem;
      }

      .category-box {
        overflow: hidden;

        .category-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 0.15rem 0.1rem 0.15rem;
        }

        .category-list {
          display: flex;
          flex-wrap: wrap;
          padding-left: 0.15rem;
        }

        .category-item {
          width: 0.74rem;
          text-align: center;
          margin-bottom: 0.07rem;
          margin-right: 0.15rem;

          .category-image {
            width: 0.74rem;
            height: 0.74rem;
            border-radius: 0.04rem;
            overflow: hidden;
            vertical-align: top;
          }

          span {
            width: 100%;
            height: 0.33rem;
            display: block;
            line-height: 0.16rem;
            font-size: 0.12rem;
            color: #616568;
            margin-top: 0.04rem;
            @include TextEllipsis(2);
          }
        }
      }
    }

    .notice-bar {
      width: initial;
      position: fixed;
      left: var(--window-left);
      right: var(--window-right);
      bottom: 0.5rem;
      bottom: calc(0.5rem + constant(safe-area-inset-bottom));
      bottom: calc(0.5rem + env(safe-area-inset-bottom));
      z-index: 20;
    }
  }

  :deep(.inviter) {
    display: none;
  }
</style>
