<template>
  <div class="refuse-pop">
    <ijl-action-sheet
      title="拒绝核销"
      v-model:show="showPop"
      @open="open"
      @confirm="submit"
      @cancel="onClose"
      @close="onClose"
    >
      <div class="refuse-pop-box">
        <!-- <h3 class="title">拒绝核销</h3> -->
        <div class="coach-info">
          <p>
            申请教练：<span class="coach-name">{{ consumeInfo?.coachName }}</span>
          </p>
          <p>
            核销课时：<span class="time-account">{{ consumeInfo.consumeQuantity }}个课时</span>
          </p>
          <p>
            授课方式：<span class="time-account">{{ consumeInfo.statusName }}</span>
          </p>
          <p>
            申请时间：<span class="coach-name">{{
              dateFormat(consumeInfo.applyTime, 'YYYY/MM/DD HH:mm')
            }}</span>
          </p>
        </div>
        <div class="split"></div>
        <div class="label">拒绝理由（选填）</div>
        <van-field
          v-model="formData.reason"
          rows="4"
          autosize
          type="textarea"
          maxlength="100"
          show-word-limit
        />
      </div>
    </ijl-action-sheet>
  </div>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue'
  const emit = defineEmits(['close', 'update:show', 'refresh'])
  import { dateFormat } from '@/utils/day'
  import { refuseConsume } from '@/api/trade-server'
  import { Toast } from 'vant'

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    refuseInfo: {
      type: Object,
      default: () => {},
    },
  })

  const formData = reactive({
    reason: '',
  })
  const showPop = ref(props.show)
  watch(
    () => props.show,
    (newVal) => (showPop.value = newVal),
    { immediate: true },
  )
  const consumeInfo = ref({})
  watch(
    () => props.refuseInfo,
    (newVal) => {
      consumeInfo.value = newVal
      console.log(newVal, 'newVal')
    },
    // (consumeInfo.value = newVal),
    { immediate: true },
  )

  const open = () => {}
  const submit = () => {
    const params = {
      explain: formData.reason || '',
      id: consumeInfo.value.id,
    }
    refuseConsume(params).then(() => {
      Toast('已拒绝核销')
      onClose()
      emit('refresh')
    })
  }

  const onClose = () => {
    showPop.value = false
    emit('update:show', false)
  }
</script>

<style scoped lang="scss">
  .flex {
    display: flex;
  }

  .refuse-pop-box {
    padding: 0 0.15rem;
    .title {
      font-size: 0.16rem;
      color: #616568;
      font-weight: 600;
      text-align: center;
    }
    .coach-info {
      p {
        font-size: 0.14rem;
        color: #1a1b1d;
        margin-bottom: 0.1rem;
      }
      .coach-name {
        color: #1f1f1f;
        font-weight: 600;
      }
      .time-account {
        color: #ff6445;
        font-weight: 600;
      }
    }
    .split {
      margin: 0.12rem 0;
      width: 100%;
      height: 0.01rem;
      background: #eeeeee;
    }
    .label {
      margin-bottom: 0.08rem;
      font-size: 0.14rem;
      color: #1f1f1f;
    }
  }
  :deep(.van-cell) {
    padding: 0.05rem 0.1rem;
    background: #fafafa;
    border-radius: 0.06rem;
  }
  :deep(.van-field__word-limit) {
    color: #b2b1b7;
  }
</style>
