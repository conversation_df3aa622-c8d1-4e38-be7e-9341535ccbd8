<template>
  <div class="sku-group-container">
    <div v-for="(spec, index) in sku.specs" :key="index" class="sku-row">
      <div class="sku-row-title">
        {{ spec.k }}
        <span v-if="spec.k_s === 's2'" class="sku-spec-desc">* 1个课时为60分钟</span>
      </div>
      <div class="sku-specs">
        <div class="sku-spec__item" v-for="(item, index) in spec.list" :key="item.name + index">
          <span
            :class="specClassName(spec, item)"
            @click="onChangeSpec(spec.k_s, item.id, item.disabled)"
          >
            {{ item.name }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'

  const props = defineProps({
    skuConfig: {
      type: Object,
      default: () => {},
      required: true,
    },
  })

  const emit = defineEmits(['change'])

  const sku = computed(() => props.skuConfig)

  // 这里存放的是选中的属性规则id
  const selected = ref({})

  // 这里查找详细的选中的数据
  const selectedSku = computed(() => {
    const selectSpecs = []
    for (let key in selected.value) {
      if (selected.value[key] !== '') {
        // 先找到对应属性规格
        let specGroup = sku.value.specs.find((item) => {
          return item.k_s === key
        })

        let spec = specGroup.list.find((item) => {
          return item.id === selected.value[key]
        })

        selectSpecs.push({
          ...spec,
          specField: key,
          specName: specGroup.k,
        })
      }
    }
    return selectSpecs
  })

  // 重置选中规格到初始状态
  const resetSelected = () => {
    sku.value.specs.forEach((spec) => {
      selected.value[spec.k_s] = ''
    })
  }

  const specClassName = (spec, item) => {
    let className = []
    if (selected.value[spec.k_s] === item.id) {
      className.push('active')
    }
    if (item.disabled) {
      className.push('disabled')
    }
    return className
  }

  // 初始化所有规格 disabled属性 值，
  const initAllSpecDisabled = () => {
    sku.value.specs.forEach((spec) => {
      spec.list.forEach((item) => {
        item.disabled = checkOptional(spec.k_s, item.id)
      })
    })
  }

  /**
   * @param specKey 规格属性
   * @param specVal 规则值
   */
  const checkOptional = (specKey, specVal) => {
    // 深拷贝 避免被影响
    const selectSpec = JSON.parse(JSON.stringify(selected.value))
    selectSpec[specKey] = specVal

    let disabled = sku.value.list.some((item) => {
      let i = 0
      for (let k in selectSpec) {
        if (selectSpec[k] === item[k]) {
          i++
        } else if (selectSpec[k] === '') {
          i++
        }
      }
      // 符合下面条件就退出
      return i === sku.value.specs.length
    })

    return !disabled
  }

  const onChangeSpec = (key, value, disable = false) => {
    if (disable) return

    // 点击同个，清空选中对象的值
    if (selected.value[key] === value) {
      selected.value[key] = ''
    } else {
      selected.value[key] = value
    }

    initAllSpecDisabled()
    emit('change', selectedSku.value)
  }

  const init = () => {
    // 初始化 selected 值 注意：这里要先执行完
    resetSelected()
    initAllSpecDisabled()
  }

  init()

  // 抛出方法
  defineExpose({
    reset: resetSelected,
  })

  // sku结构示例
  // const skuConfig = ref({
  //   specs: [
  //     {
  //       k: "授课类型",
  //       k_s: "s1",
  //       list: [
  //         {
  //           id: "1",
  //           name: "私教一对一",
  //           imgUrl: "https://img01.yzcdn.cn/1.jpg",
  //           price: 100, // 单课时价格
  //         }
  //       ],
  //     }
  //   ],
  //   list: [
  //     {
  //       id: "1",
  //       s1: "1", // 属性1 id
  //       s2: "1", // 属性2 id
  //       price: 100,
  //     },
  //   ],
  // });
</script>

<style lang="scss" scoped>
  .sku-group-container {
  }

  .sku-row-title {
    padding-bottom: 0.1rem;
    font-size: 0.14rem;
    color: #1a1b1d;
  }

  .sku-spec-desc {
    font-size: 0.14rem;
    color: #b2b1b7;
    margin-left: 0.1rem;
  }

  .sku-specs {
    margin-bottom: 0.1rem;
  }

  .sku-spec__item {
    display: inline-block;
    margin-bottom: 0.1rem;

    &:not(:nth-child(4n + 0)) {
      margin-right: 0.1rem;
    }

    span {
      background: #f7f7f7;
      border: 1px solid #eee;
      border-radius: 0.02rem;
      cursor: pointer;
      padding: 0.06rem 0.1rem;
      display: block;
      font-size: 0.13rem;
      user-select: none;
    }

    .active {
      background: #fff6e9;
      border: 1px solid #ffa524;
      color: #ffa524;
    }

    .disabled {
      background: #eeeeee;
      color: #b2b1b7;
      cursor: not-allowed;
    }
  }
</style>
