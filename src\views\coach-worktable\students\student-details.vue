<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="container">
        <div v-if="student" class="attend-class">
          <div class="border-bottom">
            <div class="remain">
              <span class="strong">学员剩余课时：{{ student.showRemainQuantity || 0 }}个</span>
              <span class="history" @click="toBuyListPage">购买记录<van-icon name="arrow" /></span>
            </div>
            <div class="platform-tip">学员已购买课时，请及时联系学员预约上课哦～</div>
          </div>
        </div>
        <div class="student-info">
          <div class="title">学员信息</div>
          <van-row justify="space-between" align="center">
            <van-col>
              <div class="flex flex-center">
                <div class="head-portrait">
                  <img :src="ossURLJoin(student.studentInfo?.avatarUrl)" alt="" />
                </div>
                <div class="username">{{ student.studentInfo?.studentName }}</div>
              </div>
            </van-col>
            <van-col>
              <!-- <a :href="'tel:' + student.studentInfo?.mobile">
                <button class="contact-btn">联系学员</button>
              </a> -->
            </van-col>
          </van-row>
        </div>

        <div class="class-record">
          <div class="title">上课记录</div>
          <van-list
            class="student-list"
            v-model:loading="loading"
            :finished="finished"
            @load="getList"
          >
            <div v-for="item in classRecord" :key="item.id" class="collapse">
              <div class="collapse-title" @click="item.open = !item.open">
                <van-row justify="space-between" align="center">
                  <van-col class="hx">
                    <span>核销{{ item.consumeQuantity }}个课时</span>
                    <van-icon :name="item.open ? 'arrow-down' : 'arrow'" />
                  </van-col>
                  <van-col class="state">{{ item.consumeStatus.statusName }}</van-col>
                </van-row>
                <div class="time">{{ item.applyTime }}</div>
              </div>
              <div :class="['collapse-wrapper', { open: item.open }]">
                <div class="collapse-content">
                  <div v-for="(log, index) in item.logs" :key="index" class="sub-item">
                    <div class="sub-title">{{ log.title }}</div>
                    <div v-if="log.desc" class="operating-desc">
                      {{ log.desc }}
                    </div>
                    <div class="operating-time">{{ log.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
          <empty
            class="empty"
            v-if="emptyShow"
            top="1rem"
            description="暂无上课记录，快去联系学员上课吧～"
          ></empty>
        </div>

        <div v-if="student.remainQuantity > 0" class="fixed-button">
          <button class="button" @click="dialogShow = true">核销课时</button>
        </div>

        <!-- 核销课时弹窗 -->
        <apply-verify-dialog
          v-model:show="dialogShow"
          :data="curHandleOrder"
          @afterSubmit="handleAfterSubmit"
        />
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import ApplyVerifyDialog from './apply-verify-dialog'
  import { getStudentClassSituation, getStudentVerificationList } from '@/api/coach-worktable'
  import Empty from '@/components/empty'
  import { ossURLJoin } from '@/common'

  const route = useRoute()
  const router = useRouter()

  const studentUserId = route.query.studentUserId
  const student = ref({})
  const classRecord = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const dialogShow = ref(false)
  const emptyShow = ref(false)
  const curHandleOrder = ref(null)

  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  // 获取学员详情
  const getStudentDetails = () => {
    let params = { studentUserId }
    getStudentClassSituation(params).then((res) => {
      const { data } = res
      student.value = data
      curHandleOrder.value = {
        studentUserId: student.value.studentInfo.studentUserId,
      }
    })
  }

  // 获取上课情况列表
  const getList = () => {
    pagination.pageNum += 1

    let params = {
      heId: studentUserId,
      ...pagination,
    }
    getStudentVerificationList(params).then((res) => {
      const { data } = res

      data.forEach((item) => {
        item.open = false
        item.logs = []
        // 拼接核销记录
        item.logs.unshift({
          title: `教练${item.coachName}申请核销课时：${item.consumeQuantity}个课时（${item.teachingWay.typeName}）`,
          time: item.applyTime,
        })
        // 核销成功
        if (item.consumeStatus.status === 'AGREED') {
          item.logs.unshift({
            title: `学员已同意教练${item.coachName}的核销申请`,
            time: item.auditTime,
          })
        }
        // 核销拒绝
        if (item.consumeStatus.status === 'REFUSED') {
          item.logs.unshift({
            title: `学员已拒绝教练${item.coachName}的核销申请`,
            time: item.auditTime,
            desc: '拒绝理由：' + item.refuseExplain,
          })
        }
      })

      classRecord.value = classRecord.value.concat(data)

      emptyShow.value = classRecord.value.length === 0

      // 加载结束
      loading.value = false
      // 数据全部加载完成
      if (data.length === 0 || data.length < params.pageSize) {
        finished.value = true
      }
    })
  }

  const toBuyListPage = () => {
    router.push({
      name: 'myWorktableStudentsBuyList',
      query: {
        studentUserId: student.value.studentInfo.studentUserId,
      },
    })
  }

  const init = () => {
    getStudentDetails()
  }

  // 申请核销后，更新当前页面
  const handleAfterSubmit = () => {
    pagination.pageNum = 0
    loading.value = false
    finished.value = false
    classRecord.value = []
    init()
  }

  init()
</script>

<style lang="scss" scoped>
  .container {
    padding-bottom: 0.65rem;
    background: #fff;
  }

  .flex {
    display: flex;
  }

  .flex-center {
    align-items: center;
  }

  .border-bottom {
    border-bottom: 1px solid #eeeeee;
  }

  .attend-class {
    padding: 0 0.15rem;
    background: #fff;

    .remain {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 0.12rem;
    }

    .strong {
      font-size: 0.18rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .platform-tip {
      font-size: 0.14rem;
      color: #616568;
      margin: 0.06rem 0 0.1rem 0;
    }

    .history {
      color: #616568;
    }
  }

  .student-info {
    padding: 0 0.15rem 0.12rem 0.15rem;
    background: #fff;

    .title {
      padding: 0.1rem 0 0.08rem 0;
      font-size: 0.16rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .head-portrait {
      width: 0.36rem;
      height: 0.36rem;
      display: inline-block;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .username {
      color: #1f1f1f;
      margin-left: 0.1rem;
      display: inline-block;
    }

    .contact-btn {
      padding: 0.04rem 0.12rem;
      font-size: 0.14rem;
      line-height: 20px;
      color: #ff9b26;
      border: 1px solid #ff9b26;
      border-radius: 0.16rem;
    }
  }

  .class-record {
    padding: 0 0.15rem;
    background: #fff;
    border-top: 0.08rem solid #f7f7f7;

    .title {
      padding: 0.1rem 0 0.08rem 0;
      font-size: 0.16rem;
      font-weight: bold;
      color: #1a1b1d;
    }

    .record {
    }
  }

  .collapse {
    .collapse-title {
      padding: 0.1rem 0;

      .hx {
        color: #333333;
      }

      .state {
        color: #ff9b26;
      }

      .time {
        margin-top: 0.04rem;
        font-size: 0.12rem;
        color: #b2b1b7;
      }
    }

    .collapse-wrapper {
      height: 0;
      display: none;
      transition: height 1s ease-in-out;
      will-change: height;
    }

    .open {
      height: auto;
      display: block;
    }

    .collapse-content {
      background: #f7f7f7;
      border-radius: 4px;
      padding: 0 0.12rem;
    }

    .sub-item {
      font-size: 0.13rem;
      color: #1a1b1d;
      padding-bottom: 0.1rem;

      &:not(:last-child) {
        border-bottom: 1px solid #eeeeee;
      }
    }

    .sub-title {
      padding-top: 0.1rem;
    }

    .operating-desc {
      margin-top: 0.05rem;
      font-size: 0.13rem;
      color: #616568;
      word-break: break-all;
    }

    .operating-time {
      margin-top: 0.08rem;
      font-size: 0.12rem;
      color: #b2b1b7;
    }
  }

  .fixed-button {
    position: fixed;
    bottom: 0;
    background-color: #fff;
    width: 3.75rem;
    height: 0.6rem;
    padding: 0.1rem 0.15rem;
    text-align: center;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .button {
      width: 3.45rem;
      height: 0.4rem;
      font-size: 0.17rem;
      color: #ffffff;
      background-color: var(--i-primary);
      box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
      border-radius: 0.23rem;
      cursor: pointer;
    }
  }

  .empty {
    padding: 1.2rem 0;
  }
</style>
