<template>
  <div class="trainer-item" @click="trainerClick(newCoach)">
    <img
      v-if="newCoach.coachImages"
      :src="ossURLCompress(JSON.parse(newCoach.coachImages)[0])"
      v-error-img
    />
    <img v-else src="../../../assets/images/default-img.png" alt="" />
    <div v-if="isShowHot" class="hot">
      <img src="../../../assets/images/home/<USER>" alt="" />
    </div>

    <div class="trainer-footer">
      <p class="nick-name-box flex">
        <span class="nick-name">{{ newCoach.realName }}</span>
        <span class="watch-num">{{ newCoach.readCount || 0 }}浏览</span>
      </p>
      <p v-if="newCoach.teachTitle" class="introduce-tit">{{ newCoach.teachTitle }}</p>
      <p v-if="newCoach.teachDescription" class="introduce">{{ newCoach.teachDescription }}</p>
      <p class="label">
        <span v-if="newCoach.teachYear">{{ newCoach.teachYear }}年</span>
        <span v-if="newCoach.coachFeesSetListVO?.feesTypeOne">{{ classType(1) }}</span>
        <span v-if="newCoach.coachFeesSetListVO?.feesTypeTwo">{{ classType(2) }}</span>
        <span v-if="newCoach.coachFeesSetListVO?.feesMoneyThree">{{ classType(3) }}</span>
        <span v-if="newCoach.coachFeesSetListVO?.feesMoneyFour">{{ classType(4) }}</span>
      </p>
      <div v-if="newCoach.showMoney && Number(newCoach.showMoney) !== 0" class="price">
        ¥{{ Number(newCoach.showMoney) }}/小时
      </div>
      <div v-else class="price">待议价</div>
    </div>
  </div>
</template>

<script setup>
  import { ossURLCompress } from '@/common'
  import { watch, ref, computed } from 'vue'
  const props = defineProps({
    coachData: {
      type: Object,
      default: () => {},
    },
    isShowHot: {
      type: Boolean,
      default: false,
    },
    articleList: {
      type: Array,
      default: () => [],
    },
  })
  const newCoach = ref('')
  watch(
    () => props.coachData,
    (newVal) => {
      if (newVal) {
        newCoach.value = props.coachData
      }
    },
    {
      immediate: true,
    },
  )

  const classType = computed(() => {
    return (state) => {
      const obj = {
        1: '私教1对1',
        2: '小班1对2',
        3: '小班1对4',
        4: '亲子班',
      }
      return obj[state]
    }
  })

  const emit = defineEmits(['trainerDetails'])
  const trainerClick = (item) => {
    emit('trainerDetails', item)
  }
</script>

<style scoped lang="scss">
  // 公共样式
  $color1: #1f1f1fff;

  .mb10 {
    margin-top: 0.1rem;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .trainer-item {
    position: relative;
    width: 1.68rem;
    padding: 0.02rem;
    padding-bottom: 0.17rem;
    .trainer-footer {
      height: 1.18rem;
      padding: 0.06rem 0.03rem 0;
    }
    .hot {
      position: absolute;
      top: 0rem;
      right: -0.02rem;
      width: 0.36rem;
      height: 0.18rem;
      img {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
    img {
      display: block;
      width: 1.68rem;
      height: 1.68rem;
      border-radius: 0.06rem;
      border: 0.01rem solid #eee;
      object-fit: cover;
    }
    .nick-name-box {
      margin-bottom: 0.04rem;
      font-weight: 600;
      font-size: 0.14rem;
      line-height: 0.2rem;
      .watch-num {
        font-size: 0.1rem;
        color: #979797;
      }
    }
    .nick-name {
      color: $color1;
    }
    .position {
      font-size: 0.12rem;
      color: #616568;
    }
    .label {
      margin-bottom: 0.05rem;
      width: 1.5rem;
      color: #ff8142;
      overflow: hidden;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      span {
        margin-right: 0.04rem;
        border-radius: 0.02rem;
        font-size: 0.12rem;
        padding: 0.01rem 0.04rem;
        background: #fff3e5;
        color: #ff6445;
        line-height: 0.17rem;
      }
    }

    .introduce,
    .introduce-tit {
      height: 0.17rem;
      // margin: 0.04rem 0;
      margin-bottom: 0.02rem;
      font-size: 0.12rem;
      color: #616568;

      overflow: hidden;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .introduce {
      color: #b2b1b7;
    }
    .price {
      font-size: 0.16rem;
      font-weight: 600;
      color: #ff6445;
      line-height: 0.22rem;
    }
  }
</style>
