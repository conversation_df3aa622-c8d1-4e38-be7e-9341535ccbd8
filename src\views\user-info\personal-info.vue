<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div>
        <van-form class="form" @submit="submit">
          <div class="top-box">
            <van-field
              class="vator"
              name="radio"
              label="头像"
              type="text"
              readonly
              right-icon="arrow"
            >
              <template v-slot:input>
                <van-uploader
                  v-model="formData.headImgList"
                  :deletable="false"
                  :preview-full-image="false"
                  :after-read="afterRead"
                  :preview-image="false"
                >
                  <img :src="formData.vatorPath" alt="" />
                </van-uploader>
              </template>
            </van-field>
            <van-field
              v-model="formData.realName"
              name="用户昵称"
              label="用户昵称"
              maxlength="20"
              :readonly="isNameReadonly"
              placeholder="请输入用户昵称"
              :rules="[{ validator: validatorRealName }]"
            >
              <template #button> {{ getStringLength(formData.realName) }}/20 </template>
            </van-field>
            <van-field name="radio" label="性别">
              <template #input>
                <van-radio-group v-model="formData.sex" direction="horizontal">
                  <van-radio name="man" shape="square" checked-color="#FF9B26">男</van-radio>
                  <van-radio name="lady" shape="square" checked-color="#FF9B26">女</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <field-date-picker
              v-model="formData.birthTime"
              placeholder="请输入日期"
              label="出生日期"
              :maxDate="new Date()"
              type="date"
              title="选择出生日期"
              format="YYYY年MM月DD日"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <van-field
              v-model="formatData.address"
              is-link
              readonly
              label="所在地区"
              placeholder="请选择地区"
              @click="distPickerShow = true"
            ></van-field>
            <!-- 地址选择器 -->
            <dist-picker
              :default-selected="formatData.lastAreaCode"
              v-model:show="distPickerShow"
              @finish="handledAddressFinish"
            />
          </div>
          <p class="tip-txt">以下为运动健康数据，仅用于推荐教练、制定运动计划等</p>
          <div class="skillType">
            <div class="type-label">兴趣科目</div>
            <div class="skill-box">
              <div class="skill-tip" @click="skillPickerShow = true">请选择兴趣科目(多选)</div>
              <div v-for="item in formatData.skillArr.slice(0, 5)" :key="item" class="skill-item">
                {{ item.name }}
                <i class="icon icon-close2" @click="skillDelect(item)" />
              </div>
            </div>
            <van-icon
              class="skill-arrow"
              color="#999"
              size=".16rem"
              name="arrow"
              @click="skillPickerShow = true"
            />
          </div>
          <!-- 技能选择器 -->
          <skill-type
            :selectedDefault="formatData.categoriesRequests"
            :maxSelectNum="5"
            v-model:show="skillPickerShow"
            @confirm="handleSkillConfirm"
          />
          <div class="foot-box">
            <van-field
              v-model="formData.height"
              type="number"
              :maxlength="3"
              name="radio"
              placeholder="请输入身高"
              label="身高"
            >
              <template #button>
                <span class="unit">cm</span>
              </template>
            </van-field>
            <van-field
              v-model="formData.weight"
              type="number"
              :maxlength="3"
              name="radio"
              placeholder="请输入体重"
              label="体重"
            >
              <template #button>
                <span class="unit">kg</span>
              </template>
            </van-field>

            <div class="label">运动目标</div>
            <van-field
              v-model="formData.sportsTarget"
              autosize
              class="textarea-wrapper"
              rows="3"
              maxlength="100"
              type="textarea"
              placeholder="请输入运动目标、健康需求"
            />
          </div>
          <div class="fixed-button">
            <button class="button" native-type="submit">
              {{ isSignUp ? '提交' : '保存' }}
            </button>
          </div>
        </van-form>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import DistPicker from '@/components/dist-picker'
  import FieldDatePicker from '@/components/field-date-picker'
  import SkillType from '@/components/skill-type'
  import { useRoute, useRouter } from 'vue-router'
  import { uploadFile } from '@/api/generic-server'
  import { Toast } from 'vant'
  import { ossURLJoin } from '@/common'
  import { getStringLength } from '@/utils'

  // import UploadFile from "@/components/upload-file";
  import { updateUserInfo, getUserMemberProfileInfo } from '@/api/user-server'
  import { localProxyStorage } from '@/utils/storage'

  const route = useRoute()
  const router = useRouter()

  const isSignUp = ref(route.query.channel === 'signUp')

  const distPickerShow = ref(false)
  const skillPickerShow = ref(false)
  const isNameReadonly = ref(false)

  const formData = reactive({
    realName: '', // 名称
    birthTime: '', // 出生日期
    sex: '', // 性别
    categoriesIds: [], // 兴趣科目
    areaVO: {}, // 所在地区
    metaTitle: '',
    tempHeadImg: '',
    headImgList: [],
    vatorPath: require('../../assets/images/home/<USER>'),
    height: '', //身高
    weight: '', //体重
    sportsTarget: '', //运动目标
    userId: '',
  })

  const formatData = reactive({
    address: '', // 省/市/区名称拼接
    lastAreaCode: '',
    skillArr: [],
    categoriesRequests: {},
  })

  if (localStorage.getItem('_.user')) {
    formData.userId = JSON.parse(localStorage.getItem('_.user')).userId
  }

  const validatorRealName = (val) => {
    if (!val) return '请输入用户昵称'
    if (val.trim() !== '') {
      return true
    } else {
      return '请输入用户昵称'
    }
  }

  onMounted(() => {
    getUserMemberProfileInfo().then((res) => {
      const { data } = res
      isNameReadonly.value = data.identity === 'coach'
      formData.realName = data.realName
      formData.vatorPath = ossURLJoin(data.headImg)
      formData.sex = data.sex || ''
      formData.birthTime = data.birthTime || ''
      formData.height = data.height || ''
      formData.weight = data.weight || ''
      formData.sportsTarget = data.sportsTarget || ''

      if (Array.isArray(data.categoriesDOList)) {
        let selectArr = []
        data.categoriesDOList.forEach((children) => {
          let arr = []
          children.forEach((item) => {
            arr.push(item.id)
          })

          selectArr.push(children[2])
          formatData.skillArr = selectArr
          formData.categoriesIds.push(arr)
          formatData.categoriesRequests[children?.[2]?.id] = children
        })
      }

      formatData.lastAreaCode = data.areaVO?.county

      if (data.areaVO?.provinceName) {
        formatData.address = `${data.areaVO.provinceName}/${data.areaVO.cityName}/${data.areaVO.countyName}`
        formData.areaVO['province'] = data.areaVO.province
        formData.areaVO['city'] = data.areaVO.city
        formData.areaVO['county'] = data.areaVO.county
        formData.areaVO['provinceName'] = data.areaVO.provinceName
        formData.areaVO['cityName'] = data.areaVO.cityName
        formData.areaVO['countyName'] = data.areaVO.countyName
      }
    })
  })
  // 地址选择完毕hook
  const selectedCity = ref('')
  const handledAddressFinish = (params) => {
    selectedCity.value = params.selectedOptions[1].adName
    if (params) {
      let { selectedOptions } = params
      formData.areaCodes = []
      formData.areaVO['province'] = selectedOptions[0].adCode
      formData.areaVO['city'] = selectedOptions[1].adCode
      formData.areaVO['county'] = selectedOptions[2].adCode
      formData.areaVO['provinceName'] = selectedOptions[0].adName
      formData.areaVO['cityName'] = selectedOptions[1].adName
      formData.areaVO['countyName'] = selectedOptions[2].adName

      // selectedOptions.forEach((item) => {
      //   formData.areaCodes.push(item.adCode);
      // });
      formatData.address = params.address
    }
    distPickerShow.value = false
  }

  const tempIndex = ref(null)
  // 删除营业类型
  const skillDelect = (item) => {
    tempIndex.value = null

    formData.categoriesIds.forEach((item, index) => {
      item.forEach((childItem) => {
        if (childItem[2] === item.id) {
          tempIndex.value = index
        }
      })
    })

    if (tempIndex.value || tempIndex.value === 0) {
      formData.categoriesIds.splice(tempIndex.value, 1)
      delete formatData.categoriesRequests[item.id]
    }

    formatData.skillArr = formatData.skillArr.filter((skillItem) => {
      return skillItem.id !== item.id
    })
  }

  // 选中兴趣科目
  const handleSkillConfirm = ({ selected }) => {
    formData.categoriesIds = []
    formatData.skillArr = []
    // skillTreeArr.value = selected;
    if (Array.isArray(selected)) {
      selected.forEach((children) => {
        let arr = []
        children.forEach((item) => {
          arr.push(item.id)
        })
        formData.categoriesIds.push(arr)
        formatData.skillArr.push(children[2])
      })
    }
    skillPickerShow.value = false
  }

  const afterRead = (fileOv) => {
    fileOv.status = 'uploading'
    fileOv.message = '上传中...'

    let formDataParams = new FormData()
    formDataParams.append('file', fileOv.file)
    uploadFile(formDataParams)
      .then((res) => {
        let { code } = res
        if (code === 0) {
          // fileOv.path = data; //存放相对路径
          formData.vatorPath = URL.createObjectURL(fileOv.file)
          formData.tempHeadImg = res.data
          fileOv.status = 'done'
        }
      })
      .catch(() => {
        fileOv.status = 'failed'
        fileOv.message = '上传失败'
      })
  }

  const handleFormData = () => {
    let data = JSON.parse(JSON.stringify(formData))
    return {
      userMemberBasicInfoRequest: {
        realName: data.realName,
        sex: data.sex,
        birthTime: data.birthTime,
        tempHeadImg: data.tempHeadImg,
        areaVO: data.areaVO,
      },
      userMemberHealthDataRequest: {
        weight: data.weight,
        height: data.height,
        sportsTarget: data.sportsTarget,
        categoriesIds: data.categoriesIds,
      },
    }
  }

  const submit = () => {
    let form = handleFormData()
    updateUserInfo(form).then(() => {
      Toast('保存成功')

      // 更新本地存储用户信息
      const userInfo = localProxyStorage.user
      userInfo.realName = form.userMemberBasicInfoRequest.realName
      localProxyStorage.user = userInfo

      if (isSignUp.value) {
        router.push({ name: 'signUpSuccess' })
      } else {
        router.go(-1)
      }
    })
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/fixed-button.scss';
  @import '~@/styles/mixins/mixins';
  @include Icon('close2', 0.11rem, 0.11rem);

  .form {
    padding-bottom: 0.8rem;
    .top-box,
    .foot-box {
      padding: 0 0.2rem;
      background-color: #fff;
    }
    .tip-txt {
      padding: 0.08rem 0.1rem;
      background-color: #f9f9f9;
      font-size: 0.12rem;
      color: #b2b1b7;
    }
  }
  :deep(.van-cell) {
    padding: 0.17rem 0;
    border-bottom: 0.01rem solid #eee;
  }
  .skillType {
    padding: 0.17rem 0;
    font-size: 0.14rem;
    border-bottom: 1px solid #eeeeee;
    background-color: #fff;
    display: flex;
    .skill-arrow {
      vertical-align: middle;
    }
    .type-label {
      position: relative;
      padding-left: 0.2rem;
      flex-shrink: 0;
      width: 0.8rem;
    }
    .skill-tip {
      margin-bottom: 0.1rem;
      width: 2rem;
      color: #c8c9cc;
    }
    .skill-box {
      margin-left: 0.3rem;
      flex-shrink: 0;
      width: 2.28rem;
      display: flex;
      flex-wrap: wrap;
      .skill-item {
        margin-right: 0.06rem;
        margin-bottom: 0.1rem;
        padding: 0.04rem 0.08rem;
        background: rgba(255, 165, 36, 0.1);
        border-radius: 0.04rem;
        border: 0.01rem solid #ffa524;
        font-size: 0.13rem;
        color: #ffa524;
        i {
          vertical-align: middle;
          margin-bottom: 0.03rem;
        }
      }
    }
  }
  .unit {
    font-size: 0.14rem;
    color: #333333;
  }
  .textarea-wrapper {
    padding-top: 0.1rem;
    :deep(.van-cell__value) {
      background: #fafafa;
      border: 1px solid #e8e8e8;
      border-radius: 0.04rem;
      padding: 0.08rem;
    }
  }
  .label {
    padding-top: 0.17rem;
    font-size: 0.14rem;
    color: #453938;
  }
  .vator {
    padding: 0.1rem 0;
    align-items: center;
    :deep(.van-cell__right-icon) {
      margin-left: 0.1rem;
    }
    :deep(.van-field__control--custom) {
      justify-content: flex-end;
    }
    img {
      width: 0.64rem;
      height: 0.64rem;
      border-radius: 50%;
      object-fit: cover;
    }
  }
  .top-box :deep(.van-image__img) {
    border-radius: 50%;
    width: 0.64rem;
    height: 0.64rem;
    // left: 50%;
    // top: 50%;
    // transform: translate(-50%, -50%);
  }
  :deep(.van-field__label) {
    font-size: 0.14rem;
    color: #453938;
    margin-right: 0;
  }
  :deep(.van-field__control) {
    font-size: 0.14rem;
    &:read-only {
      color: #b2b1b7;
    }
  }
  :deep(.van-cell__right-icon) {
    font-size: 0.16rem;
  }
</style>
