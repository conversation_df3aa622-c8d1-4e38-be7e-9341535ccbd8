<template>
  <div class="UploadAvatar">
    <div class="uploader-wrapper">
      <div class="upload">
        <div v-if="!previewImage" class="upload-icon" />
        <img v-if="previewImage" class="upload-preview-image" :src="previewImage" />
        <div v-if="status === 'uploading'" class="upload__loading">
          <van-loading />
        </div>
        <input
          ref="inputRef"
          class="upload-input"
          @change="onInputChange"
          type="file"
          accept="image/*"
        />
      </div>
    </div>
    <div class="upload-tip">上传图片</div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { Toast } from 'vant'
  import { ImageCropper } from '@/components/image-cropper'
  import { uploadFile } from '@/api/generic-server'
  import { base64ToFile } from '@/utils'
  import { getOssURL } from '@/common'

  const props = defineProps({
    image: {
      type: String,
      default: null,
    },
  })

  const emit = defineEmits(['success'])

  const status = ref('')

  const accept = ['jpg', 'png', 'jpeg']
  const previewImage = ref(null)
  const inputRef = ref(null)

  watch(
    () => props.image,
    (newVal) => {
      previewImage.value = getOssURL(newVal)
    },
    { immediate: true },
  )

  const resetInputValue = () => {
    if (inputRef.value) {
      inputRef.value.value = ''
    }
  }

  const onUpload = (file) => {
    status.value = 'uploading'
    let formData = new FormData()
    formData.append('file', file)

    uploadFile(formData, true).then((res) => {
      let { data, code } = res
      if (code === 0) {
        status.value = 'done'
        emit('success', data)
      }
    })
  }

  const openImageCropper = (file) => {
    ImageCropper({ image: file })
      .then((dataURL) => {
        previewImage.value = dataURL

        let imageFile = base64ToFile(dataURL, 'head-portrait')
        resetInputValue()
        onUpload(imageFile)
      })
      .catch(() => {
        resetInputValue()
      })
  }

  const validFileType = (file) => {
    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
    return accept.includes(fileType) // 是否符合规定文件格式
  }

  const onInputChange = (event) => {
    const { files } = event.target
    if (!files || !files.length) return
    const file = files.length === 1 ? files[0] : [].slice.call(files)

    if (!validFileType(file)) {
      Toast('只支持上传格式为 ' + accept.join('，') + ' 的文件')
      return
    }

    openImageCropper(file)
  }

  defineExpose({
    status,
  })
</script>

<style lang="scss" scoped>
  .UploadAvatar {
    display: inline-block;
    user-select: none;
  }

  .uploader-wrapper {
    width: 0.54rem;
    height: 0.54rem;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid #ccc;
  }

  .upload {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .upload-icon {
      width: 100%;
      height: 100%;
      background: url('../images/default-avatar.png') no-repeat;
      background-size: 100% 100%;
    }

    .upload-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      cursor: pointer;
      opacity: 0;
    }

    .upload-preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .upload-tip {
    font-size: 0.12rem;
    color: #b2b1b7;
    margin-top: 0.02rem;
  }

  .upload__loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: rgba(50, 50, 51, 0.88);
    z-index: 999;
  }
</style>
