<template>
  <div class="wrapper">
    <div ref="content" class="content">
      <slot></slot>
    </div>
    <div class="mask-layer" v-if="!isOpen"></div>
  </div>
  <div class="unpack" v-if="showUnpack" @click="open">
    <van-icon class="icon" :name="isOpen ? 'arrow-up' : 'arrow-down'" />
    <div>
      {{ isOpen ? '收起' : '展开' }}
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'

  const content = ref(null)

  const isOpen = ref(true)

  const showUnpack = ref(false)

  const open = () => {
    if (!isOpen.value) {
      content.value.setAttribute('style', '')
      isOpen.value = true
    } else {
      content.value.style.height = '2.3rem'
      content.value.style.overflow = 'hidden'
      isOpen.value = false
    }
  }

  const init = () => {
    isOpen.value = true
    let contentHeight = content.value.getBoundingClientRect().height
    if (contentHeight >= 230) {
      showUnpack.value = true
      content.value.style.height = '2.3rem'
      content.value.style.overflow = 'hidden'
      isOpen.value = false
    }
  }

  onMounted(() => {
    init()
  })

  // 如果内容发生变化，可通过 ref 获取组件实例 重新init化一次
  defineExpose({
    init: init,
  })
</script>

<style lang="scss" scoped>
  .wrapper {
    position: relative;
  }

  .content {
    transition: 0.6s;
  }

  .mask-layer {
    width: 100%;
    height: 0.55rem;
    position: absolute;
    bottom: 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
  }

  .unpack {
    width: 100%;
    height: 0.55rem;
    position: relative;
    text-align: center;
    padding: 0.1rem 0;
    //background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
    color: #a7a1a1;
    font-size: 0.14rem;
    user-select: none;
    z-index: 2;

    .icon {
      font-size: 0.16rem;
    }
  }
</style>
