<template>
  <div class="suspended-panel">
    <div class="clientele-services" @click="onOpenPopup">
      <i class="icon-clientele-services"></i>
      <span class="text">客服</span>
    </div>
  </div>
</template>

<script setup>
  import gm from '@/components/gm-popup'

  const onOpenPopup = () => {
    gm.open({
      title: '联系客服',
      desc: '添加企业微信，在线联系客服',
    })
  }
</script>

<style lang="scss" scoped>
  .suspended-panel {
    position: fixed;
    right: 0;
    bottom: 2.12rem;
  }

  .clientele-services {
    width: 0.72rem;
    height: 0.27rem;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.83);
    box-shadow: 0 0.02rem 0.04rem 0 rgba(0, 0, 0, 0.1);
    border-radius: 0.14rem 0 0 0.14rem;
    user-select: none;

    .icon-clientele-services {
      width: 0.25rem;
      height: 0.25rem;
      margin-left: 0.09rem;
      background: url('../../../assets/images/icon/icon-kefu.png') no-repeat;
      background-size: 100% 100%;
    }

    .text {
      font-size: 0.12rem;
      color: #333333;
      margin-left: 0.03rem;
    }
  }
</style>
