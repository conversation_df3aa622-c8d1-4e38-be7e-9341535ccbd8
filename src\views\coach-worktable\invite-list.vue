<template>
  <page :title="$route.meta?.title" v-bind="$route.meta?.page">
    <template #page>
      <div class="page-content page-bg-white invite-wrap">
        <div v-if="mainShow" class="list">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="-没有更多了-"
            @load="onLoadList"
          >
            <div v-for="item in list" :key="item.userId" class="item feedback">
              <van-image
                width="0.42rem"
                height="0.42rem"
                fit="cover"
                round
                class="avatar"
                :src="ossURLJoin(item.headImg)"
              />
              <div class="user-info-box">
                <div class="username">
                  <span class="text">{{ item.userName }}</span>
                  <div class="tag">
                    <span>{{ item.identity }}</span>
                  </div>
                </div>
                <div class="invite-info">
                  <div class="invite-name omit">推荐人：{{ item.puserName }}</div>
                  <div class="invite-time">{{ formatTiem(item.registerTime) }}</div>
                </div>
              </div>
            </div>
          </van-list>
        </div>
        <div v-if="emptyShow" class="empty">
          <img class="empty-icon" src="../../assets/images/empty.png" alt="你还未发布过内容哦～" />
          <div class="empty-desc">
            <p>你还没有推荐用户哦</p>
            <p>你可以通过名片或点击右上角分享</p>
          </div>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { getUserDistributionList } from '@/api/user-server'
  import { ossURLJoin } from '@/common'

  const list = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const emptyShow = ref(false)
  const mainShow = ref(false)
  const pagination = reactive({
    pageNum: 0,
    pageSize: 10,
  })

  const onLoadList = (callback) => {
    pagination.pageNum += 1
    getUserDistributionList(pagination).then((res) => {
      let { data } = res
      list.value = list.value.concat(data.records)
      // 加载状态结束
      loading.value = false

      // 数据全部加载完成
      if (data.records.length === 0 || data.records.length < pagination.pageSize) {
        finished.value = true
      }

      callback && callback()
    })
  }

  onLoadList(() => {
    mainShow.value = list.value.length > 0
    emptyShow.value = !mainShow.value
  })

  const formatTiem = (time) => {
    if (typeof time === 'string') {
      return time.slice(0, 10)
    }
  }
</script>

<style lang="scss" scoped>
  .invite-wrap {
    .list {
      margin: 0 0.15rem;
      .item {
        display: flex;
        border-bottom: 1px solid #eeeeee;
        padding: 0.12rem 0;

        .avatar {
          margin-right: 0.1rem;
        }

        .user-info-box {
          display: flex;
          flex-direction: column;
          flex: 1;
        }

        .username {
          font-size: 0.14rem;
          font-weight: 600;
        }

        .tag {
          display: inline;
          background: rgba(#ccc, 0.34);
          border-radius: 0.03rem;
          margin-left: 0.08rem;

          span {
            display: inline-block;
            font-size: 0.12rem;
            color: #616568;
            transform: scale(0.84);
          }
        }

        .invite-info {
          display: flex;
          justify-content: space-between;
          margin-top: 0.02rem;
          font-size: 0.12rem;

          .invite-name {
            width: 2rem;
            color: #616568;
          }

          .invite-time {
            color: #979797;
          }
        }
      }
    }

    .empty {
      background-color: #fff;
      height: 100%;
      text-align: center;

      .empty-icon {
        width: 0.98rem;
        height: 1rem;
        margin-top: 1.77rem;
      }

      .empty-desc {
        font-size: 0.12rem;
        color: #b2b1b7;
        margin-top: 0.1rem;
        user-select: none;
      }
    }
  }
</style>
