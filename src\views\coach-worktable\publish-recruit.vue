<template>
  <div class="form-wrap" :style="{ height: height + 'px' }">
    <!-- 表单 -->
    <div class="form" :style="{ height: height - 60 + 'px' }">
      <div class="form-item">
        <label class="form-item-label">标题</label>
        <input class="form-item-input" v-model="formData.title" placeholder="请输入培训标题" />
      </div>
      <div class="form-item">
        <label class="form-item-label">开班时间</label>
        <input
          class="form-item-input"
          v-model="formData.trainTime"
          @click="timePickerShow = true"
          readonly
          placeholder="请选择开班时间（选填）"
        />
      </div>
      <div class="form-item">
        <label class="form-item-label">正文</label>
        <quill-editor
          v-model="formData.content"
          class="editor"
          @ready="editorReady"
          @focus="editorFocus"
          @blur="editorBlur"
          placeholder="请输入正文内容"
        />
      </div>
    </div>

    <!--    富文本工具栏 -->
    <quill-toolbar v-show="!footerShow" />

    <div v-show="footerShow" class="fixed-bottom">
      <button v-preventReClick class="i-button publish-btn" @click="onPublish">发布</button>
    </div>

    <datetime-picker
      v-model:show="timePickerShow"
      v-model="date.current"
      type="date"
      :minDate="date.minDate"
      :maxDate="date.maxDate"
      @confirm="handledTimeConfirm"
    />
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import DatetimePicker from '@/components/datetime-picker'
  import Schema from 'async-validator'
  import { dateFormat } from '@/utils/day'
  import { addUserTrains, updateUserTrains, getTrainsDetail } from '@/api/generic-server'
  import { Toast } from 'vant'
  import useKeepAliveStore from '@/store/keepAlive'
  import useVisualViewport from '@/use/useVisualViewport'
  import QuillEditor from '@/components/quill-editor/editorV2'
  import QuillToolbar from '@/components/quill-editor/toolbarV2'
  const { height } = useVisualViewport()
  import { useEventListener } from '@vant/use'

  const route = useRoute()
  const router = useRouter()
  const keepAliveStore = useKeepAliveStore()
  const trainsId = route.query.trainsId
  const isEditState = !!trainsId // 是否编辑状态
  let quill = ref(null)

  let date = ref({
    current: new Date(),
    minDate: new Date(2012, 0, 1),
    maxDate: new Date(2100, 0, 1),
  })

  let timePickerShow = ref(false)
  const footerShow = ref(true)
  const formData = reactive({
    title: '',
    trainTime: '',
    content: '',
  })

  //表单校检
  let formValidator = new Schema({
    title: { required: true, message: '请输入标题' },
    content: { required: true, message: '请输入正文内容' },
  })

  const handledTimeConfirm = (value) => {
    timePickerShow.value = false
    formData.trainTime = dateFormat(value, 'YYYY-MM-DD')
  }

  const editorReady = (editor) => {
    quill.value = editor
  }

  const editorFocus = () => {
    footerShow.value = false
  }

  const editorBlur = () => {
    footerShow.value = true
  }

  const toPostPage = (trainsId) => {
    router.replace({
      name: 'trainDetails',
      params: {
        trainId: trainsId,
      },
    })
  }

  const onPublish = () => {
    const cloneFormData = JSON.parse(JSON.stringify(formData))
    cloneFormData.trainTime = cloneFormData.trainTime ? cloneFormData.trainTime + ' 00:00:00' : null

    formValidator
      .validate(cloneFormData)
      .then(() => {
        delete cloneFormData.text

        let loading = Toast.loading({
          duration: 0,
          forbidClick: true,
          loadingType: 'spinner',
        })

        if (isEditState) {
          cloneFormData.trainsId = trainsId
          updateUserTrains(cloneFormData)
            .then(() => {
              // 删除工作台列表页缓存
              keepAliveStore.removeKeepAlive('coachWorktableRecruitPosts')
              loading.clear()
              Toast.success('修改成功')
              toPostPage(trainsId)
            })
            .catch(() => {
              loading.clear()
            })
        } else {
          addUserTrains(cloneFormData)
            .then((res) => {
              const { data } = res
              // 删除工作台列表页缓存
              keepAliveStore.removeKeepAlive('coachWorktableRecruitPosts')
              loading.clear()
              Toast.success('发布成功')
              toPostPage(data)
            })
            .catch(() => {
              loading.clear()
            })
        }
      })
      .catch(({ errors }) => {
        Toast(errors[0].message)
      })
  }

  const getPostInfo = () => {
    let params = {
      trainsId,
      ignoreHits: true,
    }
    getTrainsDetail(params).then((res) => {
      const { data } = res
      formData.title = data.title
      if (typeof data.trainTime === 'string') {
        formData.trainTime = data.trainTime.slice(0, 10)
        date.value.current = new Date(formData.trainTime)
      }
      formData.content = data.content
    })
  }

  if (isEditState) {
    getPostInfo()
  }

  useEventListener('scroll', () => {
    document.documentElement.scrollTop = 0
  })
</script>

<style lang="scss" scoped>
  .form-wrap {
    position: relative;
    overflow: hidden;
    background-color: #fff;
    padding-bottom: 0.8rem;

    .form {
      overflow: auto;
      padding: 0 0.1rem;

      .form-item {
        //padding: 0 0.1rem;

        &:not(:last-child) {
          border-bottom: 1px solid #eeeeee;
        }
      }

      .form-item-label {
        font-size: 0.14rem;
        color: #1a1b1d;
        padding-top: 0.17rem;
        display: block;
      }

      .form-item-input {
        width: 100%;
        padding-top: 0.1rem;
        margin-bottom: 0.16rem;
        font-size: 0.15rem;
        outline: none;
        border: none;

        &::-webkit-input-placeholder {
          color: #b2b1b7;
        }
      }

      .textarea {
        padding: 0;
      }

      .editor {
        margin-top: 0.07rem;
      }
    }

    .fixed-bottom {
      width: 3.75rem;
      height: 0.6rem;
      line-height: 0.6rem;
      position: fixed;
      bottom: 0;
      text-align: center;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);
      background-color: #fff;

      .publish-btn {
        width: 3.45rem;
        height: 0.4rem;
        background: #ff9b26;
        box-shadow: 0 0.02rem 0.04rem 0.01rem rgba(245, 176, 76, 0.1);
        border-radius: 0.2rem;
        font-size: 0.16rem;
        color: #fff;
      }
    }
  }
</style>
