<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div>
        <empty v-if="emptyShow" top="2.41rem" description="你还未收藏教练哦～" />
        <div v-if="!emptyShow" class="train-box">
          <van-list
            class="trainer flex"
            v-model:loading="loading"
            :finished="finished"
            :finished-text="formData.trainerList.length < 1 ? '' : '-没有更多了-'"
            @load="onLoad"
          >
            <CoachInfo
              v-for="item in formData.trainerList"
              :coachData="item"
              :key="item"
              @trainerDetails="trainerDetails"
            />
          </van-list>
        </div>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import CoachInfo from '../common/components/coach-info.vue'
  import { useRouter, useRoute } from 'vue-router'
  import { getCoachCollectionList } from '@/api/coach-server'
  import Empty from '@/components/empty'

  const router = useRouter()
  const route = useRoute()

  console.log(route)
  const loading = ref(false)
  const finished = ref(false)
  const formData = reactive({
    trainerList: [], //教练列表
  })
  // 教练请求参数
  const formParams = reactive({
    pageNum: 0,
    pageSize: 12,
  })
  const emptyShow = ref(false)
  const onLoad = async () => {
    formParams.pageNum += 1

    await getCoachCollectionList(formParams)
      .then((res) => {
        let { data } = res
        if (res.code === 0) {
          formData.trainerList = formData.trainerList.concat(data.records)
          console.log(formData.trainerList, 'formData.trainerList')
          if (formData.trainerList.length === 0) {
            emptyShow.value = true
          }
          // 加载状态结束
          loading.value = false

          // 数据全部加载完成
          if (data.records.length === 0 || data.records.length < formParams.pageSize) {
            console.log('数据全部加载完成')
            finished.value = true
          }
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true
      })
  }
  // 教练详情
  const trainerDetails = (item) => {
    router.push({
      path: `/coach/details/${item.id}`,
    })
  }
</script>

<style scoped lang="scss">
  // 公共样式
  $color1: #1f1f1fff;

  .mb10 {
    margin-top: 0.1rem;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .train-box {
    width: 3.75rem;
    // height: 100vh;
    padding: 0.1rem 0.13rem;
    background-color: #fff;
  }
  .trainer {
    flex-wrap: wrap;
    align-items: stretch;
  }
  :deep(.van-list__finished-text) {
    width: 3.75rem;
    flex-shrink: 0;
    margin: 0;
  }
  // :deep(.train-box) {
  //   height: 100vh;
  // }
</style>
