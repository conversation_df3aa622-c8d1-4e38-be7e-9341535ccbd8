<script setup>
  import { useRoute, useRouter } from 'vue-router'
  import { userSilenceAuth, userWeChatAuth } from '@/api/user-server'
  import { weChatLogin } from '@/api/auth-server'
  import { isLogin } from '@/common'
  import { toOauthPage } from '@/views/act/a20230321jlzm/weChatAuth'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'

  const route = useRoute()
  const router = useRouter()
  const { code, state } = route.query

  if (isLogin()) {
    router.go(-1)
  }

  // 公用逻辑
  const toLoginPage = (uid) => {
    sessionProxyStorage.weChatUId = uid
    location.href = sessionProxyStorage.lastActPageRoute || '/act/a20230321jlzm'
  }

  // 静默授权
  const silenceAuth = () => {
    userSilenceAuth({ code }).then((res) => {
      // data为空，则说明没绑定微信，走手动授权
      if (!res.data) {
        toOauthPage('snsapi_userinfo')
        return
      }

      // automaticLogon 是否自动登录  0：否 1：是
      const { unionId, automaticLogon } = res.data

      if (automaticLogon === '1') {
        weChatLogin({ unionId }).then((res) => {
          const { code, data } = res
          // 登录成功
          if (code === 0) {
            localProxyStorage.user = data
            window.location.replace(sessionProxyStorage.lastActPageRoute || '/act/a20230321jlzm')
          }

          // 该微信未绑定账号，登录失败
          if (code === 110009) {
            toLoginPage(unionId)
          }
        })
      } else {
        toLoginPage(unionId)
      }
    })
  }

  // 手动授权
  const manualAuth = () => {
    userWeChatAuth({ code }).then((res) => {
      const { data: uid } = res
      toLoginPage(uid)
    })
  }

  const auth = () => {
    if (state === 'snsapi_base') {
      silenceAuth()
      return
    }

    if (state === 'snsapi_userinfo') {
      manualAuth()
    }
  }

  auth()
</script>
