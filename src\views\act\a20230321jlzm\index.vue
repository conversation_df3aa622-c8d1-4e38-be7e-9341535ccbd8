<template>
  <page
    :title="$route.meta?.title"
    navigation-bar-type="transparent"
    :navigationBarCoverage="9999999"
    :loading="pageLoading"
  >
    <template #page>
      <div class="banner">
        <img src="./images/active-image-1.png" alt="" />
        <button class="act-rules-btn" @click="actRulesShow = true">活动规则</button>
        <LikesMarquee />
      </div>

      <div class="act-content">
        <Tabs v-model="tabIndex">
          <template #intro>
            <img
              class="introduce"
              v-for="item in 8"
              :key="item"
              :src="getOssURL(`/applet/introduce-${item}.png`)"
            />
            <div class="fixed-page-footer">
              <button v-if="actInfo.btnType === 1" class="footer-btn" @click="onEnterFor">
                我要成为明星教练
              </button>
              <button
                v-if="actInfo.btnType === 2"
                class="footer-btn"
                @click="openSharePopup(actInfo.helpCoachInfo.coachInfo)"
              >
                分享集赞
              </button>
              <button
                v-if="actInfo.btnType === 3 && actInfo.finalResult"
                class="footer-btn"
                @click="contactPopupShow = true"
              >
                联系客服，领取活动参与权益
              </button>
              <button
                v-if="(actInfo.btnType === 3 || actInfo.btnType === 4) && !actInfo.finalResult"
                class="footer-btn"
                @click="onCollectGiftBag"
              >
                联系客服，领取活动礼包
              </button>
            </div>
          </template>
          <template #top>
            <div class="tab1-content">
              <ActProgress v-if="actInfo.activityStatus !== 2" />
              <ActEnded v-else />
              <div class="fixed-page-footer" v-show="footerShow">
                <button v-if="!isLike" class="footer-btn" @click="giveLike">点赞</button>
                <button
                  v-else-if="isSelf"
                  class="footer-btn"
                  @click="openSharePopup(actInfo.helpCoachInfo.coachInfo)"
                >
                  分享集赞
                </button>
                <button
                  v-else
                  class="footer-btn"
                  @click="openSharePopup(actInfo.helpCoachInfo.coachInfo)"
                >
                  帮ta集赞
                </button>
                <button
                  v-if="actInfo.btnType === 3 && actInfo.finalResult"
                  class="footer-btn"
                  @click="contactPopupShow = true"
                >
                  联系客服，领取活动参与权益
                </button>
                <button
                  v-if="(actInfo.btnType === 3 || actInfo.btnType === 4) && !actInfo.finalResult"
                  class="footer-btn"
                  @click="onCollectGiftBag"
                >
                  联系客服，领取活动礼包
                </button>
              </div>
            </div>
          </template>
        </Tabs>
      </div>

      <!-- 活动规则 -->
      <ActRulesPopup v-model="actRulesShow" />
      <!-- 分享集赞 -->
      <SharePopup v-model="sharePopupShow" />
      <!-- 登录弹窗 -->
      <LoginPopup v-model="loginPopupShow" />
      <ContactPopup v-model="contactPopupShow" />
      <GiftPopup v-model="giftPopupShow" />
      <!-- 点赞动画 -->
      <LikeAnimation ref="likeAnimRef" />
    </template>
  </page>
</template>

<script>
  export default { name: 'a20230321jlzm' }
</script>

<script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
  import { Toast } from 'vant'
  import { useChildren, useEventListener } from '@vant/use'
  import Tabs from './components/Tabs'
  import LikesMarquee from './components/LikesMarquee'
  import ActRulesPopup from './components/ActRulesPopup'
  import ActProgress from './components/ActProgress'
  import SharePopup from './components/SharePopup'
  import LoginPopup from './components/LoginPopup'
  import GiftPopup from './components/GiftPopup'
  import ActEnded from './components/ActEnded'
  import LikeAnimation from './components/LikeAnimation'
  import ContactPopup from './components/ContactPopup'
  import { reqActInfo, reqUserLike } from './api'
  import { isLogin, getOssURL } from '@/common'
  import setWxShare from '@/utils/weChat/share'
  import { baseURL, ossURL } from '@/config'
  import { localProxyStorage, sessionProxyStorage } from '@/utils/storage'
  import { isInViewPort } from '@/utils/elem'
  import { isQyWeChat, isWeChat, throttle } from '@/utils'
  import { toOauthPage } from '@/views/act/a20230321jlzm/weChatAuth'
  import useKeepAliveStore from '@/store/keepAlive'

  const route = useRoute()
  const router = useRouter()

  const shareUserId = route.query.shareUserId || null

  if (isWeChat() && !sessionProxyStorage.weChatUId && !isQyWeChat() && !isLogin()) {
    sessionProxyStorage.lastActPageRoute = route.fullPath
    toOauthPage()
  }

  const tabIndex = ref(shareUserId ? 1 : 0)
  if (route.query.tabIndex) {
    tabIndex.value = Number(route.query.tabIndex) || tabIndex.value
  }
  const keepAliveStore = useKeepAliveStore()
  const actRulesShow = ref(false)
  const sharePopupShow = ref(false)
  const loginPopupShow = ref(false)
  const contactPopupShow = ref(false)
  const giftPopupShow = ref(false)
  const pageLoading = ref(true)
  const likeAnimRef = ref(null)
  const footerShow = ref(false)

  const actInfo = ref({})
  const coachShareInfo = ref({})

  const isLike = computed(() => {
    if (!actInfo.value) return
    return !!actInfo.value.currentLikeCoachUserId
  })

  const isSelf = computed(() => {
    if (!actInfo.value) return
    return actInfo.value.loginUserId === actInfo.value.helpCoachInfo?.coachInfo.coachUserId
  })

  const openLoginPopup = () => {
    loginPopupShow.value = true
  }

  function setWeChatShare() {
    setWxShare({
      title: '投票选出，明星运动教练，让优秀的人更优秀！',
      desc: '谁是明星教练，您决定！快来投票吧！',
      link: baseURL + '/act/a20230321jlzm',
      imgUrl: ossURL + '/applet/321ActShareIcon.png',
    })
  }

  function initialize() {
    const params = { shareUserId }
    reqActInfo(params)
      .then((res) => {
        pageLoading.value = false
        const { data } = res
        actInfo.value = data

        if (data.activityStatus === 2) {
          tabIndex.value = 1
          Toast({
            message: '活动已结束',
            duration: 3000,
          })
        }

        if (data.btnType === 2) {
          tabIndex.value = 1
        }
      })
      .catch(() => {
        pageLoading.value = false
      })
  }

  const onEnterFor = () => {
    if (!isLogin()) {
      openLoginPopup(true)
    } else {
      router.push('/act/a20230321jlzm/enter-for')
    }
  }

  const onCollectGiftBag = () => {
    if (isLogin()) {
      router.push('/act/a20230321jlzm/collect-gift-bag')
    } else {
      openLoginPopup()
    }
  }

  const openSharePopup = (coachInfo) => {
    sharePopupShow.value = true
    coachShareInfo.value = coachInfo
  }

  const openGiftPopup = () => {
    giftPopupShow.value = true
  }

  // 点赞动画
  const playLikeAnim = () => {
    likeAnimRef.value?.play()
  }

  const openContactPopup = () => {
    // 每天只弹一次，活动期间做多三次
    let nowTime = new Date().getTime()
    const dayEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime().toString()
    const CONTACT_POPUP_321 = localProxyStorage.CONTACT_POPUP_321

    if (CONTACT_POPUP_321 && typeof CONTACT_POPUP_321 === 'string') {
      const data = localProxyStorage.CONTACT_POPUP_321.split('|')
      if (nowTime > data[data.length - 1] && data.length < 3) {
        contactPopupShow.value = true
        data.push(dayEndTime)
        localProxyStorage.CONTACT_POPUP_321 = data.join('|')
      }
    } else {
      localProxyStorage.CONTACT_POPUP_321 = dayEndTime
      contactPopupShow.value = true
    }
  }

  const showContactPopup = () => (contactPopupShow.value = true)

  const giveLike = () => {
    if (isLogin()) {
      let params = { coachUserId: actInfo.value.helpCoachInfo.coachInfo.coachUserId }

      reqUserLike(params).then(() => {
        playLikeAnim()
        initialize()
        // 3s后 弹窗领取礼包弹窗
        setTimeout(() => {
          if (isSelf.value && actInfo.value.btnType === 2) {
            openSharePopup(actInfo.value.helpCoachInfo.coachInfo)
          } else {
            giftPopupShow.value = true
          }
        }, 1800)
      })
    } else {
      openLoginPopup()
    }
  }

  const handleScroll = () => {
    const elem = document.documentElement.querySelector('.Coach')
    if (!elem) return
    footerShow.value = !isInViewPort(elem)
  }

  const beforeRouteLeave = (to) => {
    let pages = ['coachDetails']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('a20230321jlzm')
    }
  }

  useEventListener('scroll', throttle(handleScroll, 100))

  const { linkChildren } = useChildren('321ACT')

  linkChildren({
    actInfo,
    openSharePopup,
    openLoginPopup,
    openContactPopup,
    showContactPopup,
    openGiftPopup,
    shareUserId,
    coachShareInfo,
    initialize,
    playLikeAnim,
    giveLike,
  })

  onBeforeRouteLeave(beforeRouteLeave)
  setWeChatShare()
  initialize()
</script>

<style lang="scss" scoped>
  @import './font/index.css';

  .banner {
    position: relative;

    img {
      width: 100%;
      height: 3.04rem;
    }

    .act-rules-btn {
      width: 0.65rem;
      height: 0.24rem;
      line-height: 0.24rem;
      background: #fff0a5;
      border-radius: 1rem 0 0 1rem;
      font-size: 0.12rem;
      color: #f33308;
      position: absolute;
      top: 0.7rem;
      right: 0;
    }
  }

  .act-content {
    margin-top: -0.54rem;
    padding-bottom: 0.8rem;
    background: #ffcd00;
  }

  .tab1-content {
    background: #ffcd00;
  }

  .introduce {
    width: 100%;
    vertical-align: top;
  }

  .fixed-page-footer {
    z-index: 99;
    background-color: #ffcd00;

    .footer-btn {
      width: 100%;
      height: 0.46rem;
      background: linear-gradient(136deg, #fe5526 0%, #f12b02 100%);
      font-family: AlibabaPuHuiTiB;
      border-radius: 0.08rem 0.08rem 0 0;
      font-size: 0.18rem;
      color: #ffffff;
    }
  }

  :deep(.inviter) {
    display: none;
  }
</style>
