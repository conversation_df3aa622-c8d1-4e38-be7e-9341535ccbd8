<template>
  <div class="feedback-notice-bar">
    <van-notice-bar mode="closeable">
      <div class="text" @click="$router.push({ name: 'helpFeedback' })">
        <i class="icon-edit" />找不到想要的？发布需求
      </div>
    </van-notice-bar>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
  .feedback-notice-bar {
    background: #fff;

    .icon-edit {
      width: 0.16rem;
      height: 0.16rem;
      display: inline-block;
      vertical-align: text-top;
      background: url('../../../assets/images/icon/icon-edit.png') no-repeat;
      background-size: 100% 100%;
      margin-right: 0.08rem;
    }

    .text {
      font-size: 0.14rem;
    }

    :deep(.van-notice-bar__content) {
      width: 100%;
      text-align: center;
    }
  }
</style>
