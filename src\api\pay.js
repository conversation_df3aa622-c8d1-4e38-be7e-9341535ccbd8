// # 支付服务 pay.js

import http from '@/utils/axios'

// 获取订单详情
export const reqOrderDetails = (params) => {
  return http.get('/trade-server/api/member/order/detail', { params })
}

// 确认选择的套餐，获取订单详情
export const reqCheckOrder = (params) => {
  return http.post('/trade-server/api/member/order/check', params)
}

// 下单
export const reqSubmitOrder = (params) => {
  return http.post('/trade-server/api/member/order/submit', params)
}

// 发起支付
export const reqLaunchPay = (params) => {
  return http.post('/trade-server/api/member/order/to-pay', params)
}

// 检查订单支付状态
export const reqCheckOrderPayStatus = (params) => {
  return http.get('/trade-server/api/member/order/check-order-pay', { params })
}

// 修改订单
export const reqModifyOrder = (params) => {
  return http.post('/trade-server/api/member/order/change-contact', params)
}
