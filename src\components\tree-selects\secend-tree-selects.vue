<template>
  <div>
    <van-tree-select
      class="first-select"
      height="3.85rem"
      v-model:main-active-index="activeIndex"
      :items="formData.allDatas"
      @click-nav="firstSelect"
    >
      <template #content>
        <van-checkbox-group v-model="checked" checked-color="#ff6445" ref="checkboxGroup">
          <van-checkbox
            class="checkbox-item"
            v-for="(item, index) in formData.secendList"
            :key="item"
            :name="item.id"
            :class="{ 'select-cell': checked.includes(item.id) }"
            label-position="left"
            shape="square"
            :ref="(el) => (checkboxRefs[index] = el)"
            @click="toggle(index, item)"
            >{{ item.text }}</van-checkbox
          >
        </van-checkbox-group>
      </template>
    </van-tree-select>
  </div>
</template>

<script setup>
  import { ref, watch, reactive, onMounted } from 'vue'

  const props = defineProps({
    dataList: {
      type: Array,
      default: () => [],
    },
    clear: {
      type: Boolean,
      default: false,
    },
  })
  // 单个 ref
  watch(
    () => props.clear,
    () => {
      clear()
    },
  )

  const activeIndex = ref(0)

  // 多选框选中数据
  const checked = ref([])
  const checkboxRefs = ref([])
  const checkboxGroup = ref(null)

  // watch(
  //   () => [...checked.value],
  //   (nowList) => {
  //     formData.secondCheckedId = nowList.map((item) => item.id);
  //   }
  // );

  const formData = reactive({
    // secondCheckedId: [],
    isCurrentFirstNav: 0,
    allReturnObj: [],
    selectList: [],
    secendList: [],
    allDatas: [],
  })

  const emit = defineEmits(['selectChange'])
  onMounted(() => {
    formData.allDatas = props.dataList
    formData.secendList = formData.allDatas[0].children
  })
  // 一级菜单
  const firstSelect = (index) => {
    // 切换一级菜单，清空其余一级选中数据
    if (formData.isCurrentFirstNav !== index) {
      formData.allReturnObj['townsList'] = []
      emit('selectChange', formData.allReturnObj)
    }
    formData.secendList = formData.allDatas[index].children
    checkboxGroup.value.toggleAll(false)
    formData.isCurrentFirstNav = index
    formData.allReturnObj['county'] = formData.allDatas[index].adCode

    // checked.value = formData.allDatas[index].children;
    // formData.allDatas[index].isAllSelect = true;
    // formData.allReturnObj["townsList"] = checked.value;
    // emit("selectChange", formData.allReturnObj);
    emit('selectChange', formData.allReturnObj)
  }

  // 二级菜单
  const toggle = (index) => {
    if (index === 0) {
      // checkboxGroup.value.toggleAll(formData.allDatas[activeIndex.value].isAllSelect);
      checked.value = []
      checked.value.push('0')
    } else if (checked.value.length > 1 && checked.value.includes('0')) {
      checked.value.splice(checked.value.indexOf('0'), 1)
    }
    console.log(checked.value, 'checked.value')
    formData.allReturnObj['county'] = formData.allDatas[activeIndex.value]
    formData.allReturnObj['townsList'] = checked.value
    // console.log(checked.value, "选中");
    emit('selectChange', formData.allReturnObj)
  }

  const clear = () => {
    checked.value = []
    activeIndex.value = 0
    formData.selectList = null
  }
</script>

<style scoped lang="scss">
  .first-select {
    width: 100vw;
    // box-shadow: 0rem 0.02rem 0.1rem 0rem rgba(0, 0, 0, 0.1);
  }
  :deep(.van-popup--center) {
    top: auto;
    left: auto;
    transform: translate(0%, 0%);
  }
  :deep(.van-sidebar-item--select:before) {
    display: none;
  }
  :deep(.van-tree-select__nav-item) {
    padding: 0.1rem;
  }
  :deep(.van-tree-select__nav) {
    width: 1.35rem;
  }
  :deep(.van-tree-select__nav),
  :deep(.van-sidebar-item) {
    background-color: #fff;
    flex: none;
  }
  :deep(.van-checkbox__icon) {
    height: 0.7em;
  }
  :deep(.van-icon) {
    width: 0.15rem;
    height: 0.15rem;
    line-height: 1;
    font-size: 0.7em;
  }
  :deep(.van-sidebar-item) {
    background-color: #fff;
    border-right: 0.01rem solid #f5f5f5;
  }
  :deep(.van-sidebar-item--select),
  :deep(.van-sidebar-item--select:active) {
    color: #ff6445ff;
    font-weight: 600;
    background: #fff7f5ff;
  }
  .checkbox-item {
    padding: 0.1rem 0.3rem;
    width: 100%;
    height: 0.4rem;
    display: flex;
    justify-content: space-between;
  }
  .select-cell {
    background-color: #fff7f5;
  }
</style>
