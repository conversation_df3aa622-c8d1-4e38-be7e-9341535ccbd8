<template>
  <page :title="$route.meta?.title" @pageshow="onPageShow">
    <template #page>
      <div class="page-content page-bg-white">
        <search-head class="search" ref="searchRef" @search="onSearch" />
        <van-sticky offset-top="0.44rem">
          <filter-menu ref="filterRef" @change="onFilterChange" />
        </van-sticky>
        <div class="search-list-warp">
          <van-list
            class="search-list"
            :immediate-check="false"
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoadMore"
          >
            <coach
              v-for="(item, index) in searchResult"
              :key="index"
              :coachData="item"
              @trainerDetails="toCoachDetails"
            />
          </van-list>
          <div v-show="emptyShow" class="empty-warp">
            <div class="empty-image">
              <img src="../../assets/images/empty.png" alt="empty" />
            </div>
            <p class="empty-description">
              当前城市<span style="color: #ffb55e">「{{ selectedCity.city }}」</span>未找到匹配教练
            </p>
            <p class="empty-tip">你可以换个关键词试试或联系客服</p>
            <button class="relation" @click="onOpenPopup">联系客服</button>
          </div>
        </div>
        <feedback-notice-bar class="notice-bar" v-if="noticeBarShow" />
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'searchResult' }
</script>

<script setup>
  import { ref, onMounted, computed } from 'vue'
  import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
  import { isIOS, throttle } from '@/utils'
  import { getCoachList } from '@/api/coach-server'
  import gm from '@/components/gm-popup'
  import { localProxyStorage } from '@/utils/storage'
  import { useEventListener } from '@vant/use'
  import useKeepAliveStore from '@/store/keepAlive'

  import SearchHead from './components/SearchHead'
  import FilterMenu from './components/FilterMenu'
  import Coach from '@/views/common/components/coach-info'
  import FeedbackNoticeBar from '@/views/help/components/FeedbackNoticeBar'

  const route = useRoute()
  const router = useRouter()
  const searchRef = ref(null)
  const filterRef = ref(null)
  const loading = ref(false)
  const finished = ref(false)
  const searchResult = ref([])
  const emptyShow = ref(false)
  const noticeBarShow = ref(false)
  const selectedCity = ref(localProxyStorage.selectedGeolocation || {})
  const keepAliveStore = useKeepAliveStore()

  const initQueryParams = () => {
    return {
      nameLikeSearch: '', // 搜索关键字
      pageNum: 0,
      pageSize: 8,
    }
  }

  const queryParams = ref(initQueryParams())

  const finishedText = computed(() => {
    return searchResult.value.length === 0 ? '' : '-没有更多了-'
  })

  const onOpenPopup = () => {
    gm.open({
      title: '联系客服',
      desc: '添加企业微信，在线联系客服',
    })
  }

  const onLoadMore = () => {
    queryParams.value.pageNum += 1
    let params = { ...queryParams.value }
    getCoachList(params).then((res) => {
      const { data } = res
      searchResult.value = searchResult.value.concat(data.records)

      loading.value = false
      emptyShow.value = searchResult.value.length === 0

      if (data.records.length === 0 || data.records.length < queryParams.value.pageSize) {
        finished.value = true
      }
    })
  }

  const onSearch = (keyword) => {
    loading.value = false
    finished.value = false
    emptyShow.value = false
    searchResult.value.length = 0
    queryParams.value = Object.assign(queryParams.value, initQueryParams())
    queryParams.value.nameLikeSearch = keyword

    // 更新路由的 keyword
    router.replace({ name: 'searchResult', query: { keyword } })
    onLoadMore()
  }

  const onFilterChange = (filterParams) => {
    loading.value = false
    finished.value = false
    emptyShow.value = false
    searchResult.value.length = 0
    queryParams.value.pageNum = 0
    queryParams.value = Object.assign(queryParams.value, filterParams)
    onLoadMore()
  }

  const onPageShow = (event) => {
    if (isIOS()) {
      if (event.persisted) {
        window.location.reload()
      }
    }
  }
  const toCoachDetails = (item) => {
    router.push({
      path: '/coach/details/' + item.id,
    })
  }

  onMounted(() => {
    const { keyword, classifyIds, sort } = route.query
    const classifyAry = classifyIds ? classifyIds.split(',').map((id) => Number(id)) : []

    // 赋值
    searchRef.value.setInputValue(keyword)
    filterRef.value.setClassifyValue(classifyAry)
    filterRef.value.setSortValue(sort)

    queryParams.value = Object.assign(queryParams.value, filterRef.value.formData)
    queryParams.value.nameLikeSearch = keyword
    queryParams.value.firstCategoriesId = classifyAry?.[0]
    queryParams.value.secondCategoriesId = classifyAry?.[1]
    queryParams.value.thirdlyCategoriesId = classifyAry?.[2]
    if (queryParams.value.city === '000000') {
      queryParams.value.city = queryParams.value.county
      queryParams.value.county = null
    }

    onLoadMore()
  })

  onBeforeRouteLeave((to) => {
    let pages = ['coachDetails']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('searchResult')
    }
  })

  const setNoticeBarShow = () => {
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
    if (scrollTop >= 1050) {
      noticeBarShow.value = true
    }
  }

  const onScroll = throttle(setNoticeBarShow, 100)

  useEventListener('scroll', onScroll)
</script>

<style lang="scss" scoped>
  .search {
    background-color: #fff;
  }

  .search-list-warp {
    padding: 0.1rem 0.13rem;
    background-color: #fff;

    .search-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: stretch;

      :deep(.van-list__finished-text) {
        width: 3.75rem;
      }

      :deep(.van-list__loading) {
        width: 3.75rem;
      }
    }
  }

  .empty-warp {
    padding-top: 1.45rem;
    text-align: center;

    .empty-image {
      text-align: center;

      img {
        width: 0.98rem;
        height: 1rem;
      }
    }

    .empty-description {
      margin-top: 0.1rem;
      font-size: 0.12rem;
      color: #666666;
    }

    .empty-tip {
      margin-top: 0.06rem;
      font-size: 0.12rem;
      color: #b2b1b7;
    }

    .relation {
      width: 1.8rem;
      height: 0.4rem;
      margin-top: 0.76rem;
      background: #ff9b26;
      box-shadow: 0 0.02rem 0.04rem 1px rgba(245, 176, 76, 0.1);
      border-radius: 23px;
      font-size: 0.16rem;
      font-weight: 600;
      color: #ffffff;
    }
  }

  .notice-bar {
    position: fixed;
    left: var(--window-left);
    right: var(--window-right);
    bottom: 0;
    //bottom: constant(safe-area-inset-bottom);
    //bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 20;
  }
</style>
