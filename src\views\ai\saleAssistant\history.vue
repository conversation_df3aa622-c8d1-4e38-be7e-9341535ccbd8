<template>
  <Page title="历史对话">
    <template #page>
      <div class="list">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="list.length < 1 ? '' : '- 已经到底啦 -'"
          @load="onLoad"
        >
          <div
            v-for="item in list"
            :key="item.id"
            class="list-item"
            @click="$router.push(`/ai/sale-assistant?conversationId=${item.conversationId}`)"
          >
            <div class="flex justify-between align-center">
              <h3>{{ item.title }}</h3>
              <div class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            </div>
            <p>{{
              item.botId === 'recommend_coachs' ? '根据用户画像，推荐以下教练~…' : item.content
            }}</p>
          </div>
        </van-list>
      </div>
    </template>
  </Page>
  <div></div>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import Page from '@/layout/components/Page'
  import { getConversationList } from '@/api/ai-server'
  import dayjs from 'dayjs'
  import { useRoute } from 'vue-router'

  const { query } = useRoute()

  const loading = ref(false)
  const finished = ref(false)
  const list = ref([])
  const params = reactive({
    pageNum: 0,
    pageSize: 10,
  })
  const onLoad = () => {
    params.pageNum += 1
    loading.value = true
    getConversationList({ ...params, ...query })
      .then((res) => {
        console.log(res)
        const { data } = res
        list.value = list.value.concat(data.list)
        // 加载状态结束
        loading.value = false

        // 数据全部加载完成
        if (data.list.length === 0 || data.list.length < params.pageSize) {
          console.log('数据全部加载完成')
          finished.value = true
        }
      })
      .catch(() => {
        // 数据全部请求完成
        loading.value = false
        finished.value = true
      })
  }
</script>
<style lang="scss" scoped>
  .list {
    background-color: #fff;
    padding-top: 0.12rem;
    min-height: calc(100vh - 0.44rem);
  }
  .list-item {
    margin-bottom: 0.12rem !important;
    // width: 100%;
    padding: 0.16rem;
    background: #f5f7fa;
    border-radius: 0.12rem;
    margin: 0 0.15rem;
    box-sizing: border-box;
    h3 {
      font-weight: 600;
      font-size: 0.15rem;
      color: #333333;
      line-height: 0.21rem;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      max-width: 1.76rem;
    }

    .time {
      font-size: 0.12rem;
      color: #999999;
    }
    p {
      margin-top: 0.08rem;
      font-size: 0.12rem;
      color: #606266;
      line-height: 0.17rem;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
  }
</style>
