<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="page-content page-bg-white">
        <div class="news">
          <van-sticky offset-top="0.44rem">
            <van-dropdown-menu class="dropdown-menu" active-color="#ff6445" duration="0">
              <van-dropdown-item
                :title="classifyMenuTitle"
                :title-class="classifyTitleClass"
                ref="classifyRef"
                @open="handleClassifyMenuOpen"
                @close="handleClassifyMenuClose"
              >
                <classify-cascader v-model="classifyValue" @change="onClassifyChange" />
              </van-dropdown-item>
            </van-dropdown-menu>
          </van-sticky>
          <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
          >
            <div
              class="flex news-item"
              v-for="item in teachingArticles"
              :key="item.id"
              @click="onViewDetails(item)"
            >
              <div class="news-item-l">
                <p
                  :class="{
                    'news-title': true,
                    'news-img-title': item.image,
                  }"
                >
                  {{ item.title }}
                </p>
                <div class="news-info flex">
                  <div>
                    <span class="type-label">文章</span>
                    <span class="author">{{ ellipsis(item.realName) }}</span>
                    <span class="news-time"> {{ item.releaseTime.substring(0, 10) }}</span>
                    <span v-if="item.hits < 999" class="pageviews">{{ item.hits }}浏览</span>
                    <span v-else class="pageviews">999+浏览</span>
                  </div>
                </div>
              </div>
              <div v-if="item.image" class="news-item-r">
                <img :src="getCoverImage(item.imageList)" v-error-img alt="" />
              </div>
            </div>
          </van-list>
          <empty class="empty" v-show="emptyShow" description="暂无内容" top="1.96rem" />
        </div>
      </div>
    </template>
  </page>
</template>

<script>
  export default { name: 'newsList' }
</script>

<script setup>
  import { ref, computed } from 'vue'
  import { onBeforeRouteLeave, useRouter } from 'vue-router'
  import { getOssURL } from '@/common'
  import { getLatestNewsList } from '@/api/generic-server'
  import useKeepAliveStore from '@/store/keepAlive'
  import ClassifyCascader from '@/views/search/components/ClassifyCascader'
  import Empty from '@/components/empty'

  const router = useRouter()
  const loading = ref(false)
  const finished = ref(false)
  const emptyShow = ref(false)
  const classifyMenuTitle = ref('分类')
  const classifyRef = ref(null)
  const classifyValue = ref([])
  let beforeClassifyValue = []
  const selectedClassifyItems = ref([])
  const teachingArticles = ref([])
  const keepAliveStore = useKeepAliveStore()

  const finishedText = computed(() => (teachingArticles.value.length > 0 ? '-没有更多了-' : ''))

  const initQueryParams = () => {
    return {
      pageNum: 0,
      pageSize: 10,
      firstCategoriesId: '',
      secondCategoriesId: '',
      thirdlyCategoriesId: '',
    }
  }
  const queryParams = ref(initQueryParams())

  const classifyTitleClass = computed(() => {
    return classifyValue.value.length !== 0 ? 'dropdown-title-active' : ''
  })

  const onCriteriaQuery = () => {
    loading.value = false
    finished.value = false
    teachingArticles.value.length = 0
    queryParams.value = initQueryParams()

    queryParams.value.pageNum = 1
    queryParams.value.firstCategoriesId = classifyValue.value[0] || ''
    queryParams.value.secondCategoriesId = classifyValue.value[1] || ''
    queryParams.value.thirdlyCategoriesId = classifyValue.value[2] || ''

    getTeachingArticles()
  }

  const onClassifyChange = (value) => {
    selectedClassifyItems.value = value.selected
    if (value.selected.length === 3) {
      classifyRef.value?.toggle(false)
    }
  }

  const handleClassifyMenuOpen = () => {
    beforeClassifyValue = classifyValue.value
  }

  const handleClassifyMenuClose = () => {
    if (beforeClassifyValue.toString() === classifyValue.value.toString()) return

    if (selectedClassifyItems.value.length === 0) {
      classifyMenuTitle.value = '分类'
    } else {
      let lastItem = selectedClassifyItems.value[selectedClassifyItems.value.length - 1]
      classifyMenuTitle.value = lastItem.label
    }

    onCriteriaQuery()
  }

  const getTeachingArticles = () => {
    getLatestNewsList(queryParams.value).then((res) => {
      const { data } = res
      teachingArticles.value = teachingArticles.value.concat(data.records)
      emptyShow.value = teachingArticles.value.length === 0

      loading.value = false
      if (data.records.length === 0 || data.records.length < queryParams.value.pageSize) {
        finished.value = true
      }
    })
  }

  const onLoad = () => {
    queryParams.value.pageNum += 1
    getTeachingArticles()
  }

  const ellipsis = (item) => {
    if (!item) return
    if (item.split('').length > 5) {
      return item.substring(0, 5) + '...'
    } else {
      return item
    }
  }

  const onViewDetails = (article) => router.push('/news-details/' + article.id)

  const getCoverImage = (images) => {
    if (Array.isArray(images) && images.length > 0) {
      return getOssURL(images[0])
    }
    return require('../../assets/images/default-img.png')
  }

  onBeforeRouteLeave((to) => {
    let pages = ['newsDetails']
    if (!pages.includes(to.name)) {
      keepAliveStore.removeKeepAlive('newsList')
    }
  })
</script>
<style scoped lang="scss">
  @import '~@/styles/mixins/mixins.scss';
  @include Icon('eye2', 0.16rem, 0.12rem) {
    vertical-align: text-top;
    margin-right: 0.06rem;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .news {
    width: 3.75rem;
    background-color: #fff;

    .news-item {
      padding: 0.15rem;
      border-bottom: 1px solid #efefef;
      align-items: stretch;

      .news-item-r {
        margin-left: 0.06rem;
        width: var(--i-article-cover-w);
        height: var(--i-article-cover-h);
        flex-shrink: 0;
      }

      .news-item-r img {
        width: var(--i-article-cover-w);
        height: var(--i-article-cover-h);
        border-radius: 0.04rem;
        border: 0.01rem solid #eee;
        object-fit: cover;
      }

      .news-item-l {
        position: relative;
        // height: 0.78rem;
        flex: 1;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        justify-content: space-between;

        .news-title {
          // height: 0.47rem;
          font-size: 0.15rem;
          color: #414141;
          line-height: 0.2rem;
          margin-bottom: 0.08rem;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .news-img-title {
          height: 0.4rem;
        }

        .news-info {
          justify-content: space-between;
        }
        .type-label {
          margin-right: 0.04rem;
          padding: 0.01rem 0.04rem;
          background-color: #ededed;
          font-size: 0.1rem;
          color: #616568;
          border-radius: 0.02rem;
        }
        .author,
        .news-time {
          font-size: 0.1rem;
          color: #b2b1b7;
          line-height: 0.17rem;
          margin-right: 0.1rem;
        }

        .pageviews {
          font-size: 0.1rem;
          color: #b2b1b7;
        }
      }
    }
  }

  .dropdown-menu {
    :deep(.van-dropdown-menu__bar) {
      height: 0.4rem;
      box-shadow: none;
    }

    :deep(.van-dropdown-menu__item) {
      justify-content: flex-start;
      padding-left: 0.1rem;
    }
  }
</style>
