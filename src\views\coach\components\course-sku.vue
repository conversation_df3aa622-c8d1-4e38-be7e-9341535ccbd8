<template>
  <ijl-action-sheet
    class="sku-popup"
    v-model="showPop"
    :showConfirmButton="false"
    confirmText="立即购买"
    @confirm="submit"
    @cancel="onClose"
    @close="onClose"
  >
    <van-icon class="close" name="cross" @click="onClose" />
    <div class="course-sku">
      <div class="course-header">
        <div class="header-l">
          <van-image
            class="goods-img"
            width="0.51rem"
            height="0.51rem"
            fit="cover"
            :src="getOssURL(goods.imgUrl)"
          />
        </div>
        <div class="header-r">
          <div class="price">
            <span class="f20">¥{{ goods.price }}</span>
            <span v-if="!goods.isPackagePrice" class="f12">/课时</span>
            <span v-if="isShowVirtualPrice" class="lineation-price">
              ¥{{ goods.virtualPrice }}
            </span>
          </div>
          <div>{{ fieldValue }}</div>
        </div>
      </div>

      <sku class="sku-block" v-if="skuConfig" :sku-config="skuConfig" @change="onSkuChange" />
    </div>
  </ijl-action-sheet>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import { Toast } from 'vant'
  import Sku from '@/components/sku'
  import { reqCoachSkuConfig } from '@/api/coach-server'
  import router from '@/router'
  import { isWeChat } from '@/utils'
  import { baseURL } from '@/config'
  import { getOssURL, isLogin, toLogin } from '@/common'

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    // 教练id
    coachId: {
      type: [String, Number],
      default: null,
    },
    defaultGoods: {
      type: Object,
      default: () => {},
    },
    defaultImg: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['close', 'update:show', 'change'])

  const showPop = ref(props.show)
  const skuConfig = ref(null)
  const selectedSku = ref(null)

  const fieldValue = computed(() => {
    if (!selectedSku.value || selectedSku.value?.length === 0) return '请选择授课方式和课时包'
    return '已选：' + selectedSku.value.map((spec) => `"${spec.name}"`).join('，')
  })

  const goods = ref({
    imgUrl: props.defaultGoods.imgUrl,
    price: props.defaultGoods.price,
    virtualPrice: props.defaultGoods.virtualPrice,
    isPackagePrice: props.defaultGoods.isPackagePrice,
  })

  // 是否展示划线价格
  const isShowVirtualPrice = computed(() => {
    if (!goods.value.virtualPrice) return false
    return goods.value.virtualPrice >= 0
  })

  watch(
    () => props.show,
    (newVal) => (showPop.value = newVal),
    { immediate: true },
  )

  const getSkuConfig = () => {
    let params = { coachId: props.coachId }
    reqCoachSkuConfig(params).then((res) => {
      const { data } = res
      skuConfig.value = { specs: [], list: [] }
      // 数据格式转换
      data.coachSkuMenuVOS.forEach((spec) => {
        skuConfig.value.specs.push({
          k: spec.menuName,
          k_s: spec.menuNo,
          list: spec.menuPropertyList,
        })
      })

      data.coachSkuSettingsVOS.forEach((sku) => {
        sku.id = sku.settingsId
        delete sku.settingsId
        skuConfig.value.list.push(sku)
      })

      console.log(skuConfig, 'skuConfig')
    })
  }

  const submit = () => {
    // 非微信环境，直接去跳去提示页
    if (!isWeChat()) {
      let link = encodeURIComponent(baseURL + location.pathname + location.search)
      router.push({
        name: 'browserPay',
        query: {
          link,
        },
      })
      return
    }

    if (!isLogin()) {
      toLogin()
      return
    }

    if (!selectedSku.value || selectedSku.value?.length <= 1) {
      Toast('请选择授课方式和课时包')
      return
    }

    const s1 = selectedSku.value.find((item) => item.specField === 's1') // 授课方式
    const s2 = selectedSku.value.find((item) => item.specField === 's2') // 课时数量
    router.push({
      name: 'payCourse',
      query: {
        classesId: s1.id,
        coachId: props.coachId,
        quantity: s2.value,
      },
    })

    onClose()
  }

  const onClose = () => {
    showPop.value = false
    emit('update:show', false)
  }

  const onSkuChange = (sku) => {
    console.log(sku, '选择的sku')
    selectedSku.value = sku

    const s1 = sku.find((item) => item.specField === 's1') // 授课方式
    const s2 = selectedSku.value.find((item) => item.specField === 's2') // 课时数量

    if (sku.length === 2) {
      goods.value.price = Number((s1.price * s2.value).toFixed(2))
      goods.value.virtualPrice = Number((s1.virtualPrice * s2.value).toFixed(2))
      goods.value.imgUrl = s1.imgUrl ? s1.imgUrl : props.defaultImg
      goods.value.isPackagePrice = true
    } else {
      if (s1) {
        goods.value.price = s1.price
        goods.value.virtualPrice = s1.virtualPrice
        goods.value.imgUrl = s1.imgUrl ? s1.imgUrl : props.defaultImg
        goods.value.isPackagePrice = false
      }
    }

    emit('change', fieldValue.value, goods.value)
  }

  const init = () => {
    getSkuConfig()
  }

  init()
</script>

<style scoped lang="scss">
  .course-sku {
    margin-top: 0.35rem;
    padding: 0 0.15rem 0.15rem;
  }
  .course-header {
    display: flex;

    .header-l {
      margin-right: 0.15rem;
      flex-shrink: 0;

      .goods-img {
        background-color: #eee;
      }
    }

    .price {
      font-size: 0.16rem;
      font-weight: bold;
      color: #ff6445;
      letter-spacing: 0.5px;

      .f20 {
        font-size: 0.2rem;
      }

      .f12 {
        font-size: 0.12rem;
      }

      .lineation-price {
        margin-left: 0.06rem;
        font-size: 0.12rem;
        color: #b2b1b7;
        font-weight: 400;
        text-decoration: line-through;
      }
    }
  }
  .header-tips {
    margin-left: 0.1rem;
    color: #b2b1b7;
  }

  .sku-block {
    margin-top: 0.22rem;
  }
  :deep(.van-action-sheet__content) {
    position: relative;
  }
  .close {
    font-size: 0.2rem;
    position: absolute;
    top: -0.2rem;
    right: 0.15rem;
    color: #d8d8d8;
  }
</style>
