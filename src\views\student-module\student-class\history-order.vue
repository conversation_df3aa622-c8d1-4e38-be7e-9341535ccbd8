<template>
  <page :title="$route.meta?.title">
    <template #page>
      <div class="min-height-100">
        <van-list
          class="goods-list"
          v-model:loading="loading"
          :finished="finished"
          finished-text="-没有更多了-"
          @load="onLoad"
        >
          <div v-for="item in buyList" :key="item" class="goods-item" @click="toOrderDetail(item)">
            <div class="goods-top">
              <van-row class="buy-other" justify="space-between" align="center">
                <van-col class="buy-time">{{ item.createTime }}</van-col>
                <van-col>
                  <span class="remain-quantity">剩余{{ item.showRemainQuantity }}个课时</span>
                  <span class="goods-state">{{ item.orderStatus.statusName }}</span>
                </van-col>
              </van-row>
              <div class="buy-details">
                <van-image
                  class="goods-images"
                  round
                  fit="cover"
                  width="0.74rem"
                  height="0.74rem"
                  :src="getOssURL(item.orderItemList[0].imageUrl)"
                />
                <div class="goods-info">
                  <div>
                    <van-row justify="space-between" align="center">
                      <van-col class="goods-name omit">
                        <span>{{ item.orderItemList[0].spuName }}｜</span>
                        <span>{{ item.orderItemList[0].skuName }}</span>
                      </van-col>
                      <van-col class="buy-price">¥{{ item.orderItemList[0].totalAmount }}</van-col>
                    </van-row>
                  </div>
                  <div class="goods-spec">
                    授课方式：{{ item.orderItemList[0].teachingWay.typeName }}
                  </div>
                  <div class="buy-number">课时数：{{ item.orderItemList[0].quantity }}个课时</div>
                  <div
                    v-if="item.orderItemList[0].afterSaleStatus.status !== 'NONE'"
                    class="buy-state"
                  >
                    {{ item.orderItemList[0].afterSaleStatus.statusName }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </div>
    </template>
  </page>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { getBuyHistory } from '@/api/trade-server'
  import { useRoute, useRouter } from 'vue-router'
  import { getOssURL } from '@/common'

  const route = useRoute()
  const router = useRouter()
  const coachUserId = route.query.coachUserId
  const loading = ref(false)
  const finished = ref(false)
  const buyList = ref([])
  const pagination = reactive({
    pageNum: 0,
    pageSize: 12,
  })

  const onLoad = () => {
    pagination.pageNum += 1
    let params = { heId: coachUserId, ...pagination }
    getBuyHistory(params)
      .then((res) => {
        let { data } = res
        buyList.value = buyList.value.concat(data)
        loading.value = false
        // 数据全部加载完成
        if (data.length === 0 || data.length < pagination.pageSize) {
          finished.value = true
        }
      })
      .catch(() => {
        loading.value = false
        finished.value = true
      })
  }

  const toOrderDetail = (item) => {
    router.push({
      name: 'studentOrderDetails',
      query: {
        orderId: item.id,
      },
    })
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';
  @include Icon('arrow-right2', 0.32rem, 0.32rem);
  .flex {
    display: flex;
    align-items: center;
  }

  .goods-list {
    padding: 0 0.08rem;

    .goods-item {
      background: #fff;
      padding: 0 0.15rem;
      margin: 0.08rem 0;
      border-radius: 0.06rem;
    }

    .goods-top {
      border-bottom: 1px solid #eeeeee;
    }

    .buy-other {
      padding: 0.08rem 0;
    }

    .buy-time {
      font-size: 12px;
      color: #b2b1b7;
    }

    .goods-state {
      color: #ff9b26;
    }

    .remain-quantity {
      font-size: 0.12rem;
      color: #b2b1b7;
      margin-right: 0.1rem;
    }

    .buy-details {
      display: flex;
      padding-bottom: 0.1rem;
    }

    .goods-images {
      width: 0.74rem;
      height: 0.74rem;
      border-radius: 0.06rem;
    }

    .goods-info {
      margin-left: 0.1rem;
      flex: 1;
    }

    .goods-name {
      width: 1.8rem;
      font-size: 0.14rem;
      font-weight: 600;
      color: #1a1b1d;
    }

    .buy-price {
      font-size: 0.14rem;
      color: #1a1b1d;
    }

    .goods-spec,
    .buy-number {
      font-size: 0.12rem;
      color: #616568;
      margin-top: 0.04rem;
    }

    .buy-state {
      font-size: 0.12rem;
      color: #ff6445;
      text-align: right;
    }
  }

  .news-item {
    margin-bottom: 0.1rem;
    padding: 0.08rem 0.15rem 0.15rem 0.15rem;
    background-color: #fff;
    border-radius: 0.06rem;
    .header {
      justify-content: space-between;
      font-size: 0.12rem;
      .time {
        color: #b2b1b7;
      }
      .class-state {
        span {
          color: #b2b1b7;
        }
        p {
          margin-left: 0.1rem;
          font-size: 0.14rem;
          color: #ff9b26;
        }
      }
    }
    .content {
      display: flex;
      .content-l {
        flex-shrink: 0;
        width: 0.74rem;
        height: 0.74rem;

        img {
          width: 0.74rem;
          height: 0.74rem;
          border-radius: 0.06rem;
        }
      }
      .content-r {
        position: relative;
        margin-left: 0.1rem;
        width: 2.58rem;
        height: 0.74rem;
        justify-content: space-between;
        font-size: 0.14rem;
        color: #1a1b1d;
        flex-wrap: wrap;
        .coach-info {
          width: 2.45rem;
          display: flex;
          justify-content: space-between;
          font-weight: 600;

          p {
            width: 1.9rem;
            flex-shrink: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .control {
          width: 2rem;
          padding-top: 0.03rem;
          font-size: 0.12rem;
          color: #616568;
        }
        .refund {
          position: absolute;
          right: 0;
          bottom: 0;
          text-align: center;
          width: 0.75rem;
          height: 0.26rem;
          line-height: 0.26rem;
          border-radius: 0.23rem;
          font-size: 0.12rem;
          color: #616568;
          border: 0.01rem solid #dddddd;
        }
        .refunding {
          position: absolute;
          right: 0;
          bottom: 0;
          font-size: 0.12rem;
          color: #ff6445;
        }
      }
    }
  }
</style>
