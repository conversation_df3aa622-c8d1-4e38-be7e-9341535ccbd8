<template>
  <ijl-action-sheet
    title="处理退款"
    v-model="actionSheetShow"
    @confirm="onConfirm"
    @cancel="onCancel"
    @close="onClose"
  >
    <div class="apply-info">
      <div class="item">
        <div class="label">申请学员：</div>
        <div class="value">{{ data.studentName }}</div>
      </div>
      <div class="item">
        <div class="label">申请课时：</div>
        <div class="value showy">{{ data.refundQuantity }}个课时</div>
      </div>
      <div class="item">
        <div class="label">申请退款：</div>
        <div class="value showy">¥{{ data.refundAmount }}</div>
      </div>
      <div class="item">
        <div class="label">申请时间：</div>
        <div class="value">{{ data.applyTime }}</div>
      </div>
    </div>
    <div class="form">
      <div class="form-item">
        <div class="form-item-label">处理申请：</div>
        <div class="form-item-content">
          <div class="radio">
            <div
              :class="['radio-item', { 'radio-checked': form.pass === 0 }]"
              @click="handleRadioCheck(0)"
            >
              <i class="icon reject"></i>
              <div class="label">拒绝</div>
            </div>
            <div
              :class="['radio-item', { 'radio-checked': form.pass === 1 }]"
              @click="handleRadioCheck(1)"
            >
              <i class="icon agree"></i>
              <div class="label">同意</div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="form.pass === 0" class="form-item">
        <div class="form-item-label">拒绝理由：</div>
        <div class="form-item-content">
          <van-field
            class="textarea"
            v-model="form.refuseDesc"
            rows="3"
            autosize
            type="textarea"
            maxlength="100"
            placeholder="请输入拒绝理由"
            show-word-limit
          />
        </div>
      </div>
    </div>
  </ijl-action-sheet>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue'
  import { reqAgreeRefund, reqRejectRefund } from '@/api/coach-worktable'
  import { Toast } from 'vant'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {},
    },
  })

  const emit = defineEmits(['update:modelValue', 'agree', 'reject'])

  const actionSheetShow = ref(false)
  const loading = ref(null)

  watch(
    () => props.modelValue,
    (newValue) => (actionSheetShow.value = newValue),
    { immediate: true },
  )

  const form = reactive({
    pass: 1,
    refuseDesc: '',
  })

  const handleRadioCheck = (val) => {
    form.pass = val
  }

  // 同意退款
  const handleAgreeRefund = () => {
    let params = { id: props.data.orderId }
    reqAgreeRefund(params).then((res) => {
      emit('agree', res.data)
      loading.value.clear()
      Toast('处理成功')
      onClose()
    })
  }

  const handleRejectRefund = () => {
    if (form.refuseDesc.trim() !== '') {
      let params = {
        id: props.data.orderId,
        explain: form.refuseDesc,
      }
      reqRejectRefund(params).then((res) => {
        emit('reject', res.data)
        loading.value.clear()
        Toast('处理成功')
        onClose()
      })
    } else {
      Toast('拒绝理由不能为空')
    }
  }

  const onConfirm = () => {
    loading.value = Toast.loading({
      message: '处理中...',
      overlay: true,
      forbidClick: true,
    })

    if (form.pass) {
      handleAgreeRefund()
    } else {
      handleRejectRefund()
    }
  }

  const onCancel = () => {
    emit('update:modelValue', false)
  }

  const onClose = () => {
    emit('update:modelValue', false)
  }
</script>

<style lang="scss" scoped>
  .apply-info {
    margin: 0 0.15rem;
    border-bottom: 1px solid #eeeeee;

    .item {
      display: flex;
      margin-bottom: 0.1rem;

      .label {
        //width: 0.8rem;
        color: #1a1b1d;
      }

      .value {
        color: #1f1f1f;
      }

      .showy {
        color: #ff6445;
      }
    }
  }

  .form {
    margin: 0.12rem 0.15rem 0 0.15rem;

    .form-item-label {
      font-size: 0.16rem;
      font-weight: 600;
      color: #1f1f1f;
      margin-bottom: 0.08rem;
    }

    .form-item-content {
      margin-bottom: 0.14rem;
    }

    .radio {
      .radio-item {
        display: inline-flex;
        margin-right: 0.12rem;
        border-radius: 0.04rem;
        border: 1px solid #eeeeee;
        padding: 0.08rem 0.15rem;
      }

      .radio-checked {
        border: 1px solid #ffa524;
        background: #fff6e9;
        color: #ff9b26;
      }

      .icon {
        width: 0.21rem;
        height: 0.21rem;
        margin-right: 0.08rem;
      }

      .reject {
        background: url('../../../../assets/images/coach-worktable/icon-checked-false.png')
          no-repeat;
        background-size: 100%;
      }

      .agree {
        background: url('../../../../assets/images/coach-worktable/icon-checked-true.png') no-repeat;
        background-size: 100%;
      }
    }

    .textarea {
      border: 1px solid #eeeeee;

      :deep(.van-field__control) {
        font-size: 0.14rem;
      }
    }
  }
</style>
