<template>
  <van-swipe class="swipe" indicator-color="white">
    <van-swipe-item v-for="(item, index) in swipes" :key="index">
      <template v-if="item.type === 'image'">
        <img @click="preview(index)" class="swipe-img" :src="item.url" alt="" />
      </template>
      <template v-if="item.type === 'video'">
        <!--        <div class="swipe-video">-->
        <!--          <xg-player ref="player" :url="item.url" height="3.15rem" :ignores="['time', 'play']" />-->
        <!--        </div>-->
        <video
          controls
          webkit-playsinline
          playsinline
          x5-playsinline
          class="swipe-video"
          :src="item.url"
          :poster="item.url + '?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast,ar_auto'"
          muted
          crossorigin="anonymous"
          style="opacity: 0.99"
        />
      </template>
    </van-swipe-item>
    <van-swipe-item v-if="swipes.length === 0">
      <div class="default-img">
        <img class="logo" src="../../../assets/images/logo.png" alt="logo" />
      </div>
    </van-swipe-item>
    <template #indicator="{ active, total }">
      <div class="indicator">
        <div class="icon-img" />
        {{ active + 1 }}/{{ total }}
      </div>
    </template>
  </van-swipe>
</template>

<script setup>
  import { watch } from 'vue'
  import { ImagePreview } from 'vant'
  // import XgPlayer from "@/components/xg-player";

  let props = defineProps({
    swipes: {
      type: Array,
      default: () => [],
    },
  })

  let startIndex = 0

  const images = []

  watch(
    () => props.swipes,
    (newVal) => {
      images.length = 0
      newVal.forEach((item) => {
        if (item.type === 'image') {
          images.push(item.url)
        } else {
          startIndex = 1
          return false
        }
      })
    },
    {
      immediate: true,
    },
  )

  const preview = (index) => {
    ImagePreview(images, index - startIndex)
  }
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';

  @include Icon('img', 0.11rem, 0.09rem);

  .swipe .van-swipe-item {
    height: 3.15rem;
    background-color: #eeeeee;
  }

  .swipe {
    .swipe-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .swipe-video {
      width: 100%;
      height: 3.15rem;
      //position: relative;
      //z-index: 99;
    }
    //
    //:deep(.van-swipe__indicators) {
    //  bottom: 0.26rem;
    //}
    //
    //:deep(.van-swipe__indicator) {
    //  width: 0.04rem;
    //  height: 0.04rem;
    //  margin-right: 0.06rem;
    //}
    //
    //:deep(.van-swipe__indicator--active) {
    //  width: 0.2rem;
    //  height: 0.04rem;
    //  background: #ff9b26;
    //  border-radius: 0.02rem;
    //}
  }

  .indicator {
    position: absolute;
    right: 0.05rem;
    bottom: 0.2rem;
    padding: 0.02rem 0.05rem;
    font-size: 0.12rem;
    color: #fff;
    border-radius: 0.06rem;
    background: rgba(0, 0, 0, 0.8);
  }

  .default-img {
    height: 100%;
    display: flex;
    align-items: center;

    .logo {
      width: 1.25rem;
      height: 1.48rem;
      margin: 0 auto;
    }
  }
</style>
