<template>
  <div ref="wrapper" class="unfold" :class="{ omit: !isOpen, open: isOpen }" @click="toggle">
    <slot />
    <div v-if="showBtn" class="unfold__btn">
      <van-icon :name="isOpen ? 'arrow-up' : 'arrow-down'" />
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'

  const wrapper = ref(null)
  const isOpen = ref(true)
  const showBtn = ref(false)

  onMounted(() => {
    let wrapperHeight = Math.floor(wrapper.value.getBoundingClientRect().height) // 容器实际高度
    isOpen.value = false // 获取玩实际高度后，里面收缩

    let rootFontSize = window.getComputedStyle(document.documentElement)['fontSize']
    rootFontSize = Number(rootFontSize.substring(0, rootFontSize.length - 2))
    // 0.35 代表 0.35rem
    if (wrapperHeight > Math.floor(rootFontSize * 0.35)) {
      showBtn.value = true
    }
  })

  const toggle = () => {
    if (!showBtn.value) return
    isOpen.value = !isOpen.value
  }
</script>

<style lang="scss" scoped>
  .unfold {
    //min-height: 0.35rem;
    background: #f1f1f1;
    border-radius: 0.06rem;
    opacity: 0.7;
    padding: 0.08rem 0.3rem 0.08rem 0.08rem;
    line-height: 1.5;
    position: relative;
    transition: 0.1s;
  }

  .unfold__btn {
    position: absolute;
    top: 0.08rem;
    right: 0.1rem;
    z-index: 3;
    font-size: 0.12rem;
    color: #969799;
  }

  .open {
  }
</style>
