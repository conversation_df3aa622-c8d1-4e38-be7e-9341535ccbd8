<template>
  <div class="page wrapper">
    <div class="tip">
      <i class="icon-success"></i>
      <p class="text1">成功设置新密码！</p>
      <p class="text2">请使用新密码登录</p>
    </div>
    <button class="login-btn" @click="$router.push('/login')">登录</button>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins';

  @include Icon('success', 0.48rem, 0.48rem);

  @include Button('login-btn', 3.25rem) {
    margin-top: 0.54rem;
  }

  .wrapper {
    text-align: center;
    padding: 0.5rem 0.25rem 0 0.25rem;
  }

  .text1 {
    margin-top: 0.15rem;
    font-size: 0.16rem;
    font-weight: 600;
    color: #1a1b1d;
  }

  .text2 {
    margin-top: 0.17rem;
    color: #cccccc;
    font-size: 0.16rem;
  }
</style>
