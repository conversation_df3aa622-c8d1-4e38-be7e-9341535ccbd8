<template>
  <page :title="$route.meta.title" navigation-bar-type="transparent">
    <template #page>
      <div v-if="pageShow" class="coach min-height-100">
        <goods-swipe :swipes="swipes" />

        <div class="coach-details">
          <div class="pd15">
            <div class="coach-name">
              <div class="name">
                <div>{{ coach.realName }}</div>
                <div v-if="coach.isHot" class="icon-hot" />
              </div>
              <div v-if="!isPreviewMode" class="mark-box" @click="toMark">
                <i v-if="isMark" class="icon icon-mark" />
                <i v-else class="icon icon-unmark" />
                <div>收藏</div>
              </div>
            </div>
            <div class="coach-title">
              <span class="coach-title-name">{{ coach.teachTitle }}</span>
              <div class="pageviews">
                <span v-if="coach.readCount < 999" class="num">{{ coach.readCount || 0 }}浏览</span>
                <span v-else class="num">999+浏览</span>
                <div v-if="collectionCount">
                  <span class="split num">|</span>
                  <span v-if="collectionCount < 999" class="num"
                    >{{ collectionCount || 0 }}人气</span
                  >
                  <span v-else class="num">999+人气</span>
                </div>
              </div>
            </div>
            <div class="coach-desc" v-if="coach.teachDescription">
              {{ coach.teachDescription }}
            </div>
            <tags class="coach-tags" :list="tagList" />
          </div>

          <div class="coach-teaching-info coach-type">
            <div class="coach-teaching-item align-items-c">
              <div class="label">课时费用</div>
              <div class="value price">
                <span class="f18">¥{{ goods.price }}</span>
                <span v-if="!goods.isPackagePrice" class="f12">/课时</span>
                <span v-if="goods.virtualPrice > 0" class="Scribing"
                  >¥{{ goods.virtualPrice }}/课时</span
                >
              </div>
            </div>
            <div class="coach-teaching-item">
              <div class="label">授课方式</div>
              <div @click="showCourseSku = true" class="value">
                <p>{{ fieldValue }}</p>
                <div class="coach-type-label">
                  <span v-for="(item, index) in coachTypeList" :key="index">{{
                    teachTypeFilter(item)
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="coach-teaching-info pd15">
            <div class="coach-teaching-item">
              <div class="label">适用人群</div>
              <div class="value level-label">
                {{ forTheCrowdStr }}
              </div>
            </div>
            <div class="coach-teaching-item">
              <div class="label">授课地址</div>
              <div class="value">
                <span v-for="tag in trainingPlace" :key="tag" class="tag">
                  {{ tag }}
                </span>
              </div>
            </div>
            <div class="coach-teaching-item">
              <div class="label">授课时间</div>
              <div class="value">{{ teachingTime }}</div>
            </div>
            <div class="coach-teaching-item">
              <div class="label">所在地区</div>
              <div class="value">{{ coach.location || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 荣耀证书 -->
        <div v-if="coach.certificateImagesArray" class="block-container mt10">
          <div class="title">荣誉证书</div>
          <div class="content">
            <image-preview-wrapper multiple>
              <ul class="certificate-ul">
                <li
                  v-for="item in coach.certificateImagesArray"
                  :key="item.url"
                  class="certificate"
                >
                  <van-image width="1.22rem" height="0.92rem" :src="ossURL + '/' + item.url" />
                  <div class="name">{{ item.name }}</div>
                </li>
              </ul>
            </image-preview-wrapper>
          </div>
        </div>

        <div class="block-container mt10" v-if="coach.teachAchievement">
          <div class="title">教学成就</div>
          <div class="content" v-html="teachAchievement" />
        </div>

        <div class="block-container mt10 train-info">
          <div class="title">教学说明</div>
          <div class="content">
            <template v-if="coach.content">
              <!-- <unpack> -->
              <image-preview-wrapper>
                <div class="rich-text" v-html="coach.content" />
              </image-preview-wrapper>
              <!-- </unpack> -->
            </template>
            <template v-else>
              <van-empty description="暂无内容">
                <template #image>
                  <img src="../../assets/images/empty.png" alt="empty" />
                </template>
              </van-empty>
            </template>
          </div>
        </div>

        <div v-if="!isPreviewMode" class="block-container evaluate-box mt10">
          <div class="title">全部评论({{ commentCountStr }})</div>
          <div class="content">
            <comment-list :coach-id="coachId" ref="commentRef" :max="10" :page-size="10" />
            <button class="more-comment-btn" v-if="moreCommentBtnShow" @click="lookAllComment">
              查看全部评论<van-icon name="arrow" />
            </button>
          </div>
        </div>

        <div class="mt10">
          <ArticleInfo :articleList="userArticle" :videoList="userVideo" :trainList="userTrain" />
        </div>

        <ijl-goods-action v-if="!isPreviewMode">
          <ijl-goods-action-icon text="分享" @click="showPoster">
            <i class="icon icon-share"></i>
          </ijl-goods-action-icon>
          <ijl-goods-action-icon :text="'评论' + commentCountStr" @click="lookAllComment">
            <i class="icon icon-comment"></i>
          </ijl-goods-action-icon>
          <ijl-goods-action-button
            text="联系教练"
            backgroundColor="#fff"
            color="#FF9B26"
            borderColor="#FF9B26"
            @click="connection"
          />
          <ijl-goods-action-button text="立即购课" @click="showCourseSku = true" />
        </ijl-goods-action>

        <poster-popup v-model:show="posterShow" :coach-id="coachId" />

        <course-sku
          v-if="!isPreviewMode"
          v-model:show="showCourseSku"
          :coach-id="coachId"
          @change="onSkuChange"
          :defaultGoods="goods"
          :defaultImg="coach.coachImagesArray?.[0]"
        />

        <!-- all comment 弹窗 -->
        <comment-action-sheet
          v-model="allCommentShow"
          :coach-id="coachId"
          :commentCount="commentCount"
          @replySuccess="getCoachCommentCount"
        />
      </div>
      <details-empty v-if="emptyShow" />
    </template>
  </page>
</template>

<script>
  export default { name: 'coachDetails' }
</script>

<script setup>
  import { computed, ref, onBeforeUnmount } from 'vue'
  import { useRoute } from 'vue-router'
  import { baseURL, ossURL } from '@/config'
  import Tags from '@/components/tags'
  import ArticleInfo from '../common/components/ArticleInfo'
  import ImagePreviewWrapper from '@/components/image-preview-wrapper'
  import GoodsSwipe from '@/views/common/components/goodsSwipe'
  import GM from '@/components/gm-popup'
  import { isLogin, ossURLJoin, toLogin } from '@/common'
  import { Toast } from 'vant'
  import setWxShare from '@/utils/weChat/share'
  import { validate } from '@/utils/validate'
  import PosterPopup from './poster-popup'
  import CourseSku from './components/course-sku.vue'
  import DetailsEmpty from '@/views/common/components/details-empty'
  import CommentList from './components/comment/comment-list'
  import CommentActionSheet from './components/comment/comment-action-sheet'
  import { getUserArticles, getTrainsByUserId, getVideosByUserId } from '@/api/generic-server'
  import { reqUserIsBuyCoachGoods } from '@/api/trade-server'
  import {
    checkCoachIsCollection,
    addCollection,
    cancelCollection,
    getCoachCollectionCount,
    getCoachDetails,
    getCoachNewestCheckRecord,
  } from '@/api/coach-server'

  import { reqGetCoachCommentCount } from '@/api/user-server'

  const route = useRoute()
  const coachId = route.params.id
  const coach = ref(null)
  const posterShow = ref(false)
  const userArticle = ref([])
  const userVideo = ref([])
  const userTrain = ref([])
  const pageShow = ref(false)
  const emptyShow = ref(false)
  let gm = null
  const isUserBuy = ref(false)
  let showCourseSku = ref(false)
  const goods = ref({
    imgUrl: '',
    price: 0,
    virtualPrice: 0,
    isPackagePrice: false,
  })
  const fieldValue = ref('请选择授课方式/课时包')
  const allCommentShow = ref(false)
  const commentRef = ref(null)
  const commentCount = ref(0)
  const coachMobile = ref(null)

  const commentCountStr = computed(() => {
    if (commentCount.value > 999) {
      return '999+'
    } else {
      return commentCount.value
    }
  })

  const moreCommentBtnShow = computed(() => {
    return commentCount.value > 4
  })

  const isPreviewMode = computed(() => {
    return coachId === 'preview'
  })

  const teachAchievement = computed(() => {
    if (!coach.value.teachAchievement) return ''

    if (typeof coach.value.teachAchievement === 'string') {
      let htmlStr = coach.value.teachAchievement
      htmlStr = htmlStr.replace(/\n/g, '<br/>')
      return htmlStr
    }

    return ''
  })

  const tagList = computed(() => {
    let tags = []
    if (coach.value) {
      if (coach.value.teachYear) {
        tags.push(coach.value.teachYear + '年经验')
      }
      // 拼接地区标签
      let areaList = coach.value.areaList
      let areaTagText = ''
      if (Array.isArray(areaList) && areaList.length >= 2) {
        if (areaList.length >= 3) {
          areaTagText = areaList[1].name + areaList[2].name
        } else {
          areaTagText = areaList[0].name + areaList[1].name
        }
        tags.push(areaTagText)
      }

      if (Array.isArray(coach.value.levelList) && coach.value.levelList[2]) {
        tags.push(coach.value.levelList[2].name)
      }

      tags.push(coach.value.coachIdentity === 1 ? '专职教练' : '兼职教练')

      if (!(coach.value.sex === null)) {
        tags.push(coach.value.sex === 1 ? '男' : '女')
      }

      if (coach.value.age) {
        tags.push(coach.value.age + '岁')
      }
    }
    return tags
  })

  const swipes = computed(() => {
    if (!coach.value) return []

    let swipes = []

    if (coach.value.coachVideos) {
      let videoUrl = ''
      if (validate('networkPath', coach.value.coachVideos)) {
        videoUrl = coach.value.coachVideos
      } else {
        videoUrl = ossURLJoin(coach.value.coachVideos)
      }

      swipes.push({
        url: videoUrl,
        type: 'video',
      })
    }

    coach.value.coachImagesArray &&
      coach.value.coachImagesArray.forEach((url) => {
        swipes.push({
          url: ossURLJoin(url),
          type: 'image',
        })
      })

    return swipes
  })

  const teachTypeFilter = computed(() => {
    return (state) => {
      const obj = {
        1: '私教1对1',
        2: '小班1对2',
        3: '小班1对4',
        4: '亲子班',
      }
      return obj[state]
    }
  })

  // 培训方式
  // const teachingMethods = computed(() => {
  //   if (!coach.value) return;
  //   let data = {
  //     1: "一对一",
  //     2: "6人内",
  //     3: "6-20人",
  //   };
  //   return data[coach.value.teachingWay] || "-";
  // });

  // 培训场所
  const trainingPlace = computed(() => {
    if (!coach.value) return []

    let data = {
      1: '上门服务',
      2: '教练指定',
      3: '双方协商',
    }

    let strArray = []
    const tags = []

    if (typeof coach.value.trainPlace === 'string') {
      strArray = coach.value.trainPlace.split('')
    }

    strArray.forEach((item) => tags.push(data[item]))

    return tags
  })

  const teachingTime = computed(() => {
    if (!coach.value) return ''
    if (coach.value.teachingTimeRange.length > 0 && coach.value.teachingTimeRange[0] === null)
      return ''
    let dayStr = coach.value.teachingWeekList.join('/')
    let timeStr = ''
    if (Array.isArray(coach.value.teachingTimeRange) && coach.value.teachingTimeRange.length >= 2) {
      timeStr = coach.value.teachingTimeRange.join('-')
    }
    return dayStr + ' ' + timeStr
  })

  const connection = () => {
    if (isLogin()) {
      if (isUserBuy.value) {
        window.location.href = 'tel:' + coachMobile.value
      } else {
        gm = GM.open({
          title: '联系教练',
        })
      }
    } else {
      toLogin()
    }
  }

  onBeforeUnmount(() => {
    gm && gm.clear()
  })

  // 设置微信分享
  const setShare = (data) => {
    let imgUrl = ossURL + '/h5-assets/logo.png' // 默认logo图

    if (Array.isArray(data.coachImagesArray) && data.coachImagesArray.length > 0) {
      imgUrl = ossURLJoin(data.coachImagesArray[0])
    }

    setWxShare({
      title: '【爱教练】' + data.realName + '-' + data.teachTitle,
      desc: data.teachDescription,
      link: baseURL + '/coach/details/' + coachId,
      imgUrl: imgUrl,
    })
  }

  // 是否收藏
  const isMark = ref(false)

  const toMark = () => {
    if (!isLogin()) {
      toLogin()
      return
    }

    if (!isMark.value) {
      addCollection({ coachId: coachId }).then((res) => {
        isMark.value = !isMark.value
        collectionCount.value++
        Toast.success('收藏成功')
      })
    } else {
      cancelCollection({ coachId: coachId }).then((res) => {
        isMark.value = !isMark.value
        collectionCount.value--
        Toast.success('取消收藏')
      })
    }
  }

  const coachTypeList = ref([]) //授课方式
  const collectionCount = ref(null)

  const forTheCrowdStr = computed(() => {
    if (!coach.value && !coach.value.forTheCrowd) return '-'
    const arr = coach.value.forTheCrowd.split(',')
    const data = { 1: '初阶', 2: '中阶', 3: '高阶', 4: '体考' }
    return arr.map((item) => data[item]).join('/')
  })

  const getCoachCommentCount = () => {
    // 获取评论总数
    reqGetCoachCommentCount({ coachId }).then((res) => {
      commentCount.value = res.data
    })
  }

  const initialize = async () => {
    // 如果为预览模式, 则调用预览的接口
    if (isPreviewMode.value) {
      let { data } = await getCoachNewestCheckRecord()
      coach.value = data
      pageShow.value = true
      return
    }

    let { data } = await getCoachDetails({ coachId: coachId })
    coach.value = data

    // 设置页面title keywords description
    document.title = coach.value.teachTitle + ',爱教练私教网'
    document
      .querySelector('meta[name="keywords"]')
      .setAttribute('content', coach.value.teachTitle + ',爱教练私教网')
    document
      .querySelector('meta[name="description"]')
      .setAttribute(
        'content',
        coach.value.teachTitle +
          ',' +
          coach.value.realName +
          ',' +
          (coach.value.sex === 1 ? '男' : '女') +
          ',' +
          coach.value.teachDescription,
      )
    // 设置sku展示
    if (coach.value.coachFeesSetVOList?.length !== 0) {
      let spec = coach.value.coachFeesSetVOList[0]
      goods.value = {
        imgUrl: spec.feesImage ? spec.feesImage : coach.value.coachImagesArray?.[0],
        price: spec.feesMoney,
        virtualPrice: spec.feesVirtualMoney,
        isPackagePrice: false,
      }
    }

    // 禁用不展示
    if (data.isAllow) {
      pageShow.value = true
    } else {
      emptyShow.value = true
    }

    if (isLogin()) {
      // 是否收藏
      const collectState = await checkCoachIsCollection({ coachId: coachId })
      if (collectState.code === 0) {
        isMark.value = collectState.data
      }
      // 收藏数量
      getCoachCollectionCount({ coachId: coachId }).then((res) => {
        collectionCount.value = res.data
      })

      // 获取用户是否购买过该教练的课程
      reqUserIsBuyCoachGoods({ coachId: coachId }).then((res) => {
        const { data } = res
        isUserBuy.value = data.isBuy
        coachMobile.value = data.contactMobile
      })
    }

    getCoachCommentCount()

    // 获取文章列表
    let article = await getUserArticles({ userId: coach.value.userId, identityType: 'coach' })
    let video = await getVideosByUserId({ userId: coach.value.userId, identityType: 'coach' })
    let train = await getTrainsByUserId({ userId: coach.value.userId, identityType: 'coach' })
    // console.log(article.data, "article");

    if (Array.isArray(article.data)) {
      userArticle.value = article.data
    }

    if (Array.isArray(video.data.records)) {
      userVideo.value = video.data.records
    }
    if (Array.isArray(train.data.records)) {
      userTrain.value = train.data.records
    }
    coachTypeList.value = coach.value.coachFeesSetVOList.map((item) => item.feesType)

    const isShowSku = route.query.isShowSku
    if (isShowSku) {
      showCourseSku.value = true
    }
    setShare(data)
  }

  const onSkuChange = (selectFieldValue, selectGoods) => {
    fieldValue.value = selectFieldValue
    goods.value = selectGoods
  }

  const showPoster = () => {
    if (isLogin()) {
      posterShow.value = true
    } else {
      toLogin()
    }
  }

  const lookAllComment = () => {
    allCommentShow.value = true
  }

  initialize()
</script>

<style lang="scss" scoped>
  @import '~@/styles/mixins/mixins.scss';
  @import '~@/styles/block-container.scss';
  @include Icon('mark', 0.18rem, 0.18rem);
  @include Icon('unmark', 0.18rem, 0.18rem);
  @include Icon('eye2', 0.16rem, 0.12rem);
  @include Icon('share', 0.2rem, 0.2rem);
  @include Icon('comment', 0.2rem, 0.2rem);
  @include Icon('hot', 0.34rem, 0.18rem) {
    margin-left: 0.05rem;
  }

  .coach {
    padding-bottom: 1rem;
  }

  .pd15 {
    padding: 0 0.15rem;
  }
  .mt10 {
    margin-top: 0.1rem;
  }
  .align-items-c {
    align-items: center;
  }
  .train-info {
    padding-bottom: 0.3rem;
  }
  .swipe .van-swipe-item {
    height: 3.15rem;
    background-color: #39a9ed;
  }

  .flex {
    display: flex;
  }

  .swipe {
    :deep(.van-swipe__indicators) {
      bottom: 0.26rem;
    }

    :deep(.van-swipe__indicator) {
      width: 0.04rem;
      height: 0.04rem;
      margin-right: 0.06rem;
    }

    :deep(.van-swipe__indicator--active) {
      width: 0.2rem;
      height: 0.04rem;
      background: #ff9b26;
      border-radius: 0.02rem;
    }
  }

  .coach-details {
    position: relative;
    // top: -0.1rem;
    margin-top: -0.1rem;
    z-index: 2;
    padding: 0.15rem 0;
    background: #fff;
    border-radius: 0.16rem 0.16rem 0 0;
    .mark-box {
      display: flex;
      font-size: 0.14rem;
      color: #ff9b26;
      i {
        margin-right: 0.05rem;
      }
    }
  }

  .coach-desc {
    font-size: 0.14rem;
    color: #616568;
    margin-top: 0.04rem;
  }

  .coach-name {
    display: flex;
    align-items: center;
    .name {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 0.18rem;
      font-weight: 600;
      color: #1f1f1f;
    }
  }

  .coach-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.05rem;
    font-size: 0.14rem;
    color: #616568;
    .coach-title-name {
      opacity: 0.8;
    }
    .pageviews {
      display: flex;
      padding-top: 0.05rem;
      .num {
        font-size: 0.12rem;
        margin-left: 0.06rem;
        color: #b2b1b7;
        vertical-align: top;
      }
    }
  }

  .coach-tags {
    margin-top: 0.1rem;
  }

  .coach-teaching-info {
    margin-top: 0.17rem;
    .coach-teaching-item {
      display: flex;
      // align-items: center;
      font-size: 0.14rem;
      margin-bottom: 0.06rem;

      .label {
        width: 0.8rem;
        color: #616568;
      }

      .value {
        flex: 1;
        color: #1a1b1d;
      }

      .price {
        font-size: 0.16rem;
        font-weight: bold;
        color: #ff6445;
        letter-spacing: 0.5px;
        .Scribing {
          margin-left: 0.06rem;
          font-size: 0.12rem;
          color: #b2b1b7;
          font-weight: 400;
          text-decoration: line-through;
        }
      }

      .f18 {
        font-size: 0.18rem;
      }

      .f12 {
        font-size: 0.12rem;
      }

      .rmb {
        font-size: 0.12rem;
        display: inline-block;
        margin-right: 0.02rem;
      }

      .tag {
        display: inline-block;
        line-height: 0.18rem;
        background: #ff6445;
        padding: 0 0.03rem;
        border-radius: 0.02rem;
        color: #fff;
        font-size: 0.13rem;
        margin-right: 0.06rem;
      }
    }
  }
  .coach-type {
    border-top: 0.1rem solid #f7f7f7;
    border-bottom: 0.1rem solid #f7f7f7;
    padding: 0.15rem;

    .coach-type-label {
      margin-top: 0.1rem;
      display: flex;
      flex-wrap: wrap;
      span {
        margin-right: 0.06rem;
        font-size: 0.12rem;
        color: #ff9b26;
        padding: 0.02rem 0.06rem;
        background: #fff5e9;
        border-radius: 0.02rem;
        &:nth-child(4n) {
          margin-right: 0;
        }
      }
    }
  }

  .certificate-ul {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;

    .certificate {
      width: 1.22rem;
      display: inline-block;
      vertical-align: top;
      margin-right: 0.1rem;

      .name {
        width: 100%;
        font-size: 0.12rem;
        text-align: center;
        white-space: break-spaces;
        word-break: break-all;
      }
    }
  }

  .article-item {
    padding: 0.07rem 0.1rem 0.05rem 0.1rem;
    margin-bottom: 0.08rem;
    box-shadow: 0 0 0.02rem 0 rgba(0, 0, 0, 0.12);
  }

  :deep(.rich-text) {
    img {
      width: 100% !important;
      height: 100% !important;
    }

    p {
      font-size: 0.14rem !important;
    }
  }
  :deep(.van-empty__image) {
    width: 1rem;
    height: 1rem;
  }
  .evaluate-item {
    padding: 0.15rem;
    border-bottom: 0.01rem solid #eee;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .header-l {
        display: flex;
        align-items: center;
        img {
          margin-right: 0.1rem;
          width: 0.32rem;
          height: 0.32rem;
          border-radius: 50%;
          background-color: #eee;
        }
        p {
          font-size: 0.14rem;
          color: #1a1b1d;
        }
      }
      .header-r {
        font-size: 0.12rem;
        color: #b2b1b7;
      }
    }
    .details {
      padding-left: 0.42rem;
      margin-top: 0.06rem;
      font-size: 0.14rem;
      color: #616568;
    }
    .img-box {
      margin-top: 0.08rem;
      padding-left: 0.42rem;
      img {
        margin-right: 0.1rem;
        width: 0.72rem;
        height: 0.72rem;
      }
    }
  }
  .evaluate-box {
    padding: 0;
    .title {
      padding: 0 0.15rem;
      border-bottom: 0.01rem solid #eee;
    }
  }

  .more-comment-btn {
    width: 100%;
    padding: 0.1rem 0;
    text-align: center;
    font-size: 0.13rem;
    color: #606266;
    border-top: 1px solid #f3f3f3;
    margin-top: -0.1rem;
    background: #fff;
    cursor: pointer;
  }
</style>
