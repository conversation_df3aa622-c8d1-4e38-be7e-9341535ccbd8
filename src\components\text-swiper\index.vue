<template>
  <div>
    <div class="remind-info">
      <i class="icon icon-horn" />
      <div :class="{ anim: isRoll === true, 'info-box': true }">
        <p v-for="(item, index) in swiperList" :key="index">
          {{ item }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  })
  const isRoll = ref(false)
  const swiperList = ref([])
  swiperList.value = props.list.map((item) => item)

  onMounted(() => {
    setInterval(scroll, 3000)
  })
  // 文字轮播
  const scroll = () => {
    isRoll.value = true
    setTimeout(() => {
      swiperList.value.push(swiperList.value[0])
      swiperList.value.shift()
      isRoll.value = false
    }, 1000)
  }
</script>

<style scoped lang="scss">
  @import '~@/styles/mixins/mixins';

  @include Icon('horn', 0.13rem, 0.13rem);
  .remind-info {
    position: absolute;
    z-index: 2002;
    top: 0.5rem;
    left: 3%;
    display: flex;
    align-items: center;
    width: 2.3rem;
    height: 0.17rem;
    font-size: 0.12rem;
    color: #000000;
    line-height: 0.17rem;
    background-color: rgba(255, 255, 255, 0.75);
    border-radius: 0.1rem;
    overflow: hidden;
    i {
      margin: 0 0.06rem;
    }
  }
  .anim {
    transition: all 0.5s;
    margin-top: -0.34rem;
  }
  .info-box {
    padding-top: 0.18rem;
  }
</style>
