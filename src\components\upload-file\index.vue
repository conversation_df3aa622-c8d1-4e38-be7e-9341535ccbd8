<template>
  <Uploader
    v-bind="attrs"
    ref="uploaderRef"
    :max-size="maxSize"
    :before-read="onBeforeRead"
    :after-read="afterRead"
    @oversize="onOversize"
  >
    <template #preview-cover="previewProps">
      <slot name="preview-cover" v-bind="previewProps" />
    </template>
    <!-- 通过便利实现插槽透传 -->
    <template v-for="(item, key, index) in $slots" :key="index" v-slot:[key]>
      <slot :name="key"></slot>
    </template>
  </Uploader>
</template>

<script setup>
  import { ref, useAttrs } from 'vue'
  import { Uploader, Toast } from 'vant'
  import { uploadFile } from '@/api/generic-server'
  import { byteConvert } from '@/utils'

  const props = defineProps({
    maxSize: Number,
    fileType: {
      type: String,
      default: '',
    },
    notLogin: {
      type: Boolean,
      default: false,
    },
  })

  const attrs = useAttrs()
  const uploaderRef = ref('')

  const onBeforeRead = (file) => {
    if (props.fileType && typeof props.fileType === 'string') {
      const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      const specifyFormat = props.fileType.split('|')
      const isMeet = specifyFormat.includes(fileType) // 是否符合规定文件格式

      if (!isMeet) {
        Toast('只支持上传格式为 ' + specifyFormat.join('，') + ' 的文件')
        return false
      }
    }

    return true
  }

  const afterRead = (fileOv) => {
    fileOv.status = 'uploading'
    fileOv.message = '上传中...'

    let formData = new FormData()
    formData.append('file', fileOv.file)
    uploadFile(formData, props.notLogin)
      .then((res) => {
        let { data, code } = res
        if (code === 0) {
          fileOv.path = data //存放相对路径
          fileOv.status = 'done'
        }
      })
      .catch(() => {
        fileOv.status = 'failed'
        fileOv.message = '上传失败'
      })
  }

  const onOversize = () => {
    Toast('文件大小不能超过 ' + byteConvert(props.maxSize))
  }

  const chooseFile = () => {
    uploaderRef.value.chooseFile()
  }

  const closeImagePreview = () => {
    uploaderRef.value.closeImagePreview()
  }

  defineExpose({
    chooseFile,
    closeImagePreview,
  })
</script>
