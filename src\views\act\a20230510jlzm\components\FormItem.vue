<template>
  <div class="form-item" :class="{ feedback: isLink }">
    <slot>
      <div class="form-item-label" :class="{ 'form-item-label-required': required }">
        {{ label }}
      </div>
      <div class="form-item-input">
        <input
          :readonly="isLink"
          :value="modelValue"
          @input="$emit('update:modelValue', $event.target.value)"
          :placeholder="placeholder"
          :maxlength="maxLength"
          :type="type"
        />
      </div>
      <div v-if="isLink || $slots.right" class="form-item-right-icon">
        <slot name="right">
          <i class="icon-arrow" />
        </slot>
      </div>
    </slot>
  </div>
</template>

<script setup>
  // import { computed } from "vue";

  defineProps({
    modelValue: [String, Number],

    label: String,

    placeholder: String,

    required: {
      type: Boolean,
      default: false,
    },

    isLink: {
      type: Boolean,
      default: false,
    },

    maxLength: {
      type: [Number, String],
      default: null,
    },

    type: {
      tyep: String,
      default: '',
    },
  })

  defineEmits(['update:modelValue'])

  // const
</script>

<style lang="scss" scoped>
  .form-item {
    display: flex;
    align-items: center;
    padding: 0.17rem 0.13rem 0.17rem 0;
    border-bottom: 1px solid #eeeeee;
    height: 0.54rem;
  }

  .form-item-label {
    width: 0.86rem;
    color: #616568;
    user-select: none;
    position: relative;
    padding-left: 0.1rem;
  }

  .form-item-label-required {
    &:before {
      color: #ff6445;
      content: '*';
      font-size: 0.16rem;
      margin-right: 0.02rem;
      position: absolute;
      left: 0;
    }
  }

  .form-item-input {
    flex: 1;
    input {
      width: 100%;
      border: none;
      font-size: 0.14rem;
      background: transparent;

      &::placeholder {
        color: #b2b1b7;
      }
    }
  }

  .form-item-right-icon {
    .icon-arrow {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      background: url('../images/icon-arrow.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
