<template>
  <div class="comment-box">
    <van-overlay z-index="2050" :show="inputShow" @click="inputShow = false" />
    <div class="make-comment" v-show="inputShow">
      <div class="input-wrapper">
        <van-field
          ref="inputRef"
          class="textarea"
          v-model="message"
          type="textarea"
          maxlength="500"
          :placeholder="placeholder"
          :border="false"
          @blur="onInputBlur"
        />
        <div class="action-wrapper">
          <!-- mousedown.capture.prevent.stop 是为了阻止发送按钮 点击 失去 input 焦点 -->
          <button
            class="submit"
            :disabled="disabled"
            v-preventReClick
            @mousedown.capture.prevent.stop="onSubmit"
          >
            发布
          </button>
          <div class="word-limit">{{ count }}/500</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { nextTick, ref, computed } from 'vue'
  import { validate } from '@/utils/validate'

  defineProps({
    placeholder: {
      type: String,
      default: '写下你想说的或想了解的…',
    },
  })

  const emit = defineEmits(['submit'])

  const message = ref('')
  const inputShow = ref(false)
  const inputRef = ref(null)

  const count = computed(() => {
    if (typeof message.value === 'string') {
      return [...message.value].length
    }
    return 0
  })

  const disabled = computed(() => {
    return validate('empty', message.value)
  })

  const showInput = () => {
    inputShow.value = true
    nextTick(() => {
      inputRef.value.focus()
    })
  }

  const hideInput = () => {
    inputShow.value = false
  }

  const resetInput = () => {
    message.value = ''
  }

  const onInputBlur = () => {
    inputShow.value = false
  }

  const onSubmit = () => {
    emit('submit', message.value)
  }

  defineExpose({
    open: showInput,
    close: hideInput,
    reset: resetInput,
  })
</script>

<style lang="scss" scoped>
  .make-comment {
    background: #ffffff;
    box-shadow: 0 -0.02rem 0.04rem 0 rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: var(--window-left);
    right: var(--window-right);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    user-select: none;
    z-index: 2100;

    .placeholder {
      display: flex;
      height: 0.5rem;
      padding: 0.08rem 0.15rem;

      .fake-input {
        flex: 1;
        height: 0.32rem;
        padding: 0.06rem 0.15rem;
        background: #f7f7f7;
        border-radius: 0.16rem;
        border: 1px solid #eeeeee;
        color: #b2b1b7;
      }
    }

    .input-wrapper {
      padding-top: 0.15rem;

      :deep(.textarea) {
        padding: 0 0.15rem;

        textarea {
          height: 0.8rem;
          font-size: 0.14rem;
          color: #616568;
          background: #f2f3f5;
          padding: 0.1rem;
          border-radius: 0.04rem;
        }
      }

      .action-wrapper {
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        padding: 0.1rem 0.15rem;

        .word-limit {
          font-size: 0.12rem;
          color: #646566;
          margin-right: 0.1rem;
          user-select: none;
        }

        .submit {
          padding: 0.06rem 0.1rem;
          background: #ff9b26;
          font-size: 0.13rem;
          color: #fff;
          border-radius: 0.23rem;
          cursor: pointer;

          &:disabled {
            cursor: not-allowed;
            opacity: 0.5;
          }
        }
      }
    }
  }
</style>
